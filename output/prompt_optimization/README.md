# Prompt Optimization Version Control

这个目录用于管理直播话术生成的prompt优化版本控制。

## 目录结构

```
prompt_optimization/
├── README.md                    # 本文件，说明版本控制系统
├── v1.0/                       # 第一版优化测试
│   ├── test_data.csv           # 测试数据（每个国家10条）
│   ├── results.csv             # 测试结果
│   ├── results.json            # 详细结果（JSON格式）
│   ├── analysis_report.txt     # 分析报告
│   └── batches/               # 批处理进度文件
├── v1.1/                       # 第二版优化测试
│   └── ...
└── v2.0/                       # 主要版本更新
    └── ...
```

## 版本命名规则

- **主版本号 (X.0)**: 重大prompt结构变更
- **次版本号 (X.Y)**: 小幅优化和调整
- **修订版本号 (X.Y.Z)**: 错误修复和微调

## 测试流程

1. 创建新版本目录
2. 准备测试数据（每个国家10条记录）
3. 运行批处理测试脚本
4. 分析结果质量
5. 记录改进建议
6. 准备下一版本优化

## 当前版本

- **v1.0**: 基础prompt优化版本
  - 改进了指令清晰度
  - 增加了更好的示例
  - 优化了输出格式要求

## 测试数据分布

每个版本的测试数据包含以下国家，每个国家10条记录：
- ID (印尼)
- VN (越南) 
- MY (马来西亚)
- TH (泰国)
- PH (菲律宾)
- SG (新加坡)

总计约60条测试记录用于每个版本的评估。
