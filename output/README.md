# Output 文件夹结构说明

本文件夹包含所有脚本生成的输出文件，按功能和类型进行了组织。

## 📁 文件夹结构

```
output/
├── README.md                           # 本说明文件
├── live_highlights/                    # 直播高亮脚本相关文件
│   ├── batches/                       # 批量保存的中间结果
│   │   ├── live_highlight_scripts_batch_1.csv
│   │   ├── live_highlight_scripts_batch_1.json
│   │   └── ...
│   ├── live_highlight_scripts.csv     # 最终结果（紧凑格式）
│   ├── live_highlight_scripts.json    # 最终结果（JSON格式）
│   ├── live_highlight_scripts_expanded.csv  # 展开格式（推荐）
│   ├── live_highlight_scripts_summary_report.txt  # 汇总报告
│   └── live_highlight_checkpoint.json # 断点续传文件（临时）
├── product_scripts/                   # 产品脚本相关文件
│   ├── product_demo_scripts.csv       # 产品脚本（紧凑格式）
│   ├── product_demo_scripts.json      # 产品脚本（JSON格式）
│   ├── product_demo_scripts_expanded.csv  # 展开格式（推荐）
│   └── product_scripts_summary_report.txt  # 汇总报告
├── legacy/                           # 历史文件和旧版本输出
│   ├── live_scripts_sea.csv
│   ├── scripts_generation*.csv
│   └── ...
├── product_demo.csv                  # 原始产品演示数据
├── test.json                         # 测试文件
├── test1.json                        # 测试文件
└── test_data_analysis.json           # 数据分析测试文件
```

## 📋 文件说明

### 直播高亮脚本 (live_highlights/)

- **主要输出文件**:
  - `live_highlight_scripts_expanded.csv` - **推荐使用**，每个脚本一行，易于阅读和分析
  - `live_highlight_scripts.csv` - 紧凑格式，每个产品一行
  - `live_highlight_scripts.json` - JSON 格式，便于程序处理

- **批量保存文件** (batches/):
  - 每50条记录自动保存的中间结果
  - 防止长时间处理时数据丢失
  - 支持断点续传功能

- **报告文件**:
  - `live_highlight_scripts_summary_report.txt` - 详细的统计报告

### 产品脚本 (product_scripts/)

- **主要输出文件**:
  - `product_demo_scripts_expanded.csv` - **推荐使用**，展开格式
  - `product_demo_scripts.csv` - 紧凑格式
  - `product_demo_scripts.json` - JSON 格式

- **报告文件**:
  - `product_scripts_summary_report.txt` - 统计报告

### 历史文件 (legacy/)

包含旧版本的脚本输出和历史数据，保留用于参考。

## 🔧 使用建议

1. **查看结果**: 优先使用 `*_expanded.csv` 文件，格式清晰易读
2. **程序处理**: 使用 `.json` 文件进行程序化处理
3. **统计分析**: 查看 `*_summary_report.txt` 了解生成统计
4. **断点续传**: 脚本会自动处理 `checkpoint.json` 文件
5. **批量文件**: 通常不需要手动查看 `batches/` 文件夹内容

## 📊 文件格式说明

### 展开格式 CSV (推荐)

```csv
highlight_vid,product_id,product_name,category,country_code,script_title,script_content,total_tokens,prompt_tokens,completion_tokens
v09b4dg40024...,1730498933...,Product Name,Beauty & Personal Care,ID,Script Title,Script content here...,2877,2466,411
```

### 紧凑格式 CSV

```csv
highlight_vid,product_id,product_name,category,country_code,scripts,total_tokens,prompt_tokens,completion_tokens
v09b4dg40024...,1730498933...,Product Name,Beauty & Personal Care,ID,"{'scripts': [...]}",...
```

## 🚀 相关脚本

- `scripts/live_highlight_script_gpt.py` - 生成直播高亮脚本
- `scripts/expand_highlight_scripts.py` - 展开脚本格式
- `scripts/live_script_sea_gpt.py` - 生成产品脚本
- `scripts/expand_product_scripts.py` - 展开产品脚本格式

---

*最后更新: 2025-06-19*
