{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "pd.DataFrame.iteritems = pd.DataFrame.items\n", "import json\n", "import openai\n", "from tqdm import tqdm\n", "import re\n", "\n", "\n", "class OpenAIAgent:\n", "    def __init__(self, api_key, model=\"gpt-4-0613\", end_point = 'https://search-va.byteintl.net/gpt/openapi/online/v2/crawl'):\n", "        self.client = openai.AzureOpenAI(\n", "            azure_endpoint=end_point,\n", "            api_version=\"2024-03-01-preview\",\n", "            api_key=api_key\n", "        )\n", "        self.model = model\n", "\n", "    def system_message(self, message):\n", "        return message\n", "\n", "    def assistant_message(self, message):\n", "        return message\n", "\n", "    def user_message(self, message):\n", "        return message\n", "\n", "    @staticmethod\n", "    def clean_json_output(output):\n", "        output = output.strip()\n", "        if output.startswith(\"```json\"):\n", "            output = output[7:]\n", "        if output.endswith(\"```\"):\n", "            output = output[:-3]\n", "        cleaned_output = output.strip()\n", "\n", "        try:\n", "            json_data = json.loads(cleaned_output)\n", "        except json.JSONDecodeError as e:\n", "            print(f\"JSON decoding error: {e}\")\n", "            return cleaned_output\n", "\n", "        def clean_json(data):\n", "            if isinstance(data, dict):\n", "                return {key: clean_json(value) for key, value in data.items()}\n", "            elif isinstance(data, list):\n", "                return [clean_json(item) for item in data]\n", "            elif isinstance(data, str):\n", "                return \"\" if data.lower() in [\"unknown\", \"na\", \"null\"] else data\n", "            else:\n", "                return data\n", "\n", "        cleaned_json_data = clean_json(json_data)\n", "        return cleaned_json_data\n", "\n", "    def run_openai_task(self, system_message, user_message):\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": self.system_message(system_message)},\n", "            {\"role\": \"user\", \"content\": self.user_message(user_message)}\n", "        ]\n", "\n", "        completion = self.client.chat.completions.create(\n", "            extra_headers={\"X-TT-LOGID\": \"abc\"},  # 请务必带上此header，方便定位问题\n", "            model=self.model,\n", "            messages=messages,\n", "            temperature=0,\n", "            frequency_penalty=0,\n", "            presence_penalty=0,\n", "            max_tokens=4096# 添加max_tokens参数\n", "        )\n", "\n", "        json_data = completion.model_dump_json()\n", "        data_dict = json.loads(json_data)\n", "\n", "        result = self.clean_json_output(data_dict['choices'][0]['message']['content'])\n", "        usage_data = data_dict.get('usage', {})\n", "        total_tokens = usage_data.get('total_tokens', 0)\n", "        prompt_tokens = usage_data.get('prompt_tokens', 0)\n", "        completion_tokens = usage_data.get('completion_tokens', 0)\n", "\n", "        return result, total_tokens, prompt_tokens, completion_tokens\n", "    \n", "\n", "\n", "    def generate_product_script(self, industry, strategies, product_name, product_description):\n", "        num_scripts = \"15-20\"\n", "        \n", "        system_message = \"You are an assistant for a TikTok seller. Your task is to generate engaging product scripts for live streams based on given strategies.\"\n", "        strategy_explanations = \"\\n\".join([f\"1. **{strategy['name']}**: {strategy['explanation']}\" for strategy in strategies])\n", "        strategy_names = \", \".join([strategy['name'] for strategy in strategies])\n", "        examples = \"\\n\".join([f\"**Examples for {strategy['name']}**: {strategy['examples']}\" for strategy in strategies])\n", "\n", "\n", "        user_message = f\"\"\"\n", "        Industry: {industry}\n", "        Product Description: {product_description}\n", "        Top Strategies: {strategy_names}\n", "        Product Name: {product_name}\n", "\n", "        Please generate {num_scripts} product scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies.\n", "\n", "        Return the output in JSON format like this:\n", "        {{\n", "            \"scripts\": [\n", "                {{\n", "                    \"title\": \"Strategy\",\n", "                    \"text\": \"Script Text\"\n", "                }},\n", "                {{\n", "                    \"title\": \"Strategy\",      \n", "                    \"text\": \"Script Text\"\n", "                }},\n", "                {{\n", "                    \"title\": \"Strategy\",\n", "                    \"text\": \"Script Text\"\n", "                }}\n", "            ]\n", "        }}\n", "        \"\"\"\n", "\n", "        result, total_tokens, prompt_tokens, completion_tokens = self.run_openai_task(system_message, user_message)\n", "        return result, total_tokens, prompt_tokens, completion_tokens\n", "\n", "\n", "def product_generate(prd, industry_strategy_dict, category_industry_dict, agent):\n", "    if not industry_strategy_dict or not category_industry_dict:\n", "        return 0, None\n", "\n", "    prod_desc = prd.product_desc\n", "    # if prod_desc is None or len(prod_desc) < 100:\n", "    #     return 0, None\n", "\n", "    try:\n", "        prod_desc_lines = re.sub('<.+?>', '\\n', prod_desc).split('\\n')\n", "        product_description = '\\n'.join([line for line in prod_desc_lines if len(line) > 0]).strip()\n", "    except Exception as e:\n", "        print(e)\n", "        product_description = \"\"\n", "    product_name = prd.product_name\n", "    category = prd.category\n", "\n", "    industry = category_industry_dict.get(category, category_industry_dict.get('Default'))\n", "\n", "    strategies = industry_strategy_dict.get(industry, industry_strategy_dict['home'])\n", "    # print(industry, strategies)\n", "    if len(strategies) == 0:\n", "        print('strategies are not qualified', prd.product_id)\n", "        return 0, None\n", "\n", "    # summarized_strategies = \"####\".join(strategies)\n", "\n", "    result, total_tokens, prompt_tokens, completion_tokens = agent.generate_product_script(\n", "        industry, strategies, product_name, product_description\n", "    )\n", "    if result is None:\n", "        # 如果第一次运行失败，则再尝试一次\n", "        \n", "        result, total_tokens, prompt_tokens, completion_tokens = agent.generate_product_script(\n", "            industry, strategies, product_name, product_description\n", "        )\n", "    \n", "    if result is None:\n", "        return 0, None\n", "\n", "    return 1, result\n", "\n", "# 示例产品信息类\n", "class ProductInfo:\n", "    def __init__(self, product_name, product_desc, category):\n", "        self.product_name = product_name\n", "        self.product_desc = product_desc\n", "        self.category = category\n", "    \n", "    def __str__(self):\n", "        return f\"ProductInfo(product_name={self.product_name}, product_desc={self.product_desc}, category={self.category})\"\n", "\n", "# 示例使用\n", "\n", "# 初始化代理类对象\n", "# agent = OpenAIAgent(api_key=\"S89U2DurpJcpLyiXiTIqFpgbZCWGYc92\", model='gpt-4o-2024-05-13')\n", "agent = OpenAIAgent(api_key=\"nCUBvtcbgzNQokqFxDQyfnKKAvIlHHVq\", model='gpt-4o-2024-08-06')\n", "# 4o-mini\n", "# agent = OpenAIAgent(api_key=\"cEYjyos0EH8OX9Ejm7YC53wCwTYUbgMa\", model='gpt-4o-mini-2024-07-18', end_point=\"https://gpt-i18n.byteintl.net/gpt/openapi/online/v2/crawl\")\n", "# agent = OpenAIAgent(api_key=\"X3zrNBdXZ1wQS1EnqMXP4bkFBBMELMPB\", model='gpt-4o-mini-2024-07-18')\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Category: beauty\n", "{'industry': 'beauty', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences to build trust and relatability with the audience.', 'examples': '\"i have the black one and i love it\", \"i didn\\'t buy this for gaming. i bought this cause i work from home\", \"i work from home, so watching this all day is very pleasing to me\"'}\n", "{'industry': 'beauty', 'name': 'Demonstrating Product Usage', 'explanation': 'Showing how the product works in real-life scenarios to help the audience visualize its practicality and benefits.', 'examples': '\"i\\'ll put it on and show you what it looks like on my neck\", \"we got the galaxy light projecting onto the ceiling right there\", \"it senses when you\\'re over it and then you do your business, tap, tap, tap, and it closes\"'}\n", "{'industry': 'beauty', 'name': 'Emphasizing Versatility', 'explanation': 'Highlighting how the product can be used in various scenarios to appeal to a broader audience.', 'examples': '\"ideal for a carry on or for just a shorter trip\", \"you can project it onto your wall or like kind of at an angle\", \"do fun food cooking videos\"'}\n", "{'industry': 'beauty', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences to build trust and relate to the audience.', 'examples': '\"my husband uses it every day on his lunch break\", \"i actually don\\'t use it for gaming, but i did buy one for my son\", \"i love this one so much more because i can see my whole outfit\"'}\n", "{'industry': 'beauty', 'name': 'Highlighting Product Features', 'explanation': 'Emphasizing specific features of the product to showcase its benefits.', 'examples': '\"super quiet\", \"it has round retro keys\", \"three different sizes are included the 20 inch the 24 inch and then the 28 inch\"'}\n", "{'industry': 'beauty', 'name': 'Repetition for Emphasis', 'explanation': 'Repeating key points to reinforce the message and ensure it sticks with the audience.', 'examples': '\"look at these tsa locks\", \"they lock up so no one can take your stuff\", \"super important. super important\"'}\n", "{'industry': 'beauty', 'name': 'Demonstration of Product Usage', 'explanation': 'Showing the product in real-time to build trust and interest.', 'examples': '\"i have this called jin jelly wrapping mask on me\", \"let me do it on half of my face\", \"i\\'m gonna hit clean now\"'}\n", "{'industry': 'beauty', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences to build trust and relatability.', 'examples': '\"i am absolutely obsessed with it\", \"i went to korea to try out this device\", \"this does not hurt me at all\"'}\n", "{'industry': 'beauty', 'name': 'Highlighting Unique Selling Points', 'explanation': 'Emphasizing unique features of the product to differentiate it from competitors.', 'examples': '\"it\\'s no needle microneedling\", \"it\\'s a six in one device\", \"ems plus microcurrent\"'}\n", "{'industry': 'beauty', 'name': 'Highlighting Product Benefits', 'explanation': 'Emphasizing the benefits and positive effects of the product.', 'examples': '\"it\\'ll boost the color pigmentation\", \"extra anti aging benefits\", \"super lightweight and yeah it doesn\\'t cause any irritation\"'}\n", "{'industry': 'beauty', 'name': 'Repetition for Emphasis', 'explanation': 'Repeating key phrases to reinforce the message and make it memorable.', 'examples': '\"this is how we\\'re storing our food\", \"grab it, grab it, grab it\", \"really, really good\"'}\n", "{'industry': 'beauty', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences to build trust and relate to the audience.', 'examples': '\"i got it sitting in my house it\\'s so beautiful\", \"my skin is now freaking glossy\", \"i love this product\"'}\n", "{'industry': 'beauty', 'name': 'Descriptive Language', 'explanation': 'Using vivid and appealing descriptions to create a sensory experience for the audience.', 'examples': '\"she is so squishy\", \"perfect tiny kitty little size\", \"really carve out around the eye\"'}\n", "{'industry': 'beauty', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences and satisfaction with the product to build credibility and relatability.', 'examples': '\"this is one of my favorites\", \"i put it on before i put on my makeup\", \"i\\'ve been using these products for seven, almost seven years\"'}\n", "{'industry': 'beauty', 'name': 'Scarcity and Urgency', 'explanation': 'Creating a sense of urgency by emphasizing the limited availability of the product, encouraging quick purchases.', 'examples': '\"limited edition wax\", \"once this is sold out, we are done\", \"once it\\'s gone, it is gone\"'}\n", "{'industry': 'beauty', 'name': 'Descriptive Language', 'explanation': 'Using vivid and sensory-rich descriptions to create a strong mental image.', 'examples': '\"sweet gourmand vanilla\", \"more unisex tobacco\", \"sweet tobacco and a sweet vanilla\"'}\n", "{'industry': 'beauty', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences to build trust and relatability.', 'examples': '\"i have a hair kit i have a lot of beetravers and i put all of them in the storage pouches\", \"my suitcase looks so cute\", \"i love the vanilla one\"'}\n", "{'industry': 'beauty', 'name': 'Demonstration', 'explanation': 'Showing the product in action to illustrate its use and benefits.', 'examples': '\"let me um show you the length of a beach river\", \"so let me show you how it works\", \"grab it. the pink one.\"'}\n", "{'industry': 'beauty', 'name': 'Creating Urgency', 'explanation': 'Emphasizing limited availability to prompt quick purchases.', 'examples': '\"flash sale happening right now\", \"25 left of the rosemary, 25 left\", \"five left\"'}\n", "{'industry': 'beauty', 'name': 'Detailed Product Description', 'explanation': \"Providing thorough descriptions of the product's features and benefits.\", 'examples': '\"caramel cascade is caramel marshmallow and sugar and vanilla\", \"true vanilla creme brulee\", \"the point seven five, this is the point seven five\"'}\n", "{'industry': 'beauty', 'name': 'Repetition for Emphasis', 'explanation': \"Repeating key phrases to reinforce the product's main selling points.\", 'examples': '\"spicy rose\", \"nice spicy, exotic rose\", \"nice good spicy rose\"'}\n", "\n", "\n", "Category: collectibles\n", "{'industry': 'collectibles', 'name': 'Detailed Product Description', 'explanation': 'Providing thorough descriptions of the product, including features and benefits, to inform and attract buyers.', 'examples': '\"comes with two hundred tracks\", \"this book does come with twenty five different activities\", \"behind this butterfly have a round the gym in the middle\"'}\n", "{'industry': 'collectibles', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences and satisfaction with the product to build trust and relatability.', 'examples': '\"i peeled, my whole face literally looked completely shiny and tight\", \"i love it so much\", \"they look so natural like they make me just feel so ready for the day\"'}\n", "{'industry': 'collectibles', 'name': 'Demonstrating Product Usage', 'explanation': 'Showing how to use the product step-by-step to help the audience understand its application and benefits.', 'examples': '\"lock the lashes in place with the seal\", \"i\\'m gonna put one more it\\'s gonna be a size ten right here in the inner corner\", \"you can read it backlight here but you can also turn it off\"'}\n", "{'industry': 'collectibles', 'name': 'Repetition for Emphasis', 'explanation': \"Repeating key points to reinforce the product's benefits and make them more memorable.\", 'examples': '\"cuts through all types of paper\", \"new beads, honey?\", \"pink connor. pink connor.\"'}\n", "{'industry': 'collectibles', 'name': 'Friendly Greeting', 'explanation': 'Starting with a warm and friendly greeting to create a welcoming atmosphere and establish a connection.', 'examples': '\"hello everybody. nice to meet you.\", \"hello friends.\", \"hi, everyone. welcome in.\"'}\n", "{'industry': 'collectibles', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences to build trust and relate to the audience.', 'examples': '\"these are my lawn mowing shoes\", \"i\\'ve had these ones for years\", \"i wear them to mow my lawn\"'}\n", "{'industry': 'collectibles', 'name': 'Creating Urgency', 'explanation': 'Emphasizing limited availability and high demand to prompt immediate action.', 'examples': '\"they are likely to sell out within minutes\", \"I highly recommend that you pick them up now while you can\", \"once they sell out, they\\'re gonna be gone\"'}\n", "{'industry': 'collectibles', 'name': 'Exclusivity', 'explanation': \"Highlighting the product's exclusive availability to make it more desirable.\", 'examples': '\"tik tok shop exclusively\", \"they are only available on the tik tok shop for the next two days\", \"this is a tik tok shop exclusive\"'}\n", "{'industry': 'collectibles', 'name': 'Repetition for Emphasis', 'explanation': 'Repeating key points to ensure the audience remembers the important details.', 'examples': '\"super, super fast\", \"super, super, super popular\", \"absolutely love, love, love them\"'}\n", "{'industry': 'collectibles', 'name': 'Demonstrating Product Use', 'explanation': 'Showing how the product works in real-time to illustrate its features and benefits.', 'examples': '\"i just open this up\", \"let me show you guys over here\", \"i\\'m gonna deposit two dollars\"'}\n", "{'industry': 'collectibles', 'name': 'Highlighting Product Features', 'explanation': \"Detailing the product's features to highlight its benefits and appeal to potential buyers.\", 'examples': '\"notifies you when it\\'s done with cleaning\", \"does a million and one things\", \"more bells and whistles\"'}\n", "{'industry': 'collectibles', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences with the product to build trust and credibility with the audience.', 'examples': '\"i personally like keeping my ice out because it\\'s on our um bar top\", \"i like to have ice here for people to just quickly get when they want\", \"i\\'ve had mine since april\"'}\n", "{'industry': 'collectibles', 'name': 'Descriptive Language', 'explanation': 'Using vivid and appealing descriptions to create a strong visual image and emotional connection with the product.', 'examples': '\"it\\'s cute and it\\'s really squishable\", \"it looks literally like hawaiian bread\", \"so very beautiful\"'}\n", "{'industry': 'collectibles', 'name': 'Personal Engagement', 'explanation': 'Using a conversational tone and personal pronouns to create a sense of connection with the audience.', 'examples': '\"think you\\'re talking about this one\", \"i got to get you a top loader and sleeve for this\", \"so jasmine, if you\\'re interested in the hawaiian bread\"'}\n", "{'industry': 'collectibles', 'name': 'Building Anticipation', 'explanation': 'Creating excitement by teasing upcoming products or events to keep the audience engaged.', 'examples': '\"we will open up a box and see what\\'s inside\", \"i am running this box back you guys tonight if anybody\\'s interested\", \"as soon as we get done with these, then i\\'m doing that one\"'}\n", "{'industry': 'collectibles', 'name': 'Highlighting Unique Product Features', 'explanation': 'Emphasizing unique aspects of the product to make it stand out and attract interest.', 'examples': '\"glow in the dark dining\", \"he is a different kind of dinosaur\", \"he also glows in the dark\"'}\n", "{'industry': 'collectibles', 'name': 'Repetition for Emphasis', 'explanation': 'Repeating key points to ensure the audience remembers important details.', 'examples': '\"ten packs, bro\", \"ten packs? ten packs\", \"one berry, one berry\", \"two berries\", \"one berry\", \"three berries. arcanine, three berries.\", \"brox grit, two berries\"'}\n", "{'industry': 'collectibles', 'name': 'Building Excitement and Anticipation', 'explanation': 'Using enthusiastic language to generate excitement and keep the audience engaged.', 'examples': '\"at the very end once we get all thirty sold we pull\", \"we do a random little duck race\", \"one person from the number will win\", \"downtown hunting is in effect\", \"let\\'s go\", \"all kinds of stuff in this, man\"'}\n", "{'industry': 'collectibles', 'name': 'Highlighting Product Features', 'explanation': 'Mentioning specific features to inform and attract potential buyers.', 'examples': '\"the boxes have psa and cgc slabs all graded\", \"that box in the middle is that high dollar box\", \"wirelessly controlled\", \"rechargeable\", \"don\\'t need a bunch of batteries\"'}\n", "{'industry': 'collectibles', 'name': 'Creating Scarcity and Urgency', 'explanation': 'Emphasizing limited availability to prompt quick purchases.', 'examples': '\"does anybody else wanna get into this battle?\", \"there\\'s nine packs in it\", \"last two packs\", \"this is it. all we got left\", \"this is my third and last box\"'}\n", "{'industry': 'collectibles', 'name': 'Personal and Interactive Engagement', 'explanation': 'Directly addressing the audience to create a sense of personal interaction.', 'examples': '\"you help me get some more of this obsidian flames off my hands\", \"i will take care of you with that gyarados\", \"let me know which ones you\\'re going on\", \"if you tell me which one you\\'re going on, i can tell you which drawing is on it\", \"you want me to open this box, right?\"'}\n", "{'industry': 'collectibles', 'name': 'Enthusiastic Language', 'explanation': 'Using high-energy and enthusiastic language to create excitement and engage the audience.', 'examples': '\"oh, wow\", \"supernova\", \"that\\'s filthy\", \"oh, it\\'s auto. cracked. bang.\", \"this is my first time seeing this, bro.\", \"solid head. big dog.\", \"it\\'s a nasty nasty looking color match\", \"it\\'s pretty sick\", \"let\\'s freaking go\", \"oh my gosh, that is such a cool card\", \"let\\'s go\"'}\n", "{'industry': 'collectibles', 'name': 'Highlighting Unique Features', 'explanation': 'Emphasizing unique and rare features of the products to create a sense of exclusivity and desirability.', 'examples': '\"one of one\", \"super fracture 101 on the riley green rookie\", \"one serial card\", \"some really, real big hits\", \"some real, real crazy hits\", \"cracked ice. thompson auto.\", \"sick hit trap.\"'}\n", "{'industry': 'collectibles', 'name': 'Repetition for Emphasis', 'explanation': 'Repeating key phrases to reinforce the message and ensure it sticks with the audience.', 'examples': '\"massive break\", \"nuke nuke nuke nuke\", \"this is the break. this is the break.\", \"loaded case. loaded case.\", \"never leave usbs on the shelf. never, never.\", \"thursday night breaks on the board\", \"check them out. check them out early\"'}\n", "\n", "\n", "Category: consumables\n", "{'industry': 'consumables', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences and satisfaction with the product to build trust and credibility with the audience.', 'examples': '\"i have a bunch of their household cleaners\", \"it\\'s been working great\", \"i have a really dirty load up camp clothes that i wanna give it like a good test\"'}\n", "{'industry': 'consumables', 'name': 'Demonstrating Product Use', 'explanation': 'Showing the product in action to provide a clear understanding of its functionality and benefits.', 'examples': '\"put water in it to see if it worked\", \"gonna try it out with my coffee in the morning\", \"i was gonna show you how to curl your hair with a straightener\"'}\n", "{'industry': 'consumables', 'name': 'Highlighting Product Features', 'explanation': 'Emphasizing specific features of the product to showcase its benefits and unique selling points.', 'examples': '\"comes with your carrying bag which is heat protected\", \"it has a fast and a slow setting\", \"you have the home button so it can go straight forward\"'}\n", "{'industry': 'consumables', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences and preferences to build trust and relate to the audience.', 'examples': '\"i absolutely love it\", \"i\\'m in love with this\", \"it\\'s my absolute favorite\", \"i have completely switched over to the instant protein coffee\", \"we take these all the time\", \"i would say the malaysia ones are way better\", \"a lot of people use them\", \"this one\\'s my favorite\"'}\n", "{'industry': 'consumables', 'name': 'Repetition for Emphasis', 'explanation': 'Repeating key phrases and product attributes to reinforce the message and make it memorable.', 'examples': '\"freaking honey packs\", \"great for couples\", \"twelve packs in each box\", \"natural and organic\", \"natural, organic guys\", \"completely natural, organic\", \"twenty twenty nine expiration date\", \"made in malaysia\", \"only the best\"'}\n", "{'industry': 'consumables', 'name': 'Emphasizing Freshness and Quality', 'explanation': 'Highlighting the freshness and superior quality of the product to assure customers of its value.', 'examples': '\"manufactured in march of this year\", \"got the freshest packs on the market\", \"we got these straight from the company\", \"freshest boxes on the market\", \"made in may of this year\", \"fresh packs on the market\", \"completely fresh, guys, made in may of this year\"'}\n", "{'industry': 'consumables', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences and reactions to build trust and authenticity.', 'examples': '\"i opened the bottle this morning and i literally... licked it and i was like, oh, that\\'s really good\", \"i\\'ve been using it in my yard all week long\", \"i thought i was just getting the travel bag\"'}\n", "{'industry': 'consumables', 'name': 'Repetition for Emphasis', 'explanation': 'Repeating key points to reinforce the message and ensure retention.', 'examples': '\"they are full. i mean full\", \"they are loaded, loaded, loaded\", \"flaming hot and fuego are available in the six ounce and in the two ounce\"'}\n", "{'industry': 'consumables', 'name': 'Highlighting Product Benefits', 'explanation': 'Emphasizing the specific benefits of the product.', 'examples': '\"vitamins, electrolytes\", \"if you guys work out, this is especially good\", \"replenish those electrolytes that you lose\"'}\n", "{'industry': 'consumables', 'name': 'Highlighting Health Benefits', 'explanation': 'Emphasizing the health benefits of the product.', 'examples': '\"most americans are deficient in at least fifty milligrams of magnesium\", \"magnesium is well known for helping with cramping\", \"great for the digestive\", \"great for inflammation\", \"great for the stomach\", \"it\\'s a great pick me up\", \"it\\'s great in antioxidants\", \"it\\'s a nice tea that you can use for your morning tea\"'}\n", "{'industry': 'consumables', 'name': 'Repetition', 'explanation': \"Repeating key terms to reinforce the product's name and importance.\", 'examples': '\"oregano. yeah, oregano.\", \"oregano is going to work.\", \"oregano. yeah, oregano is the least one.\", \"english breakfast is a great tea\", \"it\\'s a great pick me up\", \"it\\'s great in antioxidants\"'}\n", "{'industry': 'consumables', 'name': 'Demonstrating Product Usage', 'explanation': 'Explaining how to use the product in various scenarios.', 'examples': '\"we advise use the spray two to three times a day\", \"you can just use it once a day before you go to bed\", \"i always do two sprays in the morning and two sprays at night\", \"add to other tea\", \"every other night i do a mullen in ginger\", \"you can do elderberry ginger\"'}\n", "{'industry': 'consumables', 'name': 'Product Differentiation', 'explanation': 'Clearly distinguishing between different product variants to cater to diverse customer preferences.', 'examples': '\"if you\\'re looking for the spicy mole, make sure that is the rojo\", \"this is a red pozole mix and this is a green pozole mix\", \"this one\\'s a red\"'}\n", "{'industry': 'consumables', 'name': 'Descriptive Language', 'explanation': \"Using vivid and sensory-rich descriptions to create a strong mental image and appeal to the audience's senses.\", 'examples': '\"look how thick it is\", \"it smells so good\", \"the green posole has a lot of different textures\"'}\n", "{'industry': 'consumables', 'name': 'Emphasizing Quality Ingredients', 'explanation': 'Highlighting the use of real, high-quality ingredients to build trust and appeal to health-conscious consumers.', 'examples': '\"all made with real ingredients\", \"all made with chilies that we roast and we toast and we grind\", \"all made with real ingredients\"'}\n", "{'industry': 'consumables', 'name': 'Direct Call to Action', 'explanation': 'Directly instructing the audience to take specific actions, such as sharing the live stream and liking the content.', 'examples': '\"please share our live with all your friends\", \"we need likes please guys get the likes going\", \"check it out\"'}\n", "{'industry': 'consumables', 'name': 'Urgency and Scarcity', 'explanation': 'Creating a sense of urgency and scarcity to prompt immediate action from the audience.', 'examples': '\"exceptional flash sale right now\", \"before we sell out of our posole bombs\", \"we\\'re almost out\"'}\n", "{'industry': 'consumables', 'name': 'Simplifying the Process', 'explanation': 'Emphasizing the simplicity and ease of using the product to make it more appealing to the audience.', 'examples': '\"all you need to add is meat, water and harmony\", \"all the instructions are right here on the bag\", \"you don\\'t have to wait and take a lot of time out of you\"'}\n", "\n", "\n", "Category: electronics, office & books\n", "{'industry': 'electronics, office & books', 'name': 'Direct Call to Action', 'explanation': 'Prompting the audience to take immediate action, creating urgency and guiding them on what to do next.', 'examples': '\"click on the orange shopping cart\", \"click on the shopping cart to see all three designs\", \"click on the shopping cart to see the other two designs\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Highlighting Exclusivity and Limited Availability', 'explanation': \"Emphasizing the product's limited availability to create urgency and a sense of rarity.\", 'examples': '\"exclusive line\", \"exclusive launch on tik tok for three days only\", \"they won\\'t be released until after the tik tok drop\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Detailed Product Description', 'explanation': \"Providing thorough descriptions of the product's features to inform and attract potential buyers.\", 'examples': '\"padded collar and heel\", \"easy slip on elastic laces\", \"sizes 7 through 15 available\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Detailed Product Description', 'explanation': 'Providing a thorough and vivid description of the product to help the audience visualize and understand its features.', 'examples': '\"there is the spongebob face on the front\", \"krabby patty detail on the back\", \"this thing can charge up to five different devices at the same time\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences and positive opinions about the product to build trust and relatability.', 'examples': '\"i love the color scheme on the back of the shoe\", \"i love it. it is so amazing\", \"i like this. like i gotta get another one\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Demonstrating Product Usage', 'explanation': 'Showing how the product works in real-time to help the audience understand its functionality and benefits.', 'examples': '\"so here goes one set in, here goes the other set in\", \"all i need to do is throw up a sign boom turn it on it\\'s gonna follow me\", \"you just unscrew it and look, you can clean\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Highlighting Convenience', 'explanation': 'Emphasizing how the product simplifies tasks and improves quality of life.', 'examples': '\"you want to scoop cat poop anymore, cat pee anymore?\", \"you\\'re basically getting someone to clean the litter box for you every single day\", \"you just are able to sit back and relax\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Addressing Pain Points', 'explanation': 'Identifying common problems and presenting the product as a solution.', 'examples': '\"it\\'s gonna save you the smells\", \"it\\'s gonna save you the sanity of cleaning the frustration of cleaning the litter box\", \"cleaning the litter box is the worst thing ever\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Detailed Product Description', 'explanation': \"Providing a thorough description of the product's features and specifications.\", 'examples': '\"quarter and sockets deep sand deeps and shallows\", \"twelve points and six points\", \"three h drive and it is twelve points and six point shallows\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Repetition for Emphasis', 'explanation': 'Repeating key phrases and product features to reinforce the message.', 'examples': '\"twelve points and six points\", \"six point deeps\", \"twelve of the deeps\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences to build trust and relate to the audience.', 'examples': '\"i absolutely love ours\", \"my kids can come in here and get ice anytime they want\", \"i\\'ve been trying to run this thing this summer as hard as possible\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Highlighting Unique Features', 'explanation': 'Emphasizing unique and innovative features of the product.', 'examples': '\"easy chew nugget restaurant style ice\", \"self cleaning function\", \"smallest footprint for a portable ice machine\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Demonstrating Product Usage', 'explanation': 'Providing a step-by-step demonstration of how the product works.', 'examples': '\"five cups of water is all it takes\", \"it\\'s gonna start producing your ice\", \"the water is gonna go right back into it\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Highlighting Product Features', 'explanation': 'Emphasizing specific features of the product to showcase its benefits.', 'examples': '\"easy chew nugget ice\", \"quick cooling\", \"slow melting\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Emphasizing Product Features', 'explanation': 'Highlighting key features and benefits of the product to attract potential buyers.', 'examples': '\"touching screen going crazy\", \"adjusting volume, music mode, movie mode and gaming mode\", \"find your earbuds function of a year battery\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Demonstrating Product Usage', 'explanation': 'Showing how the product works in real-time to give the audience a clear understanding of its functionality and benefits.', 'examples': '\"i want to show you this one four k game sticks\", \"the game i\\'m playing is the f one f one, two k two three\", \"see how easily they absorb\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Addressing Pain Points', 'explanation': 'Identifying common issues with similar products and explaining how this product resolves them to attract customers who have experienced these problems.', 'examples': '\"tired of those earbuds that goes around your ear and causing fatigue\", \"nothing goes inside not causing any kind of your fatigue\", \"you\\'re tired of those earbuds that goes inside your ear causing all kinds of ear pains\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Highlighting Product Features', 'explanation': 'Emphasizing specific features and benefits of the product to make it more appealing to potential buyers.', 'examples': '\"he can find your phone number right here\", \"red color give you six percent\", \"blue color only for button\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Demonstrating Product Use', 'explanation': 'The speaker explains how to use the product, providing practical insights and making it easier for the audience to understand its application.', 'examples': '\"if you don\\'t want to print a photo just tongue off lip print function\", \"just use a taxi charging cable to charge the camera\", \"so it\\'s a wrap with the lid\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Highlighting Unique Features', 'explanation': 'Pointing out unique aspects of the product to differentiate it from others and add value.', 'examples': '\"that\\'s hematite and copper\", \"custom crystal grids\", \"the camera is very cute with cuddle looking\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Highlighting Unique Features', 'explanation': 'Emphasizing unique and advanced features of the product to showcase its superiority and attract interest.', 'examples': '\"you can get free power\", \"having the solar panel just really\", \"this printer has a touch screen\", \"it\\'s also got a little camera right here\", \"change temperatures all on the printer itself\", \"it\\'s got a built in camera\", \"full color touch screen\", \"monitor your friends on your phone or your computer\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Demonstrating Product Features', 'explanation': \"The speaker highlights specific features and technical specifications to showcase the product's capabilities and appeal to tech-savvy customers.\", 'examples': '\"three sixty pat until uh colored night vision in four k ultra hd\", \"huge solar panel a ten foot cord\", \"sd card right here for removal local storage\", \"three sixty pat until uh colored night vision in four k ultra hd\", \"huge solar panel a ten foot cord\", \"sd card right here for removal local storage\", \"three sixty pat until uh colored night vision in four k ultra hd\", \"huge solar panel a ten foot cord\", \"sd card right here for removal local storage\"'}\n", "{'industry': 'electronics, office & books', 'name': 'Highlighting Discounts and Offers', 'explanation': \"Mentioning significant discounts to attract the audience's attention and encourage purchases.\", 'examples': '\"we also got the s three thirties on sale\", \"the s two twenties everybody are also fifty percent off\", \"seventy five dollars off\", \"these are literally fifty percent off\", \"make sure you grab some of these\"'}\n", "\n", "\n", "Category: fashion\n", "{'industry': 'fashion', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences to build trust and credibility with the audience.', 'examples': '\"i use them. i have pets. i have a fish tank.\", \"i bought these mavericks oils a while back\", \"this is one that i recently added into my skincare routine\"'}\n", "{'industry': 'fashion', 'name': 'Descriptive Language', 'explanation': 'Using vivid and sensory-rich descriptions to create a strong mental image and emotional connection with the product.', 'examples': '\"it smells like vacation\", \"warm, nutty pistachio caramel scent\", \"spa fresh centrist scent\"'}\n", "{'industry': 'fashion', 'name': 'Highlighting Product Features', 'explanation': 'Describing specific features of the product to inform the audience about its unique aspects and benefits.', 'examples': '\"we have the lace here\", \"we have the small pocket here\", \"she\\'s adjustable no matter have the bigger head or smaller head\"'}\n", "{'industry': 'fashion', 'name': 'Demonstration of Product', 'explanation': 'Showing the product in action to provide a visual understanding of its features and effectiveness.', 'examples': '\"we\\'re gonna do a couple tests with these\", \"look at that highest setting\", \"we\\'re gonna try that one more time for you guys\"'}\n", "{'industry': 'fashion', 'name': 'Emphasizing Product Features', 'explanation': 'Repeatedly mentioning key features of the product to highlight its benefits and appeal to potential buyers.', 'examples': '\"lightweight\", \"extended area right here that gives you about an inch and a half of space\", \"three hundred and sixty degree wheels\"'}\n", "{'industry': 'fashion', 'name': 'Detailed Product Description', 'explanation': \"Providing a detailed description of the product's features, including material, fit, and style, to help viewers visualize the product.\", 'examples': '\"this is a light gray color over here\", \"has it got a good stretch? yes, it is nice and stretchy\", \"it does have a slit over here on the side\"'}\n", "{'industry': 'fashion', 'name': 'Detailed Product Description', 'explanation': \"Providing thorough descriptions of the product's features and benefits.\", 'examples': '\"it\\'s ribbed here\", \"she has a full square cydical\", \"three sixty spinable wheels\"'}\n", "{'industry': 'fashion', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences to build trust and relatability.', 'examples': '\"i\\'m five five a hundred and forty pounds i have it on full swing right now\", \"i set it for five am and at six it starts making ice\", \"i am five five one forty. i\\'m rocking the small in this girls.\"'}\n", "{'industry': 'fashion', 'name': 'Demonstrating Product Features', 'explanation': \"Physically showing the product's features to highlight its functionality.\", 'examples': '\"look how stretchy it is, y\\'all\", \"i\\'m gonna push play on this it\\'s gonna give me that five second countdown\", \"i am gonna run it so you guys can hear it while it\\'s cleaning\"'}\n", "{'industry': 'fashion', 'name': 'Repetition for Emphasis', 'explanation': 'Repeating key phrases and features to reinforce the message.', 'examples': '\"this is that nugget ice\", \"it does have a self cleaning mode it does have a self cleaning mode\", \"those pants. those pants.\"'}\n", "{'industry': 'fashion', 'name': 'Repetition for Emphasis', 'explanation': 'Repeating key phrases to reinforce the message and make it memorable.', 'examples': '\"you would love this hair\", \"pepper, yeah, pepper\", \"truly amazing and totally incredible here\"'}\n", "{'industry': 'fashion', 'name': 'Personalization', 'explanation': 'Addressing the audience directly to create a personal connection.', 'examples': '\"madeline, thank you for ordering\", \"for my dear luna\", \"everybody type your name in the comment\"'}\n", "{'industry': 'fashion', 'name': 'Demonstration', 'explanation': 'Showing the product live to help the audience visualize its use and appearance.', 'examples': '\"let me put the blakely dress on\", \"i\\'m gonna put um, the billy shorts back on really quick\", \"let me show you\"'}\n", "{'industry': 'fashion', 'name': 'Highlighting Product Features', 'explanation': 'Emphasizing specific features to showcase benefits and appeal.', 'examples': '\"it\\'s so stunning\", \"eighteen k gold plait hit freshwater\", \"all different three colors\"'}\n", "{'industry': 'fashion', 'name': 'Highlighting Key Features', 'explanation': 'Emphasizing unique and beneficial features of the product to attract potential buyers.', 'examples': '\"android base smart projector\", \"automatic use, no correction and hdmi port, usb and a headphone port\", \"use the kickstand out\"'}\n", "{'industry': 'fashion', 'name': 'Repetition for Emphasis', 'explanation': 'Repeating key phrases to reinforce the message and make it more memorable.', 'examples': '\"it\\'s an extra large\", \"it\\'s an extra large\", \"it\\'s an extra large\", \"super duper cute\", \"real cute, real simple\"'}\n", "{'industry': 'fashion', 'name': 'Detailed Product Description', 'explanation': 'Providing thorough descriptions of the product and its components to inform the audience.', 'examples': '\"in the box you will find the projector, the user manual, the power cable and a remote\", \"this is a smart mini projector that you can use at home or on the go\", \"a bob ten inches long\"'}\n", "{'industry': 'fashion', 'name': 'Personal Engagement', 'explanation': 'Engaging the audience directly to make the interaction feel personal and tailored.', 'examples': '\"if you want me darling, you can just let me know\", \"you can tell us\", \"you can choose the colour\"'}\n", "{'industry': 'fashion', 'name': 'Descriptive Language', 'explanation': 'Using vivid and appealing descriptions to make the product more attractive.', 'examples': '\"super duper cute, super simple\", \"wear with some good old jeans and some, some jays or something\", \"black t shirt with you are enough in like hot hot highlighter pink\"'}\n", "{'industry': 'fashion', 'name': 'Highlighting Product Variety', 'explanation': 'Showcasing different options of the product to cater to various preferences and needs.', 'examples': '\"we got three different colors\", \"you have three kind of choice\", \"solar light\"'}\n", "{'industry': 'fashion', 'name': 'Creating Urgency', 'explanation': 'Emphasizing limited time or availability to prompt quick purchases.', 'examples': '\"it\\'s only for the next five minutes\", \"it\\'s literally less than thirty seconds\", \"they will sell out first\"'}\n", "{'industry': 'fashion', 'name': 'Personal Testimony', 'explanation': 'Sharing personal experiences to build trust and demonstrate product effectiveness.', 'examples': '\"look at my skin. it\\'s glowing\", \"i just bought\", \"i first put them on my body on heather\\'s launch\"'}\n", "{'industry': 'fashion', 'name': 'Detailed Product Description', 'explanation': 'Providing thorough descriptions to help visualize and understand the product.', 'examples': '\"almost 11 inches\", \"fourteen and a half by almost eleven\", \"they are raw hem so you can cut them\"'}\n", "\n", "\n"]}], "source": ["# 从 CSV 文件中读取数据\n", "category_industry_df = pd.read_csv('./data/category_industry_df.csv')\n", "# industry_strategy_df = pd.read_csv('./data/industry_strategy_df.csv')\n", "\n", "# 构建 category_industry_dict\n", "category_industry_dict = category_industry_df.set_index('category')['industry'].to_dict()\n", "\n", "# 构建 industry_strategy_dict\n", "# industry_strategy_dict = industry_strategy_df.groupby('industry')['strategy'].apply(list).to_dict()\n", "df = pd.read_csv('./data/live_narrative_product_strategy.csv')\n", "df['is_seller'] = df['is_seller'].map({1: 'seller', 0: 'affiliate'})\n", "df.rename(columns={'is_seller': 'seller_type', 'narrative_type': 'script_type', 'category': 'industry'}, inplace=True)\n", "df['script_type'] = df['script_type'].map({'product demonstration': 'product'})\n", "\n", "# 将策略转换为字典格式\n", "df_strategies = df[['industry', 'Strategy', 'Explanation', 'Examples']].rename(columns={\n", "    'Strategy': 'name',\n", "    'Explanation': 'explanation',\n", "    'Examples': 'examples'\n", "})\n", "\n", "# 按industry分组并创建字典\n", "industry_strategy_dict = df_strategies.groupby('industry').apply(lambda x: x.to_dict(orient='records')).to_dict()\n", "\n", "# 对每个category的strategies去重\n", "def deduplicate_strategies(strategies):\n", "    seen = set()\n", "    unique_strategies = []\n", "    for strategy in strategies:\n", "        strategy_tuple = tuple(strategy.items())\n", "        if strategy_tuple not in seen:\n", "            seen.add(strategy_tuple)\n", "            unique_strategies.append(strategy)\n", "    return unique_strategies\n", "\n", "# 更新字典中的每个category的strategies\n", "for industry in industry_strategy_dict:\n", "    industry_strategy_dict[industry] = deduplicate_strategies(industry_strategy_dict[industry])\n", "\n", "# 打印前5个策略\n", "for category, strategies in list(industry_strategy_dict.items())[:5]:\n", "    print(f\"Category: {category}\")\n", "    for strategy in strategies:\n", "        print(strategy)\n", "    print(\"\\n\")\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'Personal Testimony',\n", "  'explanation': 'Sharing personal experiences to build trust and relatability with the audience.',\n", "  'examples': '\"i have the black one and i love it\", \"i didn\\'t buy this for gaming. i bought this cause i work from home\", \"i work from home, so watching this all day is very pleasing to me\"'},\n", " {'name': 'Demonstrating Product Usage',\n", "  'explanation': 'Showing how the product works in real-life scenarios to help the audience visualize its practicality and benefits.',\n", "  'examples': '\"i\\'ll put it on and show you what it looks like on my neck\", \"we got the galaxy light projecting onto the ceiling right there\", \"it senses when you\\'re over it and then you do your business, tap, tap, tap, and it closes\"'},\n", " {'name': 'Emphasizing Versatility',\n", "  'explanation': 'Highlighting how the product can be used in various scenarios to appeal to a broader audience.',\n", "  'examples': '\"ideal for a carry on or for just a shorter trip\", \"you can project it onto your wall or like kind of at an angle\", \"do fun food cooking videos\"'},\n", " {'name': 'Personal Testimony',\n", "  'explanation': 'Sharing personal experiences to build trust and relate to the audience.',\n", "  'examples': '\"my husband uses it every day on his lunch break\", \"i actually don\\'t use it for gaming, but i did buy one for my son\", \"i love this one so much more because i can see my whole outfit\"'},\n", " {'name': 'Highlighting Product Features',\n", "  'explanation': 'Emphasizing specific features of the product to showcase its benefits.',\n", "  'examples': '\"super quiet\", \"it has round retro keys\", \"three different sizes are included the 20 inch the 24 inch and then the 28 inch\"'}]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["strategies = df[['Strategy', 'Explanation', 'Examples']].rename(columns={'Strategy': 'name', 'Explanation': 'explanation', 'Examples': 'examples'}).to_dict(orient='records')\n", "strategies[:5]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Product Script Result: {'scripts': [{'title': 'Highlight Benefits', 'text': \"Hey beauty lovers! Today, we're diving into the world of hydration with our Super Hydrating Face Mask. This mask is your skin's best friend, offering intense hydration and rejuvenation for all skin types. Imagine waking up to a plump, glowing complexion every morning!\"}, {'title': 'Customer Testimonials', 'text': \"Don't just take my word for it! Listen to what <PERSON> from New York has to say: 'I've tried countless face masks, but the Super Hydrating Face Mask is a game-changer. My skin feels like it's had a tall glass of water every time I use it!'\"}, {'title': 'Limited Time Offer', 'text': \"Attention, beauty enthusiasts! For the next 24 hours, you can grab the Super Hydrating Face Mask at a special discount. Don't miss out on this chance to give your skin the hydration it deserves!\"}, {'title': 'Highlight Benefits', 'text': 'Did you know that hydrated skin is the key to a youthful glow? Our Super Hydrating Face Mask is packed with nourishing ingredients that penetrate deep into your skin, leaving it soft, supple, and radiant.'}, {'title': 'Customer Testimonials', 'text': \"Here's what <PERSON> from California shared: 'After just one use, my skin felt incredibly soft and hydrated. It's like a spa treatment in a jar!'\"}, {'title': 'Limited Time Offer', 'text': \"Quick heads up! Our Super Hydrating Face Mask is flying off the shelves. Secure yours now with our exclusive live stream discount before it's too late!\"}, {'title': 'Highlight Benefits', 'text': \"Struggling with dry patches or dull skin? The Super Hydrating Face Mask is here to rescue you! It's designed to lock in moisture and rejuvenate your skin, making it perfect for all skin types.\"}, {'title': 'Customer Testimonials', 'text': \"Jessica from Texas says, 'This mask is a lifesaver! My skin has never felt so hydrated and refreshed. It's a must-have in my skincare routine.'\"}, {'title': 'Limited Time Offer', 'text': 'Exclusive offer alert! For a limited time, enjoy a 20% discount on the Super Hydrating Face Mask. Your skin will thank you for it!'}, {'title': 'Highlight Benefits', 'text': 'Why settle for ordinary when you can have extraordinary? The Super Hydrating Face Mask not only hydrates but also rejuvenates, giving your skin a youthful, dewy glow.'}, {'title': 'Customer Testimonials', 'text': \"Listen to what Mark from Florida has to say: 'I've never felt more confident in my skin. This mask is a staple in my skincare routine now!'\"}, {'title': 'Limited Time Offer', 'text': \"Don't wait! Our special offer on the Super Hydrating Face Mask is ending soon. Grab yours now and experience the magic of intense hydration!\"}, {'title': 'Highlight Benefits', 'text': \"Transform your skincare routine with the Super Hydrating Face Mask. It's like a hydration boost for your skin, leaving it feeling refreshed and revitalized.\"}, {'title': 'Customer Testimonials', 'text': \"Anna from Chicago raves, 'This mask is incredible! My skin feels so much smoother and hydrated. I can't imagine my routine without it.'\"}, {'title': 'Limited Time Offer', 'text': \"Hurry! Our live stream exclusive offer on the Super Hydrating Face Mask won't last long. Get yours now and enjoy the benefits of deeply hydrated skin!\"}, {'title': 'Highlight Benefits', 'text': \"Say goodbye to dry, tired skin with the Super Hydrating Face Mask. It's formulated to deliver intense hydration and rejuvenation, perfect for all skin types.\"}, {'title': 'Customer Testimonials', 'text': \"Here's what David from Seattle has to say: 'This mask is a game-changer for my skin. It's never felt so hydrated and healthy!'\"}, {'title': 'Limited Time Offer', 'text': \"Last chance to take advantage of our special offer on the Super Hydrating Face Mask. Don't miss out on the opportunity to transform your skin!\"}]}\n", "Total Tokens (Product): 1165\n", "Prompt Tokens (Product): 207\n", "Completion Tokens (Product): 958\n", "Execution Time (seconds): 54.280850887298584\n"]}], "source": ["import time\n", "\n", "# 生成商品话术\n", "product_name = \"Super Hydrating Face Mask\"\n", "product_description = \"A face mask that offers intense hydration and rejuvenation for all skin types.\"\n", "strategies = [\n", "    {\n", "        \"name\": \"Highlight Benefits\", \n", "        \"explanation\": \"Emphasize the key benefits of the product to attract customers.\", \n", "        \"examples\": \"Hey everyone! Today, I'm thrilled to introduce the Super Hydrating Face Mask. This mask is your ultimate solution for intense hydration and rejuvenation. Whether you have dry, oily, or combination skin, this mask works wonders to leave your skin feeling soft, supple, and refreshed. Imagine waking up to a glowing complexion every day!\"\n", "    },\n", "    {\n", "        \"name\": \"Customer Testimonials\", \n", "        \"explanation\": \"Include positive feedback from customers to build trust.\", \n", "        \"examples\": \"Let me share some amazing feedback from our happy customers! <PERSON> from New York says, 'The Super Hydrating Face Mask has transformed my skin! It's never felt so hydrated and smooth.' And <PERSON> from California adds, 'I love how it suits my sensitive skin without any irritation. A must-have in my skincare routine!'\"\n", "    },\n", "    {\n", "        \"name\": \"Limited Time Offer\", \n", "        \"explanation\": \"Create a sense of urgency with a limited time discount or offer.\", \n", "        \"examples\": \"Exciting news, everyone! For a limited time only, you can grab the Super Hydrating Face Mask at a special discount. Don't miss out on this exclusive offer to elevate your skincare game. Hurry, the clock is ticking, and this deal won't last long!\"\n", "    }\n", "]\n", "\n", "# 开始计时\n", "start_time = time.time()\n", "\n", "# 生成商品话术\n", "result_product, total_tokens_product, prompt_tokens_product, completion_tokens_product = agent.generate_product_script(\n", "    industry=\"Beauty & Personal Care\",\n", "    product_description=product_description,\n", "    strategies=strategies,\n", "    product_name=product_name\n", ")\n", "\n", "# 结束计时\n", "end_time = time.time()\n", "\n", "# 计算执行时间\n", "execution_time = end_time - start_time\n", "\n", "# 打印结果\n", "print(\"Product Script Result:\", result_product)\n", "print(\"Total Tokens (Product):\", total_tokens_product)\n", "print(\"Prompt Tokens (Product):\", prompt_tokens_product)\n", "print(\"Completion Tokens (Product):\", completion_tokens_product)\n", "print(\"Execution Time (seconds):\", execution_time)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 32\u001b[0m\n\u001b[1;32m     29\u001b[0m start_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[1;32m     31\u001b[0m \u001b[38;5;66;03m# 生成商品话术\u001b[39;00m\n\u001b[0;32m---> 32\u001b[0m result_product, total_tokens_product, prompt_tokens_product, completion_tokens_product \u001b[38;5;241m=\u001b[39m agent\u001b[38;5;241m.\u001b[39mgenerate_product_script(\n\u001b[1;32m     33\u001b[0m     industry\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mBeauty & Personal Care\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     34\u001b[0m     product_description\u001b[38;5;241m=\u001b[39mproduct_description,\n\u001b[1;32m     35\u001b[0m     strategies\u001b[38;5;241m=\u001b[39mstrategies,\n\u001b[1;32m     36\u001b[0m     product_name\u001b[38;5;241m=\u001b[39mproduct_name\n\u001b[1;32m     37\u001b[0m )\n\u001b[1;32m     39\u001b[0m end_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[1;32m     40\u001b[0m execution_time \u001b[38;5;241m=\u001b[39m end_time \u001b[38;5;241m-\u001b[39m start_time\n", "Cell \u001b[0;32mIn[1], line 120\u001b[0m, in \u001b[0;36mOpenAIAgent.generate_product_script\u001b[0;34m(self, industry, strategies, product_name, product_description)\u001b[0m\n\u001b[1;32m     90\u001b[0m examples \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mjoin([\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m**Examples for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mstrategy[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mname\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m**: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mstrategy[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mexamples\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m strategy \u001b[38;5;129;01min\u001b[39;00m strategies])\n\u001b[1;32m     93\u001b[0m user_message \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m     94\u001b[0m \u001b[38;5;124mIndustry: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mindustry\u001b[38;5;132;01m}\u001b[39;00m\n\u001b[1;32m     95\u001b[0m \u001b[38;5;124mProduct Description: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mproduct_description\u001b[38;5;132;01m}\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    117\u001b[0m \u001b[38;5;124m\u001b[39m\u001b[38;5;130;01m}}\u001b[39;00m\n\u001b[1;32m    118\u001b[0m \u001b[38;5;124m\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[0;32m--> 120\u001b[0m result, total_tokens, prompt_tokens, completion_tokens \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrun_openai_task(system_message, user_message)\n\u001b[1;32m    121\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m result, total_tokens, prompt_tokens, completion_tokens\n", "Cell \u001b[0;32mIn[1], line 61\u001b[0m, in \u001b[0;36mOpenAIAgent.run_openai_task\u001b[0;34m(self, system_message, user_message)\u001b[0m\n\u001b[1;32m     55\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mrun_openai_task\u001b[39m(\u001b[38;5;28mself\u001b[39m, system_message, user_message):\n\u001b[1;32m     56\u001b[0m     messages \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     57\u001b[0m         {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>e\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msystem\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msystem_message(system_message)},\n\u001b[1;32m     58\u001b[0m         {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>e\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39muser_message(user_message)}\n\u001b[1;32m     59\u001b[0m     ]\n\u001b[0;32m---> 61\u001b[0m     completion \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclient\u001b[38;5;241m.\u001b[39mchat\u001b[38;5;241m.\u001b[39mcompletions\u001b[38;5;241m.\u001b[39mcreate(\n\u001b[1;32m     62\u001b[0m         extra_headers\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mX-TT-LOGID\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mabc\u001b[39m\u001b[38;5;124m\"\u001b[39m},  \u001b[38;5;66;03m# 请务必带上此header，方便定位问题\u001b[39;00m\n\u001b[1;32m     63\u001b[0m         model\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel,\n\u001b[1;32m     64\u001b[0m         messages\u001b[38;5;241m=\u001b[39mmessages,\n\u001b[1;32m     65\u001b[0m         temperature\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m,\n\u001b[1;32m     66\u001b[0m         frequency_penalty\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m,\n\u001b[1;32m     67\u001b[0m         presence_penalty\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m,\n\u001b[1;32m     68\u001b[0m         max_tokens\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m4096\u001b[39m\u001b[38;5;66;03m# 添加max_tokens参数\u001b[39;00m\n\u001b[1;32m     69\u001b[0m     )\n\u001b[1;32m     71\u001b[0m     json_data \u001b[38;5;241m=\u001b[39m completion\u001b[38;5;241m.\u001b[39mmodel_dump_json()\n\u001b[1;32m     72\u001b[0m     data_dict \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mloads(json_data)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_utils/_utils.py:299\u001b[0m, in \u001b[0;36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    297\u001b[0m             msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[38;5;241m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    298\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[0;32m--> 299\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/resources/chat/completions.py:594\u001b[0m, in \u001b[0;36mCompletions.create\u001b[0;34m(self, messages, model, frequency_penalty, function_call, functions, logit_bias, max_tokens, n, presence_penalty, response_format, seed, stop, stream, temperature, tool_choice, tools, top_p, user, extra_headers, extra_query, extra_body, timeout)\u001b[0m\n\u001b[1;32m    548\u001b[0m \u001b[38;5;129m@required_args\u001b[39m([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m], [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[1;32m    549\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcreate\u001b[39m(\n\u001b[1;32m    550\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    592\u001b[0m     timeout: \u001b[38;5;28mfloat\u001b[39m \u001b[38;5;241m|\u001b[39m httpx\u001b[38;5;241m.\u001b[39mTimeout \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m|\u001b[39m NotGiven \u001b[38;5;241m=\u001b[39m NOT_GIVEN,\n\u001b[1;32m    593\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ChatCompletion \u001b[38;5;241m|\u001b[39m Stream[ChatCompletionChunk]:\n\u001b[0;32m--> 594\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_post(\n\u001b[1;32m    595\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/chat/completions\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    596\u001b[0m         body\u001b[38;5;241m=\u001b[39mmaybe_transform(\n\u001b[1;32m    597\u001b[0m             {\n\u001b[1;32m    598\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: messages,\n\u001b[1;32m    599\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m: model,\n\u001b[1;32m    600\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfrequency_penalty\u001b[39m\u001b[38;5;124m\"\u001b[39m: frequency_penalty,\n\u001b[1;32m    601\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfunction_call\u001b[39m\u001b[38;5;124m\"\u001b[39m: function_call,\n\u001b[1;32m    602\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfunctions\u001b[39m\u001b[38;5;124m\"\u001b[39m: functions,\n\u001b[1;32m    603\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlogit_bias\u001b[39m\u001b[38;5;124m\"\u001b[39m: logit_bias,\n\u001b[1;32m    604\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmax_tokens\u001b[39m\u001b[38;5;124m\"\u001b[39m: max_tokens,\n\u001b[1;32m    605\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mn\u001b[39m\u001b[38;5;124m\"\u001b[39m: n,\n\u001b[1;32m    606\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpresence_penalty\u001b[39m\u001b[38;5;124m\"\u001b[39m: presence_penalty,\n\u001b[1;32m    607\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse_format\u001b[39m\u001b[38;5;124m\"\u001b[39m: response_format,\n\u001b[1;32m    608\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mseed\u001b[39m\u001b[38;5;124m\"\u001b[39m: seed,\n\u001b[1;32m    609\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstop\u001b[39m\u001b[38;5;124m\"\u001b[39m: stop,\n\u001b[1;32m    610\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m: stream,\n\u001b[1;32m    611\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtemperature\u001b[39m\u001b[38;5;124m\"\u001b[39m: temperature,\n\u001b[1;32m    612\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtool_choice\u001b[39m\u001b[38;5;124m\"\u001b[39m: tool_choice,\n\u001b[1;32m    613\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtools\u001b[39m\u001b[38;5;124m\"\u001b[39m: tools,\n\u001b[1;32m    614\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtop_p\u001b[39m\u001b[38;5;124m\"\u001b[39m: top_p,\n\u001b[1;32m    615\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m: user,\n\u001b[1;32m    616\u001b[0m             },\n\u001b[1;32m    617\u001b[0m             completion_create_params\u001b[38;5;241m.\u001b[39mCompletionCreateParams,\n\u001b[1;32m    618\u001b[0m         ),\n\u001b[1;32m    619\u001b[0m         options\u001b[38;5;241m=\u001b[39mmake_request_options(\n\u001b[1;32m    620\u001b[0m             extra_headers\u001b[38;5;241m=\u001b[39mextra_headers, extra_query\u001b[38;5;241m=\u001b[39mextra_query, extra_body\u001b[38;5;241m=\u001b[39mextra_body, timeout\u001b[38;5;241m=\u001b[39mtimeout\n\u001b[1;32m    621\u001b[0m         ),\n\u001b[1;32m    622\u001b[0m         cast_to\u001b[38;5;241m=\u001b[39mChatCompletion,\n\u001b[1;32m    623\u001b[0m         stream\u001b[38;5;241m=\u001b[39mstream \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m    624\u001b[0m         stream_cls\u001b[38;5;241m=\u001b[39mStream[ChatCompletionChunk],\n\u001b[1;32m    625\u001b[0m     )\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_base_client.py:1055\u001b[0m, in \u001b[0;36mSyncAPIClient.post\u001b[0;34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[0m\n\u001b[1;32m   1041\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\n\u001b[1;32m   1042\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1043\u001b[0m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1050\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1051\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _StreamT:\n\u001b[1;32m   1052\u001b[0m     opts \u001b[38;5;241m=\u001b[39m FinalRequestOptions\u001b[38;5;241m.\u001b[39mconstruct(\n\u001b[1;32m   1053\u001b[0m         method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpost\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39mpath, json_data\u001b[38;5;241m=\u001b[39mbody, files\u001b[38;5;241m=\u001b[39mto_httpx_files(files), \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions\n\u001b[1;32m   1054\u001b[0m     )\n\u001b[0;32m-> 1055\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest(cast_to, opts, stream\u001b[38;5;241m=\u001b[39mstream, stream_cls\u001b[38;5;241m=\u001b[39mstream_cls))\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_base_client.py:834\u001b[0m, in \u001b[0;36mSyncAPIClient.request\u001b[0;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[1;32m    825\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mrequest\u001b[39m(\n\u001b[1;32m    826\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    827\u001b[0m     cast_to: Type[ResponseT],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    832\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    833\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _StreamT:\n\u001b[0;32m--> 834\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_request(\n\u001b[1;32m    835\u001b[0m         cast_to\u001b[38;5;241m=\u001b[39mcast_to,\n\u001b[1;32m    836\u001b[0m         options\u001b[38;5;241m=\u001b[39moptions,\n\u001b[1;32m    837\u001b[0m         stream\u001b[38;5;241m=\u001b[39mstream,\n\u001b[1;32m    838\u001b[0m         stream_cls\u001b[38;5;241m=\u001b[39mstream_cls,\n\u001b[1;32m    839\u001b[0m         remaining_retries\u001b[38;5;241m=\u001b[39mremaining_retries,\n\u001b[1;32m    840\u001b[0m     )\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_base_client.py:858\u001b[0m, in \u001b[0;36mSyncAPIClient._request\u001b[0;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[1;32m    855\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_prepare_request(request)\n\u001b[1;32m    857\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 858\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client\u001b[38;5;241m.\u001b[39msend(request, auth\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcustom_auth, stream\u001b[38;5;241m=\u001b[39mstream)\n\u001b[1;32m    859\u001b[0m     log\u001b[38;5;241m.\u001b[39mdebug(\n\u001b[1;32m    860\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mHTTP Request: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m%i\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m, request\u001b[38;5;241m.\u001b[39mmethod, request\u001b[38;5;241m.\u001b[39murl, response\u001b[38;5;241m.\u001b[39mstatus_code, response\u001b[38;5;241m.\u001b[39mreason_phrase\n\u001b[1;32m    861\u001b[0m     )\n\u001b[1;32m    862\u001b[0m     response\u001b[38;5;241m.\u001b[39mraise_for_status()\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpx/_client.py:901\u001b[0m, in \u001b[0;36mClient.send\u001b[0;34m(self, request, stream, auth, follow_redirects)\u001b[0m\n\u001b[1;32m    893\u001b[0m follow_redirects \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    894\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfollow_redirects\n\u001b[1;32m    895\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(follow_redirects, UseClientDefault)\n\u001b[1;32m    896\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m follow_redirects\n\u001b[1;32m    897\u001b[0m )\n\u001b[1;32m    899\u001b[0m auth \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_build_request_auth(request, auth)\n\u001b[0;32m--> 901\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_handling_auth(\n\u001b[1;32m    902\u001b[0m     request,\n\u001b[1;32m    903\u001b[0m     auth\u001b[38;5;241m=\u001b[39mauth,\n\u001b[1;32m    904\u001b[0m     follow_redirects\u001b[38;5;241m=\u001b[39mfollow_redirects,\n\u001b[1;32m    905\u001b[0m     history\u001b[38;5;241m=\u001b[39m[],\n\u001b[1;32m    906\u001b[0m )\n\u001b[1;32m    907\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    908\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m stream:\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpx/_client.py:929\u001b[0m, in \u001b[0;36mClient._send_handling_auth\u001b[0;34m(self, request, auth, follow_redirects, history)\u001b[0m\n\u001b[1;32m    926\u001b[0m request \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mnext\u001b[39m(auth_flow)\n\u001b[1;32m    928\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 929\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_handling_redirects(\n\u001b[1;32m    930\u001b[0m         request,\n\u001b[1;32m    931\u001b[0m         follow_redirects\u001b[38;5;241m=\u001b[39mfollow_redirects,\n\u001b[1;32m    932\u001b[0m         history\u001b[38;5;241m=\u001b[39mhistory,\n\u001b[1;32m    933\u001b[0m     )\n\u001b[1;32m    934\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    935\u001b[0m         \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpx/_client.py:966\u001b[0m, in \u001b[0;36mClient._send_handling_redirects\u001b[0;34m(self, request, follow_redirects, history)\u001b[0m\n\u001b[1;32m    963\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_event_hooks[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrequest\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n\u001b[1;32m    964\u001b[0m     hook(request)\n\u001b[0;32m--> 966\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_single_request(request)\n\u001b[1;32m    967\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    968\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_event_hooks[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpx/_client.py:1002\u001b[0m, in \u001b[0;36mClient._send_single_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    997\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[1;32m    998\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAttempted to send an async request with a sync Client instance.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    999\u001b[0m     )\n\u001b[1;32m   1001\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request\u001b[38;5;241m=\u001b[39mrequest):\n\u001b[0;32m-> 1002\u001b[0m     response \u001b[38;5;241m=\u001b[39m transport\u001b[38;5;241m.\u001b[39mhandle_request(request)\n\u001b[1;32m   1004\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response\u001b[38;5;241m.\u001b[39mstream, SyncByteStream)\n\u001b[1;32m   1006\u001b[0m response\u001b[38;5;241m.\u001b[39mrequest \u001b[38;5;241m=\u001b[39m request\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpx/_transports/default.py:228\u001b[0m, in \u001b[0;36mHTTPTransport.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    215\u001b[0m req \u001b[38;5;241m=\u001b[39m httpcore\u001b[38;5;241m.\u001b[39mRequest(\n\u001b[1;32m    216\u001b[0m     method\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mmethod,\n\u001b[1;32m    217\u001b[0m     url\u001b[38;5;241m=\u001b[39mhttpcore\u001b[38;5;241m.\u001b[39mURL(\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    225\u001b[0m     extensions\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mextensions,\n\u001b[1;32m    226\u001b[0m )\n\u001b[1;32m    227\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[0;32m--> 228\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_pool\u001b[38;5;241m.\u001b[39mhandle_request(req)\n\u001b[1;32m    230\u001b[0m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(resp\u001b[38;5;241m.\u001b[39mstream, typing\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[1;32m    232\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m Response(\n\u001b[1;32m    233\u001b[0m     status_code\u001b[38;5;241m=\u001b[39mresp\u001b[38;5;241m.\u001b[39mstatus,\n\u001b[1;32m    234\u001b[0m     headers\u001b[38;5;241m=\u001b[39mresp\u001b[38;5;241m.\u001b[39mheaders,\n\u001b[1;32m    235\u001b[0m     stream\u001b[38;5;241m=\u001b[39mResponseStream(resp\u001b[38;5;241m.\u001b[39mstream),\n\u001b[1;32m    236\u001b[0m     extensions\u001b[38;5;241m=\u001b[39mresp\u001b[38;5;241m.\u001b[39mextensions,\n\u001b[1;32m    237\u001b[0m )\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py:262\u001b[0m, in \u001b[0;36mConnectionPool.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    260\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m ShieldCancellation():\n\u001b[1;32m    261\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mresponse_closed(status)\n\u001b[0;32m--> 262\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exc\n\u001b[1;32m    263\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    264\u001b[0m     \u001b[38;5;28;01mbreak\u001b[39;00m\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py:245\u001b[0m, in \u001b[0;36mConnectionPool.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    242\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m exc\n\u001b[1;32m    244\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 245\u001b[0m     response \u001b[38;5;241m=\u001b[39m connection\u001b[38;5;241m.\u001b[39mhandle_request(request)\n\u001b[1;32m    246\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ConnectionNotAvailable:\n\u001b[1;32m    247\u001b[0m     \u001b[38;5;66;03m# The ConnectionNotAvailable exception is a special case, that\u001b[39;00m\n\u001b[1;32m    248\u001b[0m     \u001b[38;5;66;03m# indicates we need to retry the request on a new connection.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    252\u001b[0m     \u001b[38;5;66;03m# might end up as an HTTP/2 connection, but which actually ends\u001b[39;00m\n\u001b[1;32m    253\u001b[0m     \u001b[38;5;66;03m# up as HTTP/1.1.\u001b[39;00m\n\u001b[1;32m    254\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_pool_lock:\n\u001b[1;32m    255\u001b[0m         \u001b[38;5;66;03m# Maintain our position in the request queue, but reset the\u001b[39;00m\n\u001b[1;32m    256\u001b[0m         \u001b[38;5;66;03m# status so that the request becomes queued again.\u001b[39;00m\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/connection.py:103\u001b[0m, in \u001b[0;36mHTTPConnection.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    100\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connection\u001b[38;5;241m.\u001b[39mis_available():\n\u001b[1;32m    101\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m ConnectionNotAvailable()\n\u001b[0;32m--> 103\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connection\u001b[38;5;241m.\u001b[39mhandle_request(request)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/http11.py:133\u001b[0m, in \u001b[0;36mHTTP11Connection.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    131\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m Trace(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse_closed\u001b[39m\u001b[38;5;124m\"\u001b[39m, logger, request) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[1;32m    132\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_response_closed()\n\u001b[0;32m--> 133\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m exc\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/http11.py:111\u001b[0m, in \u001b[0;36mHTTP11Connection.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    101\u001b[0m     \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[1;32m    103\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m Trace(\n\u001b[1;32m    104\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mreceive_response_headers\u001b[39m\u001b[38;5;124m\"\u001b[39m, logger, request, kwargs\n\u001b[1;32m    105\u001b[0m ) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[1;32m    106\u001b[0m     (\n\u001b[1;32m    107\u001b[0m         http_version,\n\u001b[1;32m    108\u001b[0m         status,\n\u001b[1;32m    109\u001b[0m         reason_phrase,\n\u001b[1;32m    110\u001b[0m         headers,\n\u001b[0;32m--> 111\u001b[0m     ) \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_receive_response_headers(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    112\u001b[0m     trace\u001b[38;5;241m.\u001b[39mreturn_value \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    113\u001b[0m         http_version,\n\u001b[1;32m    114\u001b[0m         status,\n\u001b[1;32m    115\u001b[0m         reason_phrase,\n\u001b[1;32m    116\u001b[0m         headers,\n\u001b[1;32m    117\u001b[0m     )\n\u001b[1;32m    119\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m Response(\n\u001b[1;32m    120\u001b[0m     status\u001b[38;5;241m=\u001b[39mstatus,\n\u001b[1;32m    121\u001b[0m     headers\u001b[38;5;241m=\u001b[39mheaders,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    127\u001b[0m     },\n\u001b[1;32m    128\u001b[0m )\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/http11.py:176\u001b[0m, in \u001b[0;36mHTTP11Connection._receive_response_headers\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    173\u001b[0m timeout \u001b[38;5;241m=\u001b[39m timeouts\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mread\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m    175\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 176\u001b[0m     event \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_receive_event(timeout\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[1;32m    177\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(event, h11\u001b[38;5;241m.\u001b[39mResponse):\n\u001b[1;32m    178\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/http11.py:212\u001b[0m, in \u001b[0;36mHTTP11Connection._receive_event\u001b[0;34m(self, timeout)\u001b[0m\n\u001b[1;32m    209\u001b[0m     event \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_h11_state\u001b[38;5;241m.\u001b[39mnext_event()\n\u001b[1;32m    211\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m event \u001b[38;5;129;01mis\u001b[39;00m h11\u001b[38;5;241m.\u001b[39mNEED_DATA:\n\u001b[0;32m--> 212\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_network_stream\u001b[38;5;241m.\u001b[39mread(\n\u001b[1;32m    213\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mREAD_NUM_BYTES, timeout\u001b[38;5;241m=\u001b[39mtimeout\n\u001b[1;32m    214\u001b[0m     )\n\u001b[1;32m    216\u001b[0m     \u001b[38;5;66;03m# If we feed this case through h11 we'll raise an exception like:\u001b[39;00m\n\u001b[1;32m    217\u001b[0m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[1;32m    218\u001b[0m     \u001b[38;5;66;03m#     httpcore.RemoteProtocolError: can't handle event type\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    222\u001b[0m     \u001b[38;5;66;03m# perspective. Instead we handle this case distinctly and treat\u001b[39;00m\n\u001b[1;32m    223\u001b[0m     \u001b[38;5;66;03m# it as a ConnectError.\u001b[39;00m\n\u001b[1;32m    224\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m data \u001b[38;5;241m==\u001b[39m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_h11_state\u001b[38;5;241m.\u001b[39mtheir_state \u001b[38;5;241m==\u001b[39m h11\u001b[38;5;241m.\u001b[39mSEND_RESPONSE:\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_backends/sync.py:126\u001b[0m, in \u001b[0;36mSyncStream.read\u001b[0;34m(self, max_bytes, timeout)\u001b[0m\n\u001b[1;32m    124\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m map_exceptions(exc_map):\n\u001b[1;32m    125\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sock\u001b[38;5;241m.\u001b[39msettimeout(timeout)\n\u001b[0;32m--> 126\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sock\u001b[38;5;241m.\u001b[39mrecv(max_bytes)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/ssl.py:1263\u001b[0m, in \u001b[0;36mSSLSocket.recv\u001b[0;34m(self, buflen, flags)\u001b[0m\n\u001b[1;32m   1259\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m flags \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m   1260\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m   1261\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnon-zero flags not allowed in calls to recv() on \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m\n\u001b[1;32m   1262\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m)\n\u001b[0;32m-> 1263\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mread(buflen)\n\u001b[1;32m   1264\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1265\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mrecv(buflen, flags)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/ssl.py:1136\u001b[0m, in \u001b[0;36mSSLSocket.read\u001b[0;34m(self, len, buffer)\u001b[0m\n\u001b[1;32m   1134\u001b[0m         \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sslobj\u001b[38;5;241m.\u001b[39mread(\u001b[38;5;28mlen\u001b[39m, buffer)\n\u001b[1;32m   1135\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1136\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sslobj\u001b[38;5;241m.\u001b[39mread(\u001b[38;5;28mlen\u001b[39m)\n\u001b[1;32m   1137\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m SSLError \u001b[38;5;28;01mas\u001b[39;00m x:\n\u001b[1;32m   1138\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m x\u001b[38;5;241m.\u001b[39margs[\u001b[38;5;241m0\u001b[39m] \u001b[38;5;241m==\u001b[39m SSL_ERROR_EOF \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msuppress_ragged_eofs:\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import time\n", "\n", "# 生成商品话术\n", "product_name = \"Super Hydrating Face Mask\"\n", "product_description = \"A face mask that offers intense hydration and rejuvenation for all skin types.\"\n", "strategies = [\n", "    {\n", "        \"name\": \"Highlight Benefits\", \n", "        \"explanation\": \"Emphasize the key benefits of the product to attract customers.\", \n", "        \"examples\": \"Hey everyone! Today, I'm thrilled to introduce the Super Hydrating Face Mask. This mask is your ultimate solution for intense hydration and rejuvenation. Whether you have dry, oily, or combination skin, this mask works wonders to leave your skin feeling soft, supple, and refreshed. Imagine waking up to a glowing complexion every day!\"\n", "    },\n", "    {\n", "        \"name\": \"Customer Testimonials\", \n", "        \"explanation\": \"Include positive feedback from customers to build trust.\", \n", "        \"examples\": \"Let me share some amazing feedback from our happy customers! <PERSON> from New York says, 'The Super Hydrating Face Mask has transformed my skin! It's never felt so hydrated and smooth.' And <PERSON> from California adds, 'I love how it suits my sensitive skin without any irritation. A must-have in my skincare routine!'\"\n", "    },\n", "    {\n", "        \"name\": \"Limited Time Offer\", \n", "        \"explanation\": \"Create a sense of urgency with a limited time discount or offer.\", \n", "        \"examples\": \"Exciting news, everyone! For a limited time only, you can grab the Super Hydrating Face Mask at a special discount. Don't miss out on this exclusive offer to elevate your skincare game. Hurry, the clock is ticking, and this deal won't last long!\"\n", "    }\n", "]\n", "\n", "# 记录每次执行的时间\n", "execution_times = []\n", "\n", "# 运行100次生成商品话术的任务\n", "for _ in range(100):\n", "    start_time = time.time()\n", "    \n", "    # 生成商品话术\n", "    result_product, total_tokens_product, prompt_tokens_product, completion_tokens_product = agent.generate_product_script(\n", "        industry=\"Beauty & Personal Care\",\n", "        product_description=product_description,\n", "        strategies=strategies,\n", "        product_name=product_name\n", "    )\n", "    \n", "    end_time = time.time()\n", "    execution_time = end_time - start_time\n", "    execution_times.append(execution_time)\n", "\n", "# 计算平均执行时间\n", "average_execution_time = sum(execution_times) / len(execution_times)\n", "\n", "# 计算方差\n", "standard_deviation_execution_time = (sum((x - average_execution_time) ** 2 for x in execution_times) / len(execution_times)) ** 0.5\n", "\n", "# 打印结果\n", "print(\"Product Script Result:\", result_product)\n", "print(\"Total Tokens (Product):\", total_tokens_product)\n", "print(\"Prompt Tokens (Product):\", prompt_tokens_product)\n", "print(\"Completion Tokens (Product):\", completion_tokens_product)\n", "print(\"Average Execution Time (seconds):\", average_execution_time)\n", "print(\"Standard deviation of Execution Time (seconds):\", standard_deviation_execution_time)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'scripts': [{'title': 'Highlight Benefits',\n", "   'text': \"Hey everyone! Today, I'm thrilled to introduce the Super Hydrating Face Mask. This mask is your ultimate solution for intense hydration and rejuvenation. Whether you have dry, oily, or combination skin, this mask works wonders to leave your skin feeling soft, supple, and refreshed. Imagine waking up to a glowing complexion every day!\"},\n", "  {'title': 'Customer Testimonials',\n", "   'text': \"Let me share some amazing feedback from our happy customers! <PERSON> from New York says, 'The Super Hydrating Face Mask has transformed my skin! It's never felt so hydrated and smooth.' And <PERSON> from California adds, 'I love how it suits my sensitive skin without any irritation. A must-have in my skincare routine!'\"},\n", "  {'title': 'Limited Time Offer',\n", "   'text': \"Exciting news, everyone! For a limited time only, you can grab the Super Hydrating Face Mask at a special discount. Don't miss out on this exclusive offer to elevate your skincare game. Hurry, the clock is ticking, and this deal won't last long!\"},\n", "  {'title': 'Highlight Benefits',\n", "   'text': \"Hello beauties! If you're looking for a skincare product that delivers intense hydration and rejuvenation, look no further than the Super Hydrating Face Mask. It's perfect for all skin types and will leave your skin feeling incredibly soft and refreshed. Say goodbye to dull, dry skin and hello to a radiant glow!\"},\n", "  {'title': 'Customer Testimonials',\n", "   'text': \"I have to share some fantastic reviews with you! <PERSON> from Texas says, 'This face mask is a game-changer! My skin feels so much more hydrated and looks visibly healthier.' And <PERSON> from Florida says, 'I've tried many face masks, but this one is by far the best. It leaves my skin feeling rejuvenated and smooth.'\"}]}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["result_product"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import asyncio\n", "\n", "# 读取CSV文件\n", "data_df = pd.read_csv('./data/data_sample.csv')\n", "data_df = data_df.head(100)\n", "\n", "# 定义提取数据的函数\n", "def extract_data(row):\n", "    changed_data = row['changed_data']\n", "    if changed_data:\n", "        try:\n", "            data_json = json.loads(changed_data)\n", "            product_name = data_json.get('productName')\n", "            title = data_json.get('title')\n", "            description = data_json.get('description')\n", "            # 提取第一个 imageUrl\n", "            image_url = data_json['imageInfos'][0]['imageUrl'] if 'imageInfos' in data_json and data_json['imageInfos'] else None\n", "            return row['product_id'], product_name, title, description, row['first_category_name'], image_url\n", "        except (j<PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IndexError):\n", "            pass\n", "    return row['product_id'], None, None, None, row['first_category_name'], None\n", "\n", "# 应用函数提取数据，并创建新 DataFrame\n", "extracted_data = data_df.apply(lambda row: extract_data(row), axis=1)\n", "new_df = pd.DataFrame(extracted_data.tolist(), columns=['product_id', 'productName', 'title', 'description', 'first_category_name', 'imageUrl'])\n", "\n", "# 尝试读取已有的结果文件\n", "try:\n", "    processed_df = pd.read_csv('product_scripts_generation.csv')\n", "except FileNotFoundError:\n", "    processed_df = pd.DataFrame(columns=['product_id', 'productName', 'title', 'description', 'first_category_name', 'imageUrl', 'result'])\n", "\n", "# 找出尚未处理的项目\n", "unprocessed_df = new_df.merge(processed_df, on=['product_id', 'productName', 'title', 'description', 'first_category_name', 'imageUrl'], how='left', indicator=True)\n", "unprocessed_df = unprocessed_df[unprocessed_df['_merge'] == 'left_only'].drop(columns=['_merge', 'result'])\n", "\n", "# 定义处理每一行的异步函数，并添加错误处理逻辑\n", "def process_row(row):\n", "    product = ProductInfo(\n", "        product_name=row['productName'],\n", "        product_desc=row['description'],\n", "        category=row['first_category_name']\n", "    )\n", "    print(product)\n", "    # 尝试第一次运行\n", "    result_code, result = product_generate(product, industry_strategy_dict, category_industry_dict, agent)\n", "\n", "    # 如果返回值是 None，再尝试一次\n", "    if result_code is None:\n", "        result_code, result = product_generate(product, industry_strategy_dict, category_industry_dict, agent)\n", "\n", "    if result_code == 1:\n", "        result = {\n", "            \"productName\": f\"{product.product_name}\",\n", "            \"firstCategoryName\": f\"{product.category}\",\n", "            \"scripts\": result['scripts']\n", "            \n", "        }\n", "        \n", "        return result\n", "\n", "    return None\n", "\n", "# 初始化要添加的列\n", "unprocessed_df['result'] = None\n", "\n", "# 对每一行调用 product_generate 并将结果存储到新列中\n", "def main():\n", "    for index, row in unprocessed_df.iterrows():\n", "        result = process_row(row)\n", "        unprocessed_df.at[index, 'result'] = result\n", "\n", "# 运行事件循环\n", "try:\n", "    main()\n", "except Exception as e:\n", "    print(e)\n", "\n", "# 将新结果合并到处理后的 DataFrame 中\n", "final_df = pd.concat([processed_df, unprocessed_df])\n", "\n", "# 保存处理后的数据到一个新的或覆盖已有的 CSV 文件\n", "final_df.to_csv('product_scripts_generation.csv', index=False)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country_code</th>\n", "      <th>product_id</th>\n", "      <th>product_name</th>\n", "      <th>product_desc</th>\n", "      <th>first_category_name</th>\n", "      <th>scripts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>US</td>\n", "      <td>1729747757054266086</td>\n", "      <td>Phones &amp; Tablets</td>\n", "      <td>Tablet Spark pro 256GB (PremiumCopy Not origin...</td>\n", "      <td>Phones &amp; Electronics</td>\n", "      <td>[{'title': 'Direct Call to Action', 'text': 'D...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>US</td>\n", "      <td>1729401766432509682</td>\n", "      <td>Women's Tops</td>\n", "      <td>Perfect for those cosy nights in, this brand n...</td>\n", "      <td>Womenswear &amp; Underwear</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'I ab...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>US</td>\n", "      <td>1729725741624757117</td>\n", "      <td>Girls' Clothes</td>\n", "      <td>Children's size : , Can fit to L-XL-XXL , bust...</td>\n", "      <td>Kids' Fashion</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'I re...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>US</td>\n", "      <td>1729402081902956147</td>\n", "      <td>Indoor Furniture</td>\n", "      <td>This Liverpool FC - Defender chair is an absol...</td>\n", "      <td>Furniture</td>\n", "      <td>[{'title': 'Emphasizing Product Features', 'te...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>US</td>\n", "      <td>1729403389778497755</td>\n", "      <td>Car Interior Accessories</td>\n", "      <td>Transform your daily commute with the Hanging ...</td>\n", "      <td>Automotive &amp; Motorcycle</td>\n", "      <td>[{'title': 'Direct Call to Action', 'text': 'U...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>US</td>\n", "      <td>1729632152206608990</td>\n", "      <td>Costume Jewelry &amp; Accessories</td>\n", "      <td>NaN</td>\n", "      <td>Fashion Accessories</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'I ab...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>US</td>\n", "      <td>1729487269542073046</td>\n", "      <td>Canvas Bucket Bag</td>\n", "      <td>This Pre-Owned MCM VISETOS ESSENTIAL DRAWSTRIN...</td>\n", "      <td>Pre-Owned</td>\n", "      <td>[{'title': 'Emphasizing Product Features', 'te...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>US</td>\n", "      <td>1729700123834747299</td>\n", "      <td>Lights &amp; Lighting</td>\n", "      <td>NaN</td>\n", "      <td>Home Improvement</td>\n", "      <td>[{'title': 'Emphasizing Product Features', 'te...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>US</td>\n", "      <td>1729385781638632124</td>\n", "      <td>Sport &amp; Outdoor Clothing</td>\n", "      <td>Messi Inter Miami soccer Jersey : , * Our socc...</td>\n", "      <td>Sports &amp; Outdoor</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'I've...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>US</td>\n", "      <td>1729428580464625852</td>\n", "      <td>Women's Underwear</td>\n", "      <td>(random color)Power Belt , Dear buyers, please...</td>\n", "      <td>Womenswear &amp; Underwear</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'I we...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  country_code           product_id                   product_name  \\\n", "0           US  1729747757054266086               Phones & Tablets   \n", "1           US  1729401766432509682                   Women's Tops   \n", "2           US  1729725741624757117                 Girls' Clothes   \n", "3           US  1729402081902956147               Indoor Furniture   \n", "4           US  1729403389778497755       Car Interior Accessories   \n", "5           US  1729632152206608990  Costume Jewelry & Accessories   \n", "6           US  1729487269542073046              Canvas Bucket Bag   \n", "7           US  1729700123834747299              Lights & Lighting   \n", "8           US  1729385781638632124       Sport & Outdoor Clothing   \n", "9           US  1729428580464625852              Women's Underwear   \n", "\n", "                                        product_desc      first_category_name  \\\n", "0  Tablet Spark pro 256GB (PremiumCopy Not origin...     Phones & Electronics   \n", "1  Perfect for those cosy nights in, this brand n...   Womenswear & Underwear   \n", "2  Children's size : , Can fit to L-XL-XXL , bust...            Kids' Fashion   \n", "3  This Liverpool FC - Defender chair is an absol...                Furniture   \n", "4  Transform your daily commute with the Hanging ...  Automotive & Motorcycle   \n", "5                                                NaN      Fashion Accessories   \n", "6  This Pre-Owned MCM VISETOS ESSENTIAL DRAWSTRIN...                Pre-Owned   \n", "7                                                NaN         Home Improvement   \n", "8  Messi Inter Miami soccer Jersey : , * Our socc...         Sports & Outdoor   \n", "9  (random color)Power Belt , Dear buyers, please...   Womenswear & Underwear   \n", "\n", "                                             scripts  \n", "0  [{'title': 'Direct Call to Action', 'text': 'D...  \n", "1  [{'title': 'Personal Testimony', 'text': 'I ab...  \n", "2  [{'title': 'Personal Testimony', 'text': 'I re...  \n", "3  [{'title': 'Emphasizing Product Features', 'te...  \n", "4  [{'title': 'Direct Call to Action', 'text': 'U...  \n", "5  [{'title': 'Personal Testimony', 'text': 'I ab...  \n", "6  [{'title': 'Emphasizing Product Features', 'te...  \n", "7  [{'title': 'Emphasizing Product Features', 'te...  \n", "8  [{'title': 'Personal Testimony', 'text': 'I've...  \n", "9  [{'title': 'Personal Testimony', 'text': 'I we...  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df_output = pd.read_csv('final_extracted_data_sample_new.csv')\n", "df_output['result'] = df_output['result'].apply(lambda x: eval(x)['scripts'])\n", "df_output['country_code'] = 'US'\n", "df_output = df_output[['country_code', 'product_id', 'productName', 'description', 'first_category_name', 'result']]\n", "df_output.rename(columns={'productName': 'product_name', 'description': 'product_desc', 'result': 'scripts'}, inplace=True)\n", "df_output.to_csv('product_scripts_generation_final.csv', index=False)\n", "df_output.head(10)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# demo"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_id</th>\n", "      <th>product_name</th>\n", "      <th>product_desc</th>\n", "      <th>first_category_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1729506646002340055</td>\n", "      <td>Liquid I.V. Hydration Multiplier - Cotton Cand...</td>\n", "      <td>&lt;p&gt;​Experience faster hydration than water alo...</td>\n", "      <td>Food &amp; Beverages</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1729532679317196840</td>\n", "      <td>Deep Relief Acne Treatment with Salicylic Acid</td>\n", "      <td>&lt;p&gt;&lt;strong&gt;Powerful phytosteroid treatment hea...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1729407744514560009</td>\n", "      <td>The Acne Set</td>\n", "      <td>&lt;p&gt;A 3-step regimen with Salicylic Acid 2% Sol...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1729526668992418038</td>\n", "      <td>Benetint &amp; High Beam Jelly Donut Value Set Mak...</td>\n", "      <td>&lt;p&gt;&lt;strong&gt;Getting a rosy glow has never been ...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1729552281366401270</td>\n", "      <td>Benefit Cosmetics Benetint Liquid Lip + Cheek ...</td>\n", "      <td>&lt;p&gt;Whether you’re hitting the Benemart for all...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1729654733226152750</td>\n", "      <td>Fenty Beauty Gloss Bomb Lip Luminizer Duo Shim...</td>\n", "      <td>&lt;p&gt;&lt;strong&gt;*Only on TikTok*&lt;/strong&gt;&lt;/p&gt;&lt;br&gt;&lt;p...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1729417129*********</td>\n", "      <td>detox + repair anywhere (detox shampoo, hair m...</td>\n", "      <td>&lt;p&gt;&lt;strong&gt;$43 Value&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;Get maximu...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            product_id                                       product_name  \\\n", "0  1729506646002340055  Liquid I.V. Hydration Multiplier - Cotton Cand...   \n", "1  1729532679317196840     Deep Relief Acne Treatment with Salicylic Acid   \n", "2  1729407744514560009                                       The Acne Set   \n", "3  1729526668992418038  Benetint & High Beam Jelly Donut Value Set Mak...   \n", "4  1729552281366401270  Benefit Cosmetics Benetint Liquid Lip + Cheek ...   \n", "5  1729654733226152750  Fenty Beauty Gloss Bomb Lip Luminizer Duo Shim...   \n", "6  1729417129*********  detox + repair anywhere (detox shampoo, hair m...   \n", "\n", "                                        product_desc     first_category_name  \n", "0  <p>​Experience faster hydration than water alo...        Food & Beverages  \n", "1  <p><strong>Powerful phytosteroid treatment hea...  Beauty & Personal Care  \n", "2  <p>A 3-step regimen with Salicylic Acid 2% Sol...  Beauty & Personal Care  \n", "3  <p><strong>Getting a rosy glow has never been ...  Beauty & Personal Care  \n", "4  <p>Whether you’re hitting the Benemart for all...  Beauty & Personal Care  \n", "5  <p><strong>*Only on TikTok*</strong></p><br><p...  Beauty & Personal Care  \n", "6  <p><strong>$43 Value</strong></p><p>Get maximu...  Beauty & Personal Care  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import json\n", "import asyncio\n", "\n", "# 读取CSV文件\n", "data_df = pd.read_csv('./data/product_demo.csv')\n", "data_df"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ProductInfo(product_name=Liquid I.V. Hydration Multiplier - Cotton Candy - Hydration Powder Packets | Electrolyte Powder Drink Mix | Convenient Single-Serving Sticks | Non-GMO | 1 Pack (16 Servings), product_desc=<p>​Experience faster hydration than water alone from the <strong>most requested flavor <em>ever</em></strong>. Taste notes of vanilla flavor and swirls of soft sweetness as you hydrate in pink and blue. It’s whimsical, airy, melt-worthy hydration with 3x the electrolytes of the leading sports drink.</p><br><ul><li><strong>HYDRATION MIX:</strong> A great-tasting, non-GMO electrolyte drink mix that delivers hydration faster than water alone.&nbsp;​</li><li><strong>GREAT TASTE:</strong> Cotton Candy <em>has notes of vanilla flavor and swirls of soft sweetness.</em></li><li><strong>SCIENCE-BACKED FORMULA:</strong> Powered by LIV HYDRASCIENCE, Liquid I.V. is designed with an optimized ratio of electrolytes and clinically tested nutrients.&nbsp;​</li><li><strong>EXTRAORDINARY HYDRATION:</strong> Hydration Multiplier has 3x the electrolytes of the leading sports drink, and 8 vitamins and nutrients. It contains 100% of the daily value of 4 B vitamins: B3, B5, B6 and B12. Gluten-free, soy-free, and dairy-free.&nbsp;​</li><li><strong>CONVENIENCE:</strong> Single-serving, travel-friendly packets are easy to enjoy on the go. Pour one easy-to-open packet into 16 oz of water, mix or shake, and hydrate.&nbsp;​</li><li><strong>HYDRATION AID:</strong> Since 2015, we have donated over 55 million servings of Liquid I.V. to those in need around the globe, and donate over 1% of our revenue to our Impact programs.&nbsp;​</li></ul><br><p><em>Liquid I.V. is not intended to diagnose, treat, cure, or prevent any disease or health condition.</em></p>, category=Food & Beverages)\n", "ProductInfo(product_name=Deep Relief Acne Treatment with Salicylic Acid, product_desc=<p><strong>Powerful phytosteroid treatment heals deep, uncomfortable acne—without the side effects of steroid shots or prescription pills</strong></p><br><ul><li>An invisible film forming polymer creates a “second skin” to hold the actives in place longer on the skin for better penetration</li><li>Amino acid technology combined with max-strength 2% salicylic acid targets the “bad bacteria” (c. acnes) that triggers among the most severe types of acne</li></ul><br><p>When the pain and swelling of deep bumps are more than you can bear—both physically and psychologically (since deep blemishes can have strong negative psychosocial effects)—but a cortisone steroid shot at a dermatologist’s office is not in the cards, reach for our powerful leave-on treatment that’s stacked with ingredients like salicylic acid for quick and long-term relief</p><br><p>The best part? It's all backed by ground cherry extract, clinically proven to deliver a clearer, healthier-looking complexion</p><br><p><strong>Which skin type is it good for?</strong></p><ul><li>Balanced</li><li>Oily</li><li>Combination</li><li>Dry</li></ul>, category=Beauty & Personal Care)\n", "ProductInfo(product_name=The Acne Set, product_desc=<p>A 3-step regimen with Salicylic Acid 2% Solution for clearer skin</p><br><p><strong>Glucoside Foaming Cleanser</strong>-50ml</p><p><strong>Salicylic Acid 2% Solution</strong>-30ml</p><p><strong>Natural Moisturizing Factors + Beta Glucan</strong>-30ml</p><br><p><strong>Suited to</strong></p><p>Acne-Prone Skin</p>, category=Beauty & Personal Care)\n", "ProductInfo(product_name=Benetint & High Beam Jelly Donut Value Set Makeup Cosmetic, product_desc=<p><strong>Getting a rosy glow has never been sweeter</strong></p><br><p>Treat your cheeks to a rosy-pearl glow! Get the Jelly Donut look by drawing a donut onto cheeks with High Beam, adding a dot of Benetint to the center for that juicy jelly filling, and blending to bake. ($52 value!)</p><br><img src=\"https://p16-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/d3549c646c604c5d8a90fbce5c667905~tplv-omjb5zjo8w-origin-jpeg.jpeg?from=*********\" width=\"2000\" height=\"2000\">, category=Beauty & Personal Care)\n", "ProductInfo(product_name=Benefit Cosmetics Benetint Liquid Lip + Cheek Blush Stain with Wearable Carry Case, product_desc=<p>Whether you’re hitting the Benemart for all your gifting needs or stocking up on your own last-</p><p>minute stuff, strutting down the aisles in Benefit style is a holiday must. And there’s no better way to do it than with a limited-edition Benetint (maybe you’ve heard of it?), in a wearable, mirrored case! Swing it over your shoulder, and you’ll be ready to tint your lips & cheeks with a rosy, natural-looking flush, wherever you are. Go ahead, add to cart.</p><br><p>WHAT’S IN IT</p><p>• Benetint rose-tinted lip & cheek stain |full-size</p><br><p>HOT TIP</p><p>• Dot on high points of cheeks & temples for a just-spent-the-day-outside flush.</p><br><p>GET IT ON</p><p>• Glide across lips.</p><p>• Keep layering to deepen color.</p><p>• Dot onto cheeks and blend in quickly.</p><br><img src=\"https://p16-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/39591e04af314acf83e01b2d7e24c1e9~tplv-omjb5zjo8w-origin-jpeg.jpeg?from=*********\" width=\"8333\" height=\"8333\">, category=Beauty & Personal Care)\n", "ProductInfo(product_name=Fenty Beauty Gloss Bomb Lip Luminizer Duo Shimmer Lip Gloss, product_desc=<p><strong>*Only on TikTok*</strong></p><br><p><strong>ADDICTIVE SHINE, NOURISHING WEAR.</strong></p><p><strong>UNIVERSAL FINISHING TOUCH.</strong></p><br><p>Your duo includes:</p><ul><li>Gloss Bomb in ‘Fuchsia Flex,’ a shimmering holographic deep fuchsia lip gloss</li><li>Gloss Bomb in ‘RiRi,’ a shimmering rose mauve nude lip gloss</li></ul><br><p>Get <PERSON><PERSON><PERSON>’s gotta-have-it-gloss. Our bestselling Gloss Bomb formula brings incredible shine + brilliant shimmer with every swipe. Secure fam-fave shades that never feel sticky and are always serving.</p><br><p>One luscious swipe of Gloss Bomb’s XXL wand gives lips more to love, while conditioning shea butter enriches from within. Wearing Gloss Bomb makes lips look instantly fuller, with a non-sticky formula that’s super shiny and has an addictive peach-vanilla scent you just can’t get enough of.</p><br><p><strong>Fenty Beauty is 100% cruelty free. | Fill Weight:</strong> 0.3 oz/ 9 mL</p>, category=Beauty & Personal Care)\n", "ProductInfo(product_name=detox + repair anywhere (detox shampoo, hair mask), product_desc=<p><strong>$43 Value</strong></p><p>Get maximum damage repair with this mini detox + repair duo.</p><ul><li>Mini PEPTIDE PREP™ detox shampoo removes buildup + metals, clearing the path for maximum molecular repair results.</li><li>Award-winning mini leave-in molecular repair hair mask repairs hair damage from bleach*, color*, chemical services*, and heat* in just 4 minutes.</li></ul><p>*Disclaimers</p>, category=Beauty & Personal Care)\n"]}], "source": ["# 定义处理每一行的异步函数，并添加错误处理逻辑\n", "def process_row_new(row):\n", "    product = ProductInfo(\n", "        product_name=row['product_name'],\n", "        product_desc=row['product_desc'],\n", "        category=row['first_category_name']\n", "    )\n", "    print(product)\n", "    # 尝试第一次运行\n", "    result_code, result = product_generate(product, industry_strategy_dict, category_industry_dict, agent)\n", "\n", "    # 如果返回值是 None，再尝试一次\n", "    if result_code is None:\n", "        result_code, result = product_generate(product, industry_strategy_dict, category_industry_dict, agent)\n", "\n", "    if result_code == 1:\n", "        return result\n", "\n", "    return None\n", "\n", "results_list = []\n", "for index, row in data_df.iterrows():\n", "    result = process_row_new(row)\n", "    results_list.append(result)\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["data_df['scripts'] = [result['scripts'] for result in results_list]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_id</th>\n", "      <th>product_name</th>\n", "      <th>product_desc</th>\n", "      <th>first_category_name</th>\n", "      <th>scripts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1729506646002340055</td>\n", "      <td>Liquid I.V. Hydration Multiplier - Cotton Cand...</td>\n", "      <td>&lt;p&gt;​Experience faster hydration than water alo...</td>\n", "      <td>Food &amp; Beverages</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1729532679317196840</td>\n", "      <td>Deep Relief Acne Treatment with Salicylic Acid</td>\n", "      <td>&lt;p&gt;&lt;strong&gt;Powerful phytosteroid treatment hea...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1729407744514560009</td>\n", "      <td>The Acne Set</td>\n", "      <td>&lt;p&gt;A 3-step regimen with Salicylic Acid 2% Sol...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1729526668992418038</td>\n", "      <td>Benetint &amp; High Beam Jelly Donut Value Set Mak...</td>\n", "      <td>&lt;p&gt;&lt;strong&gt;Getting a rosy glow has never been ...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1729552281366401270</td>\n", "      <td>Benefit Cosmetics Benetint Liquid Lip + Cheek ...</td>\n", "      <td>&lt;p&gt;Whether you’re hitting the Benemart for all...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1729654733226152750</td>\n", "      <td>Fenty Beauty Gloss Bomb Lip Luminizer Duo Shim...</td>\n", "      <td>&lt;p&gt;&lt;strong&gt;*Only on TikTok*&lt;/strong&gt;&lt;/p&gt;&lt;br&gt;&lt;p...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1729417129*********</td>\n", "      <td>detox + repair anywhere (detox shampoo, hair m...</td>\n", "      <td>&lt;p&gt;&lt;strong&gt;$43 Value&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;Get maximu...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'Hey ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            product_id                                       product_name  \\\n", "0  1729506646002340055  Liquid I.V. Hydration Multiplier - Cotton Cand...   \n", "1  1729532679317196840     Deep Relief Acne Treatment with Salicylic Acid   \n", "2  1729407744514560009                                       The Acne Set   \n", "3  1729526668992418038  Benetint & High Beam Jelly Donut Value Set Mak...   \n", "4  1729552281366401270  Benefit Cosmetics Benetint Liquid Lip + Cheek ...   \n", "5  1729654733226152750  Fenty Beauty Gloss Bomb Lip Luminizer Duo Shim...   \n", "6  1729417129*********  detox + repair anywhere (detox shampoo, hair m...   \n", "\n", "                                        product_desc     first_category_name  \\\n", "0  <p>​Experience faster hydration than water alo...        Food & Beverages   \n", "1  <p><strong>Powerful phytosteroid treatment hea...  Beauty & Personal Care   \n", "2  <p>A 3-step regimen with Salicylic Acid 2% Sol...  Beauty & Personal Care   \n", "3  <p><strong>Getting a rosy glow has never been ...  Beauty & Personal Care   \n", "4  <p>Whether you’re hitting the Benemart for all...  Beauty & Personal Care   \n", "5  <p><strong>*Only on TikTok*</strong></p><br><p...  Beauty & Personal Care   \n", "6  <p><strong>$43 Value</strong></p><p>Get maximu...  Beauty & Personal Care   \n", "\n", "                                             scripts  \n", "0  [{'title': 'Personal Testimony', 'text': 'Hey ...  \n", "1  [{'title': 'Personal Testimony', 'text': 'Hey ...  \n", "2  [{'title': 'Personal Testimony', 'text': 'Hey ...  \n", "3  [{'title': 'Personal Testimony', 'text': 'Hey ...  \n", "4  [{'title': 'Personal Testimony', 'text': 'Hey ...  \n", "5  [{'title': 'Personal Testimony', 'text': 'Hey ...  \n", "6  [{'title': 'Personal Testimony', 'text': 'Hey ...  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["data_df = data_df.drop(['result'], axis = 1)\n", "data_df"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>product_id</th>\n", "      <th>product_name</th>\n", "      <th>product_desc</th>\n", "      <th>first_category_name</th>\n", "      <th>scripts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1729506646002340055</td>\n", "      <td>Liquid I.V. Hydration Multiplier - Cotton Cand...</td>\n", "      <td>​Experience faster hydration than water alone ...</td>\n", "      <td>Food &amp; Beverages</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': \"Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1729532679317196840</td>\n", "      <td>Deep Relief Acne Treatment with Salicylic Acid</td>\n", "      <td>Powerful phytosteroid treatment heals deep, un...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': \"Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1729407744514560009</td>\n", "      <td>The Acne Set</td>\n", "      <td>A 3-step regimen with Salicylic Acid 2% Soluti...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': \"Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1729526668992418038</td>\n", "      <td>Benetint &amp; High Beam Jelly Donut Value Set Mak...</td>\n", "      <td>Getting a rosy glow has never been sweeter\\nTr...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': \"Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1729552281366401270</td>\n", "      <td>Benefit Cosmetics Benetint Liquid Lip + Cheek ...</td>\n", "      <td>Whether you’re hitting the Benemart for all yo...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': \"Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>1729654733226152750</td>\n", "      <td>Fenty Beauty Gloss Bomb Lip Luminizer Duo Shim...</td>\n", "      <td>*Only on TikTok*\\nADDICTIVE SHINE, NOURISHING ...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'Hey ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>1729417129*********</td>\n", "      <td>detox + repair anywhere (detox shampoo, hair m...</td>\n", "      <td>$43 Value\\nGet maximum damage repair with this...</td>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'Hey ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            product_id                                       product_name  \\\n", "0  1729506646002340055  Liquid I.V. Hydration Multiplier - Cotton Cand...   \n", "1  1729532679317196840     Deep Relief Acne Treatment with Salicylic Acid   \n", "2  1729407744514560009                                       The Acne Set   \n", "3  1729526668992418038  Benetint & High Beam Jelly Donut Value Set Mak...   \n", "4  1729552281366401270  Benefit Cosmetics Benetint Liquid Lip + Cheek ...   \n", "5  1729654733226152750  Fenty Beauty Gloss Bomb Lip Luminizer Duo Shim...   \n", "6  1729417129*********  detox + repair anywhere (detox shampoo, hair m...   \n", "\n", "                                        product_desc     first_category_name  \\\n", "0  ​Experience faster hydration than water alone ...        Food & Beverages   \n", "1  Powerful phytosteroid treatment heals deep, un...  Beauty & Personal Care   \n", "2  A 3-step regimen with Salicylic Acid 2% Soluti...  Beauty & Personal Care   \n", "3  Getting a rosy glow has never been sweeter\\nTr...  Beauty & Personal Care   \n", "4  Whether you’re hitting the Benemart for all yo...  Beauty & Personal Care   \n", "5  *Only on TikTok*\\nADDICTIVE SHINE, NOURISHING ...  Beauty & Personal Care   \n", "6  $43 Value\\nGet maximum damage repair with this...  Beauty & Personal Care   \n", "\n", "                                             scripts  \n", "0  [{'title': 'Personal Testimony', 'text': \"Hey ...  \n", "1  [{'title': 'Personal Testimony', 'text': \"Hey ...  \n", "2  [{'title': 'Personal Testimony', 'text': \"Hey ...  \n", "3  [{'title': 'Personal Testimony', 'text': \"Hey ...  \n", "4  [{'title': 'Personal Testimony', 'text': \"Hey ...  \n", "5  [{'title': 'Personal Testimony', 'text': 'Hey ...  \n", "6  [{'title': 'Personal Testimony', 'text': 'Hey ...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import re\n", "data_df = pd.read_csv(\"./output/product_demo.csv\")\n", "def clean_product_desc(prod_desc):\n", "    prod_desc_lines = re.sub('<.+?>', '\\n', prod_desc).split('\\n')\n", "    product_description = '\\n'.join([line for line in prod_desc_lines if len(line) > 0]).strip()\n", "    return product_description\n", "data_df['product_desc'] = data_df['product_desc'].apply(clean_product_desc)\n", "display(data_df)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["data_df.to_csv(\"./output/product_demo.csv\", index = None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}