{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import openai\n", "from tqdm import tqdm\n", "import time"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class OpenAIAgent:\n", "    def __init__(self, api_key, model=\"gpt-4-0613\"):\n", "        self.client = openai.AzureOpenAI(\n", "            azure_endpoint=\"https://search-va.byteintl.net/gpt/openapi/online/v2/crawl\",\n", "            api_version=\"2024-03-01-preview\",\n", "            api_key=api_key\n", "        )\n", "        self.model = model\n", "\n", "    def system_message(self, message):\n", "        return message\n", "\n", "    def assistant_message(self, message):\n", "        return message\n", "\n", "    def user_message(self, message):\n", "        return message\n", "\n", "    @staticmethod\n", "    def clean_json_output(output):\n", "        output = output.strip()\n", "        if output.startswith(\"```json\"):\n", "            output = output[7:]\n", "        if output.endswith(\"```\"):\n", "            output = output[:-3]\n", "        cleaned_output = output.strip()\n", "\n", "        try:\n", "            json_data = json.loads(cleaned_output)\n", "        except json.JSONDecodeError as e:\n", "            print(f\"JSON decoding error: {e}\")\n", "            return cleaned_output\n", "\n", "        def clean_json(data):\n", "            if isinstance(data, dict):\n", "                return {key: clean_json(value) for key, value in data.items()}\n", "            elif isinstance(data, list):\n", "                return [clean_json(item) for item in data]\n", "            elif isinstance(data, str):\n", "                return \"\" if data.lower() in [\"unknown\", \"na\", \"null\"] else data\n", "            else:\n", "                return data\n", "\n", "        cleaned_json_data = clean_json(json_data)\n", "        return cleaned_json_data\n", "\n", "    def run_openai_task(self, system_message, assistant_message, user_message):\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": self.system_message(system_message)},\n", "            {\"role\": \"assistant\", \"content\": self.assistant_message(assistant_message)},\n", "            {\"role\": \"user\", \"content\": self.user_message(user_message)}\n", "        ]\n", "\n", "        completion = self.client.chat.completions.create(\n", "            extra_headers={\"X-TT-LOGID\": \"abc\"},  # 请务必带上此header，方便定位问题\n", "            model=self.model,\n", "            messages=messages,\n", "            temperature=0,\n", "            frequency_penalty=0,\n", "            presence_penalty=0\n", "        )\n", "\n", "        json_data = completion.model_dump_json()\n", "        data_dict = json.loads(json_data)\n", "\n", "        result = self.clean_json_output(data_dict['choices'][0]['message']['content'])\n", "        usage_data = data_dict.get('usage', {})\n", "        total_tokens = usage_data.get('total_tokens', 0)\n", "        prompt_tokens = usage_data.get('prompt_tokens', 0)\n", "        completion_tokens = usage_data.get('completion_tokens', 0)\n", "\n", "        return result, total_tokens, prompt_tokens, completion_tokens"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["agent = OpenAIAgent(api_key=\"X3zrNBdXZ1wQS1EnqMXP4bkFBBMELMPB\", model = 'gpt-4o-2024-08-06')\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["df_scripts =  pd.read_csv('../output/live_scripts_sea.csv')\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country_code</th>\n", "      <th>industry</th>\n", "      <th>seller_type</th>\n", "      <th>gmv_percentile_level</th>\n", "      <th>strategies</th>\n", "      <th>script_type</th>\n", "      <th>scripts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[['Personal Connection'], ['Call to Action'], ...</td>\n", "      <td>closing</td>\n", "      <td>[['<PERSON><PERSON> kasih semua kerana men<PERSON>tai live sa...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[['Personal Engagement'], ['Relatability'], ['...</td>\n", "      <td>opening</td>\n", "      <td>[['<PERSON> semua! Selamat datang ke live kita hari...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[['Clear Call to Action'], ['Personal Connecti...</td>\n", "      <td>closing</td>\n", "      <td>[['<PERSON><PERSON> kasih semua kerana menyertai sesi li...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[['Creating Urgency'], ['Personal Connection']...</td>\n", "      <td>opening</td>\n", "      <td>[['<PERSON> semua! Selamat datang ke siaran langsun...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G5</td>\n", "      <td>[['Urgency and Scarcity'], ['Personal Connecti...</td>\n", "      <td>closing</td>\n", "      <td>[['<PERSON><PERSON> kasih semua kerana menyertai live ki...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>571</th>\n", "      <td>VN</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G1</td>\n", "      <td>[['Repetition'], ['Personal Engagement'], ['In...</td>\n", "      <td>opening</td>\n", "      <td>[['<PERSON><PERSON> chào xin chào xin chào mọi người! Chào ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>572</th>\n", "      <td>VN</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G3</td>\n", "      <td>[['Expressing Gratitude'], ['Call to Action'],...</td>\n", "      <td>closing</td>\n", "      <td>[['<PERSON><PERSON><PERSON> ơn mọi người đã tham gia buổi livestrea...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>573</th>\n", "      <td>VN</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G3</td>\n", "      <td>[['Personal Engagement'], ['Enthusiastic Greet...</td>\n", "      <td>opening</td>\n", "      <td>[['<PERSON>n chào mọi người! Chào mừng các bạn đến v...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>574</th>\n", "      <td>VN</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>[['Urgency and Scarcity'], ['Expressing Gratit...</td>\n", "      <td>closing</td>\n", "      <td>[['<PERSON><PERSON><PERSON> ơn mọi người đã tham gia buổi livestrea...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>575</th>\n", "      <td>VN</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>[['Urgency and Exclusivity'], ['Engaging Greet...</td>\n", "      <td>opening</td>\n", "      <td>[['<PERSON><PERSON> chào mọi người! Hôm nay là một trong nh...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>576 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    country_code        industry seller_type gmv_percentile_level  \\\n", "0             MY          beauty   affiliate                   G1   \n", "1             MY          beauty   affiliate                   G1   \n", "2             MY          beauty   affiliate                   G3   \n", "3             MY          beauty   affiliate                   G3   \n", "4             MY          beauty   affiliate                   G5   \n", "..           ...             ...         ...                  ...   \n", "571           VN  multi-category      seller                   G1   \n", "572           VN  multi-category      seller                   G3   \n", "573           VN  multi-category      seller                   G3   \n", "574           VN  multi-category      seller                   G5   \n", "575           VN  multi-category      seller                   G5   \n", "\n", "                                            strategies script_type  \\\n", "0    [['Personal Connection'], ['Call to Action'], ...     closing   \n", "1    [['Personal Engagement'], ['Relatability'], ['...     opening   \n", "2    [['Clear Call to Action'], ['Personal Connecti...     closing   \n", "3    [['Creating Urgency'], ['Personal Connection']...     opening   \n", "4    [['Urgency and Scarcity'], ['Personal Connecti...     closing   \n", "..                                                 ...         ...   \n", "571  [['Repetition'], ['Personal Engagement'], ['In...     opening   \n", "572  [['Expressing Gratitude'], ['Call to Action'],...     closing   \n", "573  [['Personal Engagement'], ['Enthusiastic Greet...     opening   \n", "574  [['Urgency and Scarcity'], ['Expressing Gratit...     closing   \n", "575  [['Urgency and Exclusivity'], ['Engaging Greet...     opening   \n", "\n", "                                               scripts  \n", "0    [['<PERSON><PERSON> kasih semua kerana men<PERSON> live sa...  \n", "1    [['Hai semua! Selamat datang ke live kita hari...  \n", "2    [['<PERSON><PERSON> kasih semua kerana menyertai sesi li...  \n", "3    [['Hai semua! Selamat datang ke siaran langsun...  \n", "4    [['<PERSON><PERSON> kasih semua kerana men<PERSON> live ki...  \n", "..                                                 ...  \n", "571  [['<PERSON><PERSON> chào xin chào xin chào mọi người! Chào ...  \n", "572  [['<PERSON><PERSON><PERSON> ơn mọi người đã tham gia buổi livestrea...  \n", "573  [['<PERSON>n chào mọi người! Chào mừng các bạn đến v...  \n", "574  [['<PERSON><PERSON><PERSON> ơn mọi người đã tham gia buổi livestrea...  \n", "575  [['<PERSON><PERSON> chào mọi người! Hôm nay là một trong nh...  \n", "\n", "[576 rows x 7 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_scripts"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>num_vids_analysed</th>\n", "      <th>script_type</th>\n", "      <th>category</th>\n", "      <th>seller_type</th>\n", "      <th>live_gmv_percentile</th>\n", "      <th>Strategy</th>\n", "      <th>Explanation</th>\n", "      <th>Count</th>\n", "      <th>Examples</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Personal Testimony</td>\n", "      <td>Sharing personal experiences to build trust an...</td>\n", "      <td>10</td>\n", "      <td>\"i have the black one and i love it\", \"i didn'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Demonstrating Product Usage</td>\n", "      <td>Showing how the product works in real-life sce...</td>\n", "      <td>9</td>\n", "      <td>\"i'll put it on and show you what it looks lik...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Emphasizing Versatility</td>\n", "      <td>Highlighting how the product can be used in va...</td>\n", "      <td>8</td>\n", "      <td>\"ideal for a carry on or for just a shorter tr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>Personal Testimony</td>\n", "      <td>Sharing personal experiences to build trust an...</td>\n", "      <td>15</td>\n", "      <td>\"my husband uses it every day on his lunch bre...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>Highlighting Product Features</td>\n", "      <td>Emphasizing specific features of the product t...</td>\n", "      <td>10</td>\n", "      <td>\"super quiet\", \"it has round retro keys\", \"thr...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   num_vids_analysed script_type category seller_type live_gmv_percentile  \\\n", "0                 50         NaN   beauty   affiliate                  G1   \n", "1                 50         NaN   beauty   affiliate                  G1   \n", "2                 50         NaN   beauty   affiliate                  G1   \n", "3                 50         NaN   beauty   affiliate                  G3   \n", "4                 50         NaN   beauty   affiliate                  G3   \n", "\n", "                        Strategy  \\\n", "0             Personal Testimony   \n", "1    Demonstrating Product Usage   \n", "2        Emphasizing Versatility   \n", "3             Personal Testimony   \n", "4  Highlighting Product Features   \n", "\n", "                                         Explanation  Count  \\\n", "0  Sharing personal experiences to build trust an...     10   \n", "1  Showing how the product works in real-life sce...      9   \n", "2  Highlighting how the product can be used in va...      8   \n", "3  Sharing personal experiences to build trust an...     15   \n", "4  Emphasizing specific features of the product t...     10   \n", "\n", "                                            Examples  \n", "0  \"i have the black one and i love it\", \"i didn'...  \n", "1  \"i'll put it on and show you what it looks lik...  \n", "2  \"ideal for a carry on or for just a shorter tr...  \n", "3  \"my husband uses it every day on his lunch bre...  \n", "4  \"super quiet\", \"it has round retro keys\", \"thr...  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('../data/live_narrative_strategy_new.csv')\n", "\n", "df['is_seller'] = df['is_seller'].map({1: 'seller', 0: 'affiliate'})\n", "df.rename(columns={'is_seller': 'seller_type', 'narrative_type': 'script_type'}, inplace=True)\n", "df['script_type'] = df['script_type'].map({'intro': 'opening', 'ending': 'closing'})\n", "df.head()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["opening    179\n", "closing    151\n", "Name: script_type, dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df['script_type'].value_counts()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening strategies 数量: 75\n", "Closing strategies 数量: 34\n", "\n", "Opening strategies:\n", "1. Personal Engagement\n", "2. Relatability\n", "3. Demonstrating Product Use\n", "4. Personal Testimony\n", "5. <PERSON><PERSON><PERSON> Urgency\n", "6. Personal Connection\n", "7. Highlighting Discounts and Offers\n", "8. Interactive Engagement\n", "9. Audience Engagement\n", "10. Encouraging Interaction\n", "11. <PERSON> and <PERSON>l<PERSON> Greetings\n", "12. Highlighting Product Benefits and Features\n", "13. Repetition for Emphasis\n", "14. Incentivizing Participation\n", "15. Warm and Personal Greeting\n", "16. Repetition\n", "17. Highlighting Product Features and Benefits\n", "18. <PERSON> Welcome\n", "19. Building Excitement and Anticipation\n", "20. Casual and Relatable Tone\n", "21. Highlighting Promotions and Discounts\n", "22. Detailed Product Descriptions\n", "23. Casual and Relatable Language\n", "24. Highlighting Promotions and Special Offers\n", "25. Creating Excitement\n", "26. Direct Engagement\n", "27. Transparency and Authenticity\n", "28. Personalization\n", "29. <PERSON><PERSON> and Light-heartedness\n", "30. Personal Storytelling\n", "31. Call to Action\n", "32. Highlighting Product Features\n", "33. Highlighting Benefits\n", "34. Clear Call to Action\n", "35. Engaging the Audience\n", "36. Product Promotion\n", "37. Announcing Giveaways\n", "38. Personal Greetings and Engagement\n", "39. Expressing Gratitude\n", "40. Highlighting Discounts and Deals\n", "41. Highlighting Products and Features\n", "42. <PERSON> and <PERSON> Greeting\n", "43. Personal Introduction\n", "44. Personal Connection and Engagement\n", "45. Encouraging Participation and Community Building\n", "46. Building Anticipation and Curiosity\n", "47. Clear Instructions and Demonstrations\n", "48. Warm Greetings and Positive Atmosphere\n", "49. Warm and Personal Greetings\n", "50. Product Demonstration and Highlighting Features\n", "51. Highlighting Discounts and Special Offers\n", "52. Personal Engagement and Interaction\n", "53. Highlighting Special Offers and Discounts\n", "54. Personal Greetings\n", "55. Personal Greeting and Engagement\n", "56. Welcoming and Engaging the Audience\n", "57. Personal Testimony and Endorsement\n", "58. Warm Welcome and Personal Engagement\n", "59. Personal Testimony and Recommendations\n", "60. Personal Connection and Endorsement\n", "61. Audience Interaction\n", "62. Highlighting Product Benefits\n", "63. Engaging Greeting\n", "64. Showcasing Products\n", "65. Highlighting Offers and Discounts\n", "66. Demonstration\n", "67. Urgency and Scarcity\n", "68. Price and Value Highlighting\n", "69. Product Demonstration\n", "70. Repetition of Key Information\n", "71. Product Endorsement\n", "72. Enthusiastic <PERSON>reeting\n", "73. Urgency and Exclusivity\n", "74. Engaging Greetings\n", "75. Repetition and Emphasis\n", "\n", "Closing strategies:\n", "1. Personal Connection\n", "2. Call to Action\n", "3. <PERSON><PERSON> and Playfulness\n", "4. Clear Call to Action\n", "5. Urgency and Scarcity\n", "6. Expressing Gratitude\n", "7. Call to Action (CTA)\n", "8. Personal Engagement\n", "9. Teasing Future Content\n", "10. Direct Engagement with Audience\n", "11. <PERSON><PERSON><PERSON> Urgency\n", "12. Future Engagement\n", "13. Personal Acknowledgment\n", "14. Announcing Future Engagement\n", "15. Personal Interaction\n", "16. Repetition\n", "17. Casual and Relatable Language\n", "18. Product Mention and Promotion\n", "19. Personal Connection and Engagement\n", "20. Product Endorsement and Benefits\n", "21. <PERSON><PERSON><PERSON>\n", "22. Promising Future Content\n", "23. Apologizing for Technical Issues\n", "24. <PERSON>rat<PERSON>\n", "25. Product Highlight and Endorsement\n", "26. Creating Urgency and Scarcity\n", "27. Personal Greeting\n", "28. Highlighting Discounts and Offers\n", "29. Personal Testimony\n", "30. Expressing Gratitude and Acknowledgment\n", "31. Encouraging Engagement\n", "32. <PERSON><PERSON><PERSON> and Call to Action\n", "33. Gratitude Expression\n", "34. Scarcity and Urgency\n"]}], "source": ["# 提取 opening 和 closing 的 strategy 数量\n", "opening_strategies = df[df['script_type'] == 'opening']['Strategy'].dropna().unique()\n", "closing_strategies = df[df['script_type'] == 'closing']['Strategy'].dropna().unique()\n", "\n", "print(f\"Opening strategies 数量: {len(opening_strategies)}\")\n", "print(f\"Closing strategies 数量: {len(closing_strategies)}\")\n", "\n", "# 显示所有 opening strategies\n", "print(\"\\nOpening strategies:\")\n", "for i, strategy in enumerate(opening_strategies, 1):\n", "    print(f\"{i}. {strategy}\")\n", "\n", "# 显示所有 closing strategies\n", "print(\"\\nClosing strategies:\")\n", "for i, strategy in enumerate(closing_strategies, 1):\n", "    print(f\"{i}. {strategy}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "def translate_with_openai(agent, text, language_name):\n", "    \"\"\"\n", "    使用OpenAI API翻译文本\n", "    \n", "    参数:\n", "    agent: OpenAIAgent实例\n", "    text: 要翻译的文本\n", "    target_language: 目标语言代码或语言名称\n", "    \n", "    返回:\n", "    翻译后的文本\n", "    \"\"\"\n", "\n", "    # 如果目标语言是英语，不需要翻译\n", "    if language_name == 'English':\n", "        return text\n", "    \n", "    system_message = \"You are a professional translator.\"\n", "    assistant_message = \"\"\n", "    user_message = f\"Translate the following text to {language_name}. Provide only the translation without any explanations or additional text.\\n\\nText: {text}\"\n", "    \n", "    try:\n", "        result, _, _, _ = agent.run_openai_task(system_message, assistant_message, user_message)\n", "        # 如果结果是字典或列表，转换为字符串\n", "        if isinstance(result, (dict, list)):\n", "            result = json.dumps(result, ensure_ascii=False)\n", "        return result\n", "    except Exception as e:\n", "        print(f\"Translation error: {str(e)}\")\n", "        return f\"Error: {str(e)}\"\n", "\n", "# 创建结果列表并进行翻译\n", "def translate_strategies(agent):\n", "    # 定义国家和对应的语言\n", "    language_mapping = {\n", "            'US': 'English',  # English\n", "            'SG': 'English',  # English\n", "            'MY': 'Malay',  # Malay\n", "            'PH': 'Filipino', # Filipino\n", "            'TH': 'Thai',  # Thai\n", "            'ID': 'Indonesian',  # Indonesian   \n", "            'VN': 'Vietnamese'   # Vietnamese\n", "    }\n", "    \n", "    results = []\n", "    \n", "    # 处理 Opening 策略\n", "    for strategy in opening_strategies:\n", "        row = {\n", "            'strategy_type': 'opening',\n", "            'strategy_en': strategy\n", "        }\n", "        \n", "        # 为每个国家添加翻译\n", "        for country, lang_code in language_mapping.items():\n", "            try:\n", "                translation = translate_with_openai(agent, strategy, lang_code)\n", "                row[f'strategy_{country}'] = translation\n", "                time.sleep(1)  # 避免请求过快\n", "            except Exception as e:\n", "                row[f'strategy_{country}'] = f\"Error: {str(e)}\"\n", "        \n", "        results.append(row)\n", "        print(f\"Translated opening strategy: {strategy[:30]}...\")\n", "    \n", "    # 处理 Closing 策略\n", "    for strategy in closing_strategies:\n", "        row = {\n", "            'strategy_type': 'closing',\n", "            'strategy_en': strategy\n", "        }\n", "        \n", "        # 为每个国家添加翻译\n", "        for country, lang_code in language_mapping.items():\n", "            try:\n", "                translation = translate_with_openai(agent, strategy, lang_code)\n", "                row[f'strategy_{country}'] = translation\n", "                time.sleep(1)  # 避免请求过快\n", "            except Exception as e:\n", "                row[f'strategy_{country}'] = f\"Error: {str(e)}\"\n", "        \n", "        results.append(row)\n", "        print(f\"Translated closing strategy: {strategy[:30]}...\")\n", "    \n", "    # 创建 DataFrame\n", "    df = pd.DataFrame(results)\n", "    \n", "    # 保存到 CSV 文件\n", "    df.to_csv('../output/strategy_translations_openai.csv', index=False, encoding='utf-8-sig')\n", "    \n", "    print(\"翻译完成并保存到 strategy_translations_openai.csv\")\n", "    \n", "    # 显示前几行结果\n", "    print(\"\\n--- 结果示例 ---\")\n", "    print(df.head())\n", "    \n", "    return df"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Engagement...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Relatability...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Demonstrating Product Use...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Testimony...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Creating Urgency...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Connection...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Discounts and Off...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Interactive Engagement...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Audience Engagement...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Encouraging Interaction...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Warm and Welcoming Greetings...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Product Benefits ...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Repetition for Emphasis...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Incentivizing Participation...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Warm and Personal Greeting...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Repetition...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Product Features ...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Warm Welcome...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Building Excitement and Antici...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Casual and Relatable Tone...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Promotions and Di...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Detailed Product Descriptions...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Casual and Relatable Language...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Promotions and Sp...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Creating Excitement...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Direct Engagement...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Transparency and Authenticity...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personalization...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Humor and Light-heartedness...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Storytelling...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Call to Action...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Product Features...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Benefits...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Clear Call to Action...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Engaging the Audience...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Product Promotion...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Announcing Giveaways...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Greetings and Engagem...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Expressing Gratitude...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Discounts and Dea...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Products and Feat...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Warm and Friendly Greeting...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Introduction...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Connection and Engage...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Encouraging Participation and ...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Building Anticipation and Curi...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Clear Instructions and Demonst...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Warm Greetings and Positive At...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Warm and Personal Greetings...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Product Demonstration and High...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Discounts and Spe...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Engagement and Intera...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Special Offers an...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Greetings...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Greeting and Engageme...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Welcoming and Engaging the Aud...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Testimony and Endorse...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Warm Welcome and Personal Enga...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Testimony and Recomme...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Personal Connection and Endors...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Audience Interaction...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Product Benefits...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Engaging Greeting...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Showcasing Products...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Highlighting Offers and Discou...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Demonstration...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Urgency and Scarcity...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Price and Value Highlighting...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Product Demonstration...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Repetition of Key Information...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Product Endorsement...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Enthusiastic Greeting...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Urgency and Exclusivity...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Engaging Greetings...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated opening strategy: Repetition and Emphasis...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Personal Connection...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Call to Action...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Humor and Playfulness...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Clear Call to Action...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Urgency and Scarcity...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Expressing Gratitude...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Call to Action (CTA)...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Personal Engagement...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Teasing Future Content...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Direct Engagement with Audienc...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Creating Urgency...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Future Engagement...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Personal Acknowledgment...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Announcing Future Engagement...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Personal Interaction...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Repetition...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Casual and Relatable Language...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Product Mention and Promotion...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Personal Connection and Engage...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Product Endorsement and Benefi...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Urgency...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Promising Future Content...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Apologizing for Technical Issu...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Gratitude...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Product Highlight and Endorsem...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Creating Urgency and Scarcity...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Personal Greeting...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Highlighting Discounts and Off...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Personal Testimony...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Expressing Gratitude and Ackno...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Encouraging Engagement...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Urgency and Call to Action...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Gratitude Expression...\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "JSON decoding error: Expecting value: line 1 column 1 (char 0)\n", "Translated closing strategy: Scarcity and Urgency...\n", "翻译完成并保存到 strategy_translations_openai.csv\n", "\n", "--- 结果示例 ---\n", "  strategy_type                strategy_en                strategy_US  \\\n", "0       opening        Personal Engagement        Personal Engagement   \n", "1       opening               Relatability               Relatability   \n", "2       opening  Demonstrating Product Use  Demonstrating Product Use   \n", "3       opening         Personal Testimony         Personal Testimony   \n", "4       opening           Creating Urgency           Creating Urgency   \n", "\n", "                 strategy_SG                    strategy_MY  \\\n", "0        Personal Engagement           Penglibatan Peribadi   \n", "1               Relatability                Kebolehhubungan   \n", "2  Demonstrating Product Use  Menunjukkan Penggunaan Produk   \n", "3         Personal Testimony             <PERSON><PERSON><PERSON><PERSON>   \n", "4           Creating Urgency  Me<PERSON><PERSON><PERSON><PERSON> Mendesak   \n", "\n", "                             strategy_PH              strategy_TH  \\\n", "0                Personal na Pakikilahok   การมีส่วนร่วมส่วนบุคคล   \n", "1                           Pagkakaugnay             ความสัมพันธ์   \n", "2    Pagpapakita ng Paggamit ng Produkto  การสาธิตการใช้ผลิตภัณฑ์   \n", "3                    Personal na Patotoo          คำให้การส่วนตัว   \n", "4  Paglikha ng Kagyat na Pangangailangan        สร้างความเร่งด่วน   \n", "\n", "                     strategy_ID                      strategy_VN  \n", "0           Keterlibatan Pribadi              Sự Tham Gia Cá Nhân  \n", "1                  Keterhubungan                 Khả năng liên hệ  \n", "2  Menunjukkan <PERSON>an Produk  <PERSON> b<PERSON>y cách sử dụng sản phẩm  \n", "3              Kesaksian Pribadi                Lời Chứng Cá Nhân  \n", "4            Menciptakan Urgensi                  T<PERSON> s<PERSON> cấp bách  \n"]}], "source": ["df_translate = translate_strategies(agent)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>strategy_type</th>\n", "      <th>strategy_en</th>\n", "      <th>strategy_US</th>\n", "      <th>strategy_SG</th>\n", "      <th>strategy_MY</th>\n", "      <th>strategy_PH</th>\n", "      <th>strategy_TH</th>\n", "      <th>strategy_ID</th>\n", "      <th>strategy_VN</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>opening</td>\n", "      <td>Personal Engagement</td>\n", "      <td>Personal Engagement</td>\n", "      <td>Personal Engagement</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Personal na Pakikilahok</td>\n", "      <td>การมีส่วนร่วมส่วนบุคคล</td>\n", "      <td>Keterlibatan Pribadi</td>\n", "      <td><PERSON><PERSON>ham <PERSON>á N<PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>opening</td>\n", "      <td>Relatability</td>\n", "      <td>Relatability</td>\n", "      <td>Relatability</td>\n", "      <td>Kebolehhubungan</td>\n", "      <td>Pagkakaugnay</td>\n", "      <td>ความสัมพันธ์</td>\n", "      <td>Keterhubungan</td>\n", "      <td><PERSON><PERSON><PERSON> năng liên hệ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>opening</td>\n", "      <td>Demonstrating Product Use</td>\n", "      <td>Demonstrating Product Use</td>\n", "      <td>Demonstrating Product Use</td>\n", "      <td>Men<PERSON><PERSON>kka<PERSON> Pen<PERSON>an Produk</td>\n", "      <td>Pagpapakita ng Paggamit ng Produkto</td>\n", "      <td>การสาธิตการใช้ผลิตภัณฑ์</td>\n", "      <td>Men<PERSON><PERSON>kka<PERSON> Pen<PERSON>an Produk</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> b<PERSON>y cách sử dụng sản phẩm</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>opening</td>\n", "      <td>Personal Testimony</td>\n", "      <td>Personal Testimony</td>\n", "      <td>Personal Testimony</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Personal na Patotoo</td>\n", "      <td>คำให้การส่วนตัว</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>opening</td>\n", "      <td>Creating Urgency</td>\n", "      <td>Creating Urgency</td>\n", "      <td>Creating Urgency</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Paglikha ng Ka<PERSON>at na Pangangailangan</td>\n", "      <td>สร้างความเร่งด่วน</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON> s<PERSON> cấp bách</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>closing</td>\n", "      <td>Expressing Gratitude and Acknowledgment</td>\n", "      <td>Expressing Gratitude and Acknowledgment</td>\n", "      <td>Expressing Gratitude and Acknowledgment</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> dan <PERSON></td>\n", "      <td>Pagpapahayag ng Pasasalamat at Pagkilala</td>\n", "      <td>การแสดงความขอบคุณและการยอมรับ</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON> dan <PERSON></td>\n", "      <td>Bày tỏ lòng biết ơn và sự công nhận</td>\n", "    </tr>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>closing</td>\n", "      <td>Encouraging Engagement</td>\n", "      <td>Encouraging Engagement</td>\n", "      <td>Encouraging Engagement</td>\n", "      <td>Menggalakka<PERSON>glibatan</td>\n", "      <td>Paghikayat sa Pakikilahok</td>\n", "      <td>ส่งเสริมการมีส่วนร่วม</td>\n", "      <td>Mendorong Keterlibatan</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON> khích sự tham gia</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>closing</td>\n", "      <td>Urgency and Call to Action</td>\n", "      <td>Urgency and Call to Action</td>\n", "      <td>Urgency and Call to Action</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> dan <PERSON> untuk <PERSON></td>\n", "      <td>Kagyat na Pangangailangan at Panawagan sa Pagk...</td>\n", "      <td>ความเร่งด่วนและการเรียกร้องให้ลงมือทำ</td>\n", "      <td><PERSON><PERSON><PERSON> dan <PERSON> untuk <PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> cấp và <PERSON> g<PERSON>nh động</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>closing</td>\n", "      <td>Gratitude Expression</td>\n", "      <td>Gratitude Expression</td>\n", "      <td>Gratitude Expression</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Pagpapahayag ng Pasasalamat</td>\n", "      <td>การแสดงความขอบคุณ</td>\n", "      <td>Ekspresi Rasa Syukur</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> hiện lòng biết ơn</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>closing</td>\n", "      <td>Scarcity and Urgency</td>\n", "      <td>Scarcity and Urgency</td>\n", "      <td>Scarcity and Urgency</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> dan <PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> at Kagipitan</td>\n", "      <td>ความขาดแคลนและความเร่งด่วน</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> dan <PERSON></td>\n", "      <td>Sự khan hiếm và sự cấp bách</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>109 rows × 9 columns</p>\n", "</div>"], "text/plain": ["    strategy_type                              strategy_en  \\\n", "0         opening                      Personal Engagement   \n", "1         opening                             Relatability   \n", "2         opening                Demonstrating Product Use   \n", "3         opening                       Personal Testimony   \n", "4         opening                         Creating Urgency   \n", "..            ...                                      ...   \n", "104       closing  Expressing Gratitude and Acknowledgment   \n", "105       closing                   Encouraging Engagement   \n", "106       closing               Urgency and Call to Action   \n", "107       closing                     Gratitude Expression   \n", "108       closing                     Scarcity and Urgency   \n", "\n", "                                 strategy_US  \\\n", "0                        Personal Engagement   \n", "1                               Relatability   \n", "2                  Demonstrating Product Use   \n", "3                         Personal Testimony   \n", "4                           Creating Urgency   \n", "..                                       ...   \n", "104  Expressing Gratitude and Acknowledgment   \n", "105                   Encouraging Engagement   \n", "106               Urgency and Call to Action   \n", "107                     Gratitude Expression   \n", "108                     Scarcity and Urgency   \n", "\n", "                                 strategy_SG  \\\n", "0                        Personal Engagement   \n", "1                               Relatability   \n", "2                  Demonstrating Product Use   \n", "3                         Personal Testimony   \n", "4                           Creating Urgency   \n", "..                                       ...   \n", "104  Expressing Gratitude and Acknowledgment   \n", "105                   Encouraging Engagement   \n", "106               Urgency and Call to Action   \n", "107                     Gratitude Expression   \n", "108                     Scarcity and Urgency   \n", "\n", "                                strategy_MY  \\\n", "0                      Penglibatan Peribadi   \n", "1                           Kebolehhubungan   \n", "2             Menunjukkan Penggunaan Produk   \n", "3                        Kesaksian <PERSON>   \n", "4             Mewujudkan Keperluan Mendesak   \n", "..                                      ...   \n", "104   Menyatakan <PERSON> dan <PERSON>   \n", "105                Menggalakkan Penglibatan   \n", "106  Kepentingan dan <PERSON> untuk <PERSON>   \n", "107                     <PERSON>g<PERSON><PERSON>   \n", "108                Kekurangan dan <PERSON>   \n", "\n", "                                           strategy_PH  \\\n", "0                              Personal na Pakikilahok   \n", "1                                         Pagkakaugnay   \n", "2                  Pagpapakita ng Paggamit ng Produkto   \n", "3                                  Personal na Patotoo   \n", "4                Paglikha ng Kagyat na Pangangailangan   \n", "..                                                 ...   \n", "104           Pagpapahayag ng Pasasalamat at Pagkilala   \n", "105                          Paghikayat sa Pakikilahok   \n", "106  Kagyat na Pangangailangan at Panawagan sa Pagk...   \n", "107                        Pagpapahayag ng Pasasalamat   \n", "108                             Kakapusan at Kagipitan   \n", "\n", "                               strategy_TH  \\\n", "0                   การมีส่วนร่วมส่วนบุคคล   \n", "1                             ความสัมพันธ์   \n", "2                  การสาธิตการใช้ผลิตภัณฑ์   \n", "3                          คำให้การส่วนตัว   \n", "4                        สร้างความเร่งด่วน   \n", "..                                     ...   \n", "104          การแสดงความขอบคุณและการยอมรับ   \n", "105                  ส่งเสริมการมีส่วนร่วม   \n", "106  ความเร่งด่วนและการเรียกร้องให้ลงมือทำ   \n", "107                      การแสดงความขอบคุณ   \n", "108             ความขาดแคลนและความเร่งด่วน   \n", "\n", "                                strategy_ID  \\\n", "0                      Keterlibatan Pribadi   \n", "1                             Keterhubungan   \n", "2             Menunjukkan Penggunaan Produk   \n", "3                         Kesaksian Pribadi   \n", "4                       Menciptakan Urgensi   \n", "..                                      ...   \n", "104  Menyampaikan <PERSON> dan <PERSON>   \n", "105                  Mendorong Keterlibatan   \n", "106      Urgensi dan <PERSON>uan untuk Bertindak   \n", "107                    Ekspresi Rasa Syukur   \n", "108              <PERSON><PERSON><PERSON>an dan <PERSON>   \n", "\n", "                             strategy_VN  \n", "0                    Sự Tham Gia Cá Nhân  \n", "1                       <PERSON><PERSON><PERSON> n<PERSON>ng liên hệ  \n", "2        <PERSON><PERSON><PERSON><PERSON> b<PERSON>y cách sử dụng sản phẩm  \n", "3                      Lời Chứng Cá Nhân  \n", "4                        <PERSON><PERSON>o s<PERSON> cấp bách  \n", "..                                   ...  \n", "104  Bày tỏ lòng biết ơn và sự công nhận  \n", "105             Khuyến khích sự tham gia  \n", "106        Khẩn cấp và <PERSON>u gọi <PERSON>ành động  \n", "107               <PERSON><PERSON><PERSON><PERSON> hiện lòng biết ơn  \n", "108          S<PERSON> khan hiếm và sự cấp bách  \n", "\n", "[109 rows x 9 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df_translate"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country_code</th>\n", "      <th>industry</th>\n", "      <th>seller_type</th>\n", "      <th>gmv_percentile_level</th>\n", "      <th>strategies</th>\n", "      <th>script_type</th>\n", "      <th>scripts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[['Personal Connection'], ['Call to Action'], ...</td>\n", "      <td>closing</td>\n", "      <td>[['<PERSON><PERSON> kasih semua kerana men<PERSON>tai live sa...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[['Personal Engagement'], ['Relatability'], ['...</td>\n", "      <td>opening</td>\n", "      <td>[['<PERSON> semua! Selamat datang ke live kita hari...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[['Clear Call to Action'], ['Personal Connecti...</td>\n", "      <td>closing</td>\n", "      <td>[['<PERSON><PERSON> kasih semua kerana menyertai sesi li...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[['Creating Urgency'], ['Personal Connection']...</td>\n", "      <td>opening</td>\n", "      <td>[['<PERSON> semua! Selamat datang ke siaran langsun...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G5</td>\n", "      <td>[['Urgency and Scarcity'], ['Personal Connecti...</td>\n", "      <td>closing</td>\n", "      <td>[['<PERSON><PERSON> kasih semua kerana menyertai live ki...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>571</th>\n", "      <td>VN</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G1</td>\n", "      <td>[['Repetition'], ['Personal Engagement'], ['In...</td>\n", "      <td>opening</td>\n", "      <td>[['<PERSON><PERSON> chào xin chào xin chào mọi người! Chào ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>572</th>\n", "      <td>VN</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G3</td>\n", "      <td>[['Expressing Gratitude'], ['Call to Action'],...</td>\n", "      <td>closing</td>\n", "      <td>[['<PERSON><PERSON><PERSON> ơn mọi người đã tham gia buổi livestrea...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>573</th>\n", "      <td>VN</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G3</td>\n", "      <td>[['Personal Engagement'], ['Enthusiastic Greet...</td>\n", "      <td>opening</td>\n", "      <td>[['<PERSON>n chào mọi người! Chào mừng các bạn đến v...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>574</th>\n", "      <td>VN</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>[['Urgency and Scarcity'], ['Expressing Gratit...</td>\n", "      <td>closing</td>\n", "      <td>[['<PERSON><PERSON><PERSON> ơn mọi người đã tham gia buổi livestrea...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>575</th>\n", "      <td>VN</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>[['Urgency and Exclusivity'], ['Engaging Greet...</td>\n", "      <td>opening</td>\n", "      <td>[['<PERSON><PERSON> chào mọi người! Hôm nay là một trong nh...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>576 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    country_code        industry seller_type gmv_percentile_level  \\\n", "0             MY          beauty   affiliate                   G1   \n", "1             MY          beauty   affiliate                   G1   \n", "2             MY          beauty   affiliate                   G3   \n", "3             MY          beauty   affiliate                   G3   \n", "4             MY          beauty   affiliate                   G5   \n", "..           ...             ...         ...                  ...   \n", "571           VN  multi-category      seller                   G1   \n", "572           VN  multi-category      seller                   G3   \n", "573           VN  multi-category      seller                   G3   \n", "574           VN  multi-category      seller                   G5   \n", "575           VN  multi-category      seller                   G5   \n", "\n", "                                            strategies script_type  \\\n", "0    [['Personal Connection'], ['Call to Action'], ...     closing   \n", "1    [['Personal Engagement'], ['Relatability'], ['...     opening   \n", "2    [['Clear Call to Action'], ['Personal Connecti...     closing   \n", "3    [['Creating Urgency'], ['Personal Connection']...     opening   \n", "4    [['Urgency and Scarcity'], ['Personal Connecti...     closing   \n", "..                                                 ...         ...   \n", "571  [['Repetition'], ['Personal Engagement'], ['In...     opening   \n", "572  [['Expressing Gratitude'], ['Call to Action'],...     closing   \n", "573  [['Personal Engagement'], ['Enthusiastic Greet...     opening   \n", "574  [['Urgency and Scarcity'], ['Expressing Gratit...     closing   \n", "575  [['Urgency and Exclusivity'], ['Engaging Greet...     opening   \n", "\n", "                                               scripts  \n", "0    [['<PERSON><PERSON> kasih semua kerana men<PERSON> live sa...  \n", "1    [['Hai semua! Selamat datang ke live kita hari...  \n", "2    [['<PERSON><PERSON> kasih semua kerana menyertai sesi li...  \n", "3    [['Hai semua! Selamat datang ke siaran langsun...  \n", "4    [['<PERSON><PERSON> kasih semua kerana men<PERSON> live ki...  \n", "..                                                 ...  \n", "571  [['<PERSON><PERSON> chào xin chào xin chào mọi người! Chào ...  \n", "572  [['<PERSON><PERSON><PERSON> ơn mọi người đã tham gia buổi livestrea...  \n", "573  [['<PERSON>n chào mọi người! Chào mừng các bạn đến v...  \n", "574  [['<PERSON><PERSON><PERSON> ơn mọi người đã tham gia buổi livestrea...  \n", "575  [['<PERSON><PERSON> chào mọi người! Hôm nay là một trong nh...  \n", "\n", "[576 rows x 7 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df_scripts"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Applying translations: 100%|██████████| 576/576 [00:00<00:00, 5281.63it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- 翻译结果示例 ---\n", "  country_code                                         strategies\n", "0           MY  [[<PERSON><PERSON><PERSON><PERSON>], [Seruan untuk Bertindak]...\n", "1           MY  [[<PERSON><PERSON><PERSON><PERSON>], [<PERSON><PERSON><PERSON><PERSON>bunga<PERSON>], [M...\n", "2           MY  [[<PERSON><PERSON> ya<PERSON>], [Hubunga<PERSON>...\n", "3           MY  [[Mewu<PERSON><PERSON><PERSON>], [Hubungan Pe...\n", "4           MY  [[<PERSON><PERSON><PERSON><PERSON> dan <PERSON>], [<PERSON><PERSON><PERSON><PERSON>...\n"]}], "source": ["def create_strategy_translation_mapping(df_translate):\n", "    \"\"\"\n", "    从翻译表格创建映射字典，将英文策略映射到各国家的翻译\n", "    \n", "    参数:\n", "    df_translate: 包含策略翻译的DataFrame\n", "    \n", "    返回:\n", "    映射字典，格式为 {country_code: {strategy_en: translated_strategy}}\n", "    \"\"\"\n", "    # 创建映射字典\n", "    translation_mapping = {}\n", "    \n", "    # 遍历所有国家代码\n", "    countries = ['US', 'SG', 'MY', 'PH', 'TH', 'ID', 'VN']\n", "    \n", "    for country in countries:\n", "        # 为每个国家创建一个子字典\n", "        country_mapping = {}\n", "        \n", "        # 遍历翻译表格的每一行\n", "        for _, row in df_translate.iterrows():\n", "            strategy_en = row['strategy_en']\n", "            strategy_translated = row[f'strategy_{country}']\n", "            \n", "            # 将英文策略映射到翻译后的策略\n", "            country_mapping[strategy_en] = strategy_translated\n", "        \n", "        # 将国家映射添加到主映射字典\n", "        translation_mapping[country] = country_mapping\n", "    \n", "    return translation_mapping\n", "\n", "def apply_translations_to_dataframe(df, translation_mapping):\n", "    \"\"\"\n", "    将翻译映射应用到DataFrame中（处理嵌套策略结构）\n", "    \"\"\"\n", "    def process_nested_strategies(strategy_list, country_mapping):\n", "        \"\"\"递归处理嵌套策略结构\"\"\"\n", "        translated = []\n", "        for item in strategy_list:\n", "            if isinstance(item, list):\n", "                # 递归处理嵌套列表\n", "                translated.append(process_nested_strategies(item, country_mapping))\n", "            else:\n", "                # 处理单个策略\n", "                if item in country_mapping:\n", "                    translated.append(country_mapping[item])\n", "                else:\n", "                    print(f\"Warning: No translation for '{item}' in {country}\")\n", "                    translated.append(item)\n", "        return translated\n", "\n", "    df['translated_strategies'] = None\n", "    \n", "    for idx, row in tqdm(df.iterrows(), total=len(df), desc=\"Applying translations\"):\n", "        country = row['country_code']\n", "        strategies = row['strategies']\n", "        \n", "        if country not in translation_mapping:\n", "            continue\n", "            \n", "        country_mapping = translation_mapping[country]\n", "\n", "        # 更健壮的嵌套列表解析\n", "        try:\n", "            if isinstance(strategies, str):\n", "                # 使用ast模块安全解析嵌套列表\n", "                import ast\n", "                parsed_strategies = ast.literal_eval(strategies)\n", "            else:\n", "                parsed_strategies = strategies\n", "            \n", "            # 处理嵌套结构\n", "            translated = process_nested_strategies(parsed_strategies, country_mapping)\n", "            df.at[idx, 'translated_strategies'] = translated\n", "        except Exception as e:\n", "            print(f\"Error processing index {idx}: {str(e)}\")\n", "            df.at[idx, 'translated_strategies'] = strategies  # 保留原始值\n", "\n", "    return df\n", "\n", "# 创建翻译映射\n", "translation_mapping = create_strategy_translation_mapping(df_translate)\n", "\n", "# 应用翻译到df_scripts\n", "df_scripts_translated = apply_translations_to_dataframe(df_scripts.copy(), translation_mapping)\n", "\n", "df_scripts_translated['strategies'] = df_scripts_translated['translated_strategies']\n", "\n", "# 删除原始的strategies列\n", "df_scripts_translated.drop(columns=['translated_strategies'], inplace=True)\n", "\n", "# 保存结果\n", "df_scripts_translated.to_csv('../output/live_scripts_sea_translated.csv', index=False, encoding='utf-8-sig')\n", "\n", "# 显示前几行结果\n", "print(\"\\n--- 翻译结果示例 ---\")\n", "print(df_scripts_translated[['country_code', 'strategies']].head())"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "undefined.undefined.undefined"}}, "nbformat": 4, "nbformat_minor": 2}