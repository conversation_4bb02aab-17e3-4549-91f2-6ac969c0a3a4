from laplace import Client
import json
import time

def laplace_request_transformer(request):
    def encode_deal(data):
        if isinstance(data, str):
            return data.encode()
        elif isinstance(data, dict):
            return json.dumps(data).encode()
        else:
            return data
    laplace_request = {}
    for k, v in request.items():
        if isinstance(v, list):
            laplace_request[k] = [json.dumps(v).encode()]
        else:
            laplace_request[k] = [encode_deal(v)]
    return laplace_request

def generate_live_script():
    start_time = time.time()
    
    psm = "tcp://[2605:340:cda2:1235:4e66:2acf:4d91:127a]:9267"
    model_server = "ECOM_MFIP_SERVER"
    
    prompt = """
    You are an assistant for a TikTok seller. Your task is to generate engaging product scripts for live streams based on given strategies. The generated script should follow the structure below. [Label_level_1] is the first class label system, including Opening, Interaction, Explaining Product, and Urging Purchase. Under every first class, there are some second level labels <Label_level_2>. You should generate the specific content under each second level label.

    [Opening]
    <Warm and Friendly Greeting>
    <Personal Introduction>
    <Expressing Gratitude>

    [Interaction]
    <Personal Engagement>
    <Encouraging Interaction>
    <Personal Connection>

    [Explaining Product]
    <Demonstrating Product Usage>
    <Highlighting Product Features>
    <Detailed Product Description>
    <Addressing Pain Points>
    <Personal Testimony>

    [Urging Purchase]
    <Creating Urgency and Scarcity>
    <Highlighting Discounts and Offers>
    <Call to Action (CTA)>

    Please generate product scripts based on the following information:
    - **Industry**: mixed
    - **Product Name**: OMG - 1 BOX WT PREMIUM SERIES - SKINCARE WAJAH - GODDESSKIN - BPOM
    - **Product Description**: 
    <product_desc_start>
    White Tomato Premium Series
    diformulasikan untuk membantu mencerahkan, menghilangkan bekas jerawat, menyamarkan kerutan, memudarkan flek kulit yang kusam dan tidak disarankan untuk kulit yang berjerawat dibuat khusus kandungan premium semua hasil 2x lipat lebih cepat.
    Terbuat dari extract tomat putih sehingga aman untuk ibu hamil dan menyusui serta telah bersertifikat BPOM dan HALAL
    Isi Paket Goddesskin White Tomato Premium, terdiri dari:
    - Facial Wash Premium
    - Toner Premium
    - Serum Premium
    - Day Cream Premium
    - Night Cream Premium
    Goddesskin White Tomato Premium Facial Wash
    Isi 100 ml
    NO BPOM: NA18211203156
    Goddesskin White Tomato Premium Toner
    Isi 100 ml
    NO BPOM: NA18211203157
    Goddesskin White Tomato Premium Serum
    Isi 20 ml
    NO BPOM: NA18211901153
    Goddesskin White Tomato Premium Day Cream
    Isi 10 gr
    NO BPOM: NA18210104013
    Goddesskin White Tomato Premium Night Cream
    Isi 10 gr
    NO BPOM: NA18210104016
    Cara Pemakaian:
    Ambil produk secukupnya dan oleskan pada kulit wajah secara merata
    Pagi : Facial Wash + Toner + Serum + Day Cream
    Malam : Facial Wash + Toner + Serum + Night Cream
    <product_desc_end>

    Please generate a product script referencing the above strategies, suitable for an affiliate live streaming in the mixed industry. The scripts should incorporate elements from the specified strategies, and the live script should be generated in Indonesian.

    Note that you don't need to apply all the summarized content strategies; choose the ones that are most suitable. Ensure that the generated scripts are based on the provided information and do not include any false information.

    Please generate the script in JSON format like below, don't add any other information, just the json string:
    {
        "scripts": [
            "generated script"
        ]
    }
    """    
    batch_request = {
        "input": prompt,
        "params": {
            "mfip_n": 1,
            "mfip_temperature": 0.75,
            "mfip_top_k": 1,
            "mfip_top_p": 0.75,
            "mfip_max_new_tokens": 4096,
            "mfip_stop_words": ["<|eot_id|>"],
            "mfip_inprompt_flag": False,
            "mfip_enable_print_in_prompt": False,
            "mfip_enable_print_out_prompt": False,
        },
        "api_key": "client psm"
    }
    
    new_batch_request = laplace_request_transformer(batch_request)
    client = Client(psm, timeout=15000)
    output = client.matx_inference(model_server, new_batch_request)
    
    end_time = time.time()
    print(f"\nTime elapsed: {end_time - start_time:.2f} seconds")
    print("------------------------------")
    for result in output.output_bytes_lists['output']:
        print(result.decode())

if __name__ == "__main__":
    generate_live_script() 