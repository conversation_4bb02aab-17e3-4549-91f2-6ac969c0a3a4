{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import openai\n", "from tqdm import tqdm\n", "import re\n", "\n", "\n", "class OpenAIAgent:\n", "    def __init__(self, api_key, model=\"gpt-4-0613\"):\n", "        self.client = openai.AzureOpenAI(\n", "            azure_endpoint=\"https://search-va.byteintl.net/gpt/openapi/online/v2/crawl\",\n", "            api_version=\"2024-03-01-preview\",\n", "            api_key=api_key\n", "        )\n", "        self.model = model\n", "\n", "    def system_message(self, message):\n", "        return message\n", "\n", "    def assistant_message(self, message):\n", "        return message\n", "\n", "    def user_message(self, message):\n", "        return message\n", "\n", "    @staticmethod\n", "    def clean_json_output(output):\n", "        output = output.strip()\n", "        if output.startswith(\"```json\"):\n", "            output = output[7:]\n", "        if output.endswith(\"```\"):\n", "            output = output[:-3]\n", "        cleaned_output = output.strip()\n", "\n", "        try:\n", "            json_data = json.loads(cleaned_output)\n", "        except json.JSONDecodeError as e:\n", "            print(f\"JSON decoding error: {e}\")\n", "            return cleaned_output\n", "\n", "        def clean_json(data):\n", "            if isinstance(data, dict):\n", "                return {key: clean_json(value) for key, value in data.items()}\n", "            elif isinstance(data, list):\n", "                return [clean_json(item) for item in data]\n", "            elif isinstance(data, str):\n", "                return \"\" if data.lower() in [\"unknown\", \"na\", \"null\"] else data\n", "            else:\n", "                return data\n", "\n", "        cleaned_json_data = clean_json(json_data)\n", "        return cleaned_json_data\n", "\n", "    def run_openai_task(self, system_message, assistant_message, user_message):\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": self.system_message(system_message)},\n", "            {\"role\": \"assistant\", \"content\": self.assistant_message(assistant_message)},\n", "            {\"role\": \"user\", \"content\": self.user_message(user_message)}\n", "        ]\n", "\n", "        completion = self.client.chat.completions.create(\n", "            extra_headers={\"X-TT-LOGID\": \"abc\"},  # 请务必带上此header，方便定位问题\n", "            model=self.model,\n", "            messages=messages,\n", "            temperature=0,\n", "            frequency_penalty=0,\n", "            presence_penalty=0\n", "        )\n", "\n", "        json_data = completion.model_dump_json()\n", "        data_dict = json.loads(json_data)\n", "\n", "        result = self.clean_json_output(data_dict['choices'][0]['message']['content'])\n", "        usage_data = data_dict.get('usage', {})\n", "        total_tokens = usage_data.get('total_tokens', 0)\n", "        prompt_tokens = usage_data.get('prompt_tokens', 0)\n", "        completion_tokens = usage_data.get('completion_tokens', 0)\n", "\n", "        return result, total_tokens, prompt_tokens, completion_tokens\n", "    \n", "\n", "\n", "    def generate_product_script(self, industry, strategies, product_name, product_description):\n", "        num_scripts = \"10-20\"\n", "        \n", "        system_message = \"You are an assistant for a TikTok seller. Your task is to generate engaging product scripts for live streams based on given strategies.\"\n", "        strategy_explanations = \"\\n\".join([f\"1. **{strategy['name']}**: {strategy['explanation']}\" for strategy in strategies])\n", "        strategy_names = \", \".join([strategy['name'] for strategy in strategies])\n", "        examples = \"\\n\".join([f\"**Examples for {strategy['name']}**: {strategy['examples']}\" for strategy in strategies])\n", "\n", "        \n", "        assistant_message = f\"\"\"\n", "        Please generate product scripts based on the following information:\n", "\n", "        - **Industry**: {industry}\n", "        - **Product \n", "        \n", "        **: {product_description}\n", "        - **Top Strategies**: {strategy_names}\n", "        - **Product Name**: {product_name}\n", "\n", "        **Strategy Explanations**:\n", "        {strategy_explanations}\n", "\n", "        {examples}\n", "\n", "        **Generate Product Scripts**:\n", "        Please generate {num_scripts} product scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies.\n", "\n", "        Return the output in JSON format like this:\n", "        {{\n", "            \"scripts\": [\n", "                {{\n", "                    \"title\": \"Strategy\",\n", "                    \"text\": \"Script Text\"\n", "                }},\n", "                {{\n", "                    \"title\": \"Strategy\",\n", "                    \"text\": \"Script Text\"\n", "                }},\n", "                {{\n", "                    \"title\": \"Strategy\",\n", "                    \"text\": \"Script Text\"\n", "                }}\n", "                ...\n", "            ]\n", "        }}\n", "        \"\"\"\n", "\n", "        user_message = f\"\"\"\n", "        Industry: {industry}\n", "        Product Description: {product_description}\n", "        Top Strategies: {strategy_names}\n", "        Product Name: {product_name}\n", "\n", "        Please generate {num_scripts} product scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies.\n", "\n", "        Return the output in JSON format like this:\n", "        {{\n", "            \"scripts\": [\n", "                {{\n", "                    \"title\": \"Strategy\",\n", "                    \"text\": \"Script Text\"\n", "                }},\n", "                {{\n", "                    \"title\": \"Strategy\",      \n", "                    \"text\": \"Script Text\"\n", "                }},\n", "                {{\n", "                    \"title\": \"Strategy\",\n", "                    \"text\": \"Script Text\"\n", "                }}\n", "                ...\n", "            ]\n", "        }}\n", "        \"\"\"\n", "\n", "        result, total_tokens, prompt_tokens, completion_tokens = self.run_openai_task(system_message, assistant_message, user_message)\n", "        return result, total_tokens, prompt_tokens, completion_tokens\n", "\n", "def product_generate(prd, industry_strategy_dict, category_industry_dict, agent):\n", "    if not industry_strategy_dict or not category_industry_dict:\n", "        return 0, None\n", "\n", "    prod_desc = prd.product_desc\n", "    # if prod_desc is None or len(prod_desc) < 100:\n", "    #     return 0, None\n", "\n", "    try:\n", "        prod_desc_lines = re.sub('<.+?>', '\\n', prod_desc).split('\\n')\n", "        product_description = '\\n'.join([line for line in prod_desc_lines if len(line) > 0]).strip()\n", "    except Exception as e:\n", "        print(e)\n", "        product_description = \"\"\n", "    product_name = prd.product_name\n", "    category = prd.category\n", "\n", "    industry = category_industry_dict.get(category, category_industry_dict.get('Default'))\n", "\n", "    strategies = industry_strategy_dict.get(industry, industry_strategy_dict['home'])\n", "    # print(industry, strategies)\n", "    if len(strategies) == 0:\n", "        print('strategies are not qualified', prd.product_id)\n", "        return 0, None\n", "\n", "    # summarized_strategies = \"####\".join(strategies)\n", "\n", "    result, total_tokens, prompt_tokens, completion_tokens = agent.generate_product_script(\n", "        industry, strategies, product_name, product_description\n", "    )\n", "    if result is None:\n", "        # 如果第一次运行失败，则再尝试一次\n", "        \n", "        result, total_tokens, prompt_tokens, completion_tokens = agent.generate_product_script(\n", "            industry, strategies, product_name, product_description\n", "        )\n", "    \n", "    if result is None:\n", "        return 0, None\n", "\n", "    return 1, result\n", "\n", "# 示例产品信息类\n", "class ProductInfo:\n", "    def __init__(self, product_name, product_desc, category):\n", "        self.product_name = product_name\n", "        self.product_desc = product_desc\n", "        self.category = category\n", "    \n", "    def __str__(self):\n", "        return f\"ProductInfo(product_name={self.product_name}, product_desc={self.product_desc}, category={self.category})\"\n", "\n", "# 示例使用\n", "\n", "# 初始化代理类对象\n", "\n", "agent = OpenAIAgent(api_key=\"fmGaxre25OCRtlxBhGrQHfyVq1RBpbkT\", model='gpt-4o-2024-05-13')\n", "# agent = OpenAIAgent(api_key=\"nCUBvtcbgzNQokqFxDQyfnKKAvIlHHVq\", model='gpt-4o-2024-08-06')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: './data/category_industry_df.csv'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# 从 CSV 文件中读取数据\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m category_industry_df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m./data/category_industry_df.csv\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m      3\u001b[0m \u001b[38;5;66;03m# industry_strategy_df = pd.read_csv('./data/industry_strategy_df.csv')\u001b[39;00m\n\u001b[1;32m      4\u001b[0m \n\u001b[1;32m      5\u001b[0m \u001b[38;5;66;03m# 构建 category_industry_dict\u001b[39;00m\n\u001b[1;32m      6\u001b[0m category_industry_dict \u001b[38;5;241m=\u001b[39m category_industry_df\u001b[38;5;241m.\u001b[39mset_index(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcategory\u001b[39m\u001b[38;5;124m'\u001b[39m)[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mindustry\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mto_dict()\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/pandas/util/_decorators.py:211\u001b[0m, in \u001b[0;36mdeprecate_kwarg.<locals>._deprecate_kwarg.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    209\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    210\u001b[0m         kwargs[new_arg_name] \u001b[38;5;241m=\u001b[39m new_arg_value\n\u001b[0;32m--> 211\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/pandas/util/_decorators.py:331\u001b[0m, in \u001b[0;36mdeprecate_nonkeyword_arguments.<locals>.decorate.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    325\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(args) \u001b[38;5;241m>\u001b[39m num_allow_args:\n\u001b[1;32m    326\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m    327\u001b[0m         msg\u001b[38;5;241m.\u001b[39mformat(arguments\u001b[38;5;241m=\u001b[39m_format_argument_list(allow_args)),\n\u001b[1;32m    328\u001b[0m         \u001b[38;5;167;01mFutureWarning\u001b[39;00m,\n\u001b[1;32m    329\u001b[0m         stacklevel\u001b[38;5;241m=\u001b[39mfind_stack_level(),\n\u001b[1;32m    330\u001b[0m     )\n\u001b[0;32m--> 331\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/pandas/io/parsers/readers.py:950\u001b[0m, in \u001b[0;36mread_csv\u001b[0;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, squeeze, prefix, mangle_dupe_cols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, error_bad_lines, warn_bad_lines, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options)\u001b[0m\n\u001b[1;32m    935\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[1;32m    936\u001b[0m     dialect,\n\u001b[1;32m    937\u001b[0m     delimiter,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    946\u001b[0m     defaults\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdelimiter\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m,\u001b[39m\u001b[38;5;124m\"\u001b[39m},\n\u001b[1;32m    947\u001b[0m )\n\u001b[1;32m    948\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[0;32m--> 950\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m _read(filepath_or_buffer, kwds)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/pandas/io/parsers/readers.py:605\u001b[0m, in \u001b[0;36m_read\u001b[0;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[1;32m    602\u001b[0m _validate_names(kwds\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnames\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[1;32m    604\u001b[0m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[0;32m--> 605\u001b[0m parser \u001b[38;5;241m=\u001b[39m TextFileReader(filepath_or_buffer, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwds)\n\u001b[1;32m    607\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[1;32m    608\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m parser\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/pandas/io/parsers/readers.py:1442\u001b[0m, in \u001b[0;36mTextFileReader.__init__\u001b[0;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[1;32m   1439\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m kwds[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m   1441\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles: IOHandles \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[0;32m-> 1442\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_engine(f, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mengine)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/pandas/io/parsers/readers.py:1735\u001b[0m, in \u001b[0;36mTextFileReader._make_engine\u001b[0;34m(self, f, engine)\u001b[0m\n\u001b[1;32m   1733\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[1;32m   1734\u001b[0m         mode \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m-> 1735\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;241m=\u001b[39m get_handle(\n\u001b[1;32m   1736\u001b[0m     f,\n\u001b[1;32m   1737\u001b[0m     mode,\n\u001b[1;32m   1738\u001b[0m     encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mencoding\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[1;32m   1739\u001b[0m     compression\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcompression\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[1;32m   1740\u001b[0m     memory_map\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmemory_map\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mFalse\u001b[39;00m),\n\u001b[1;32m   1741\u001b[0m     is_text\u001b[38;5;241m=\u001b[39mis_text,\n\u001b[1;32m   1742\u001b[0m     errors\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mencoding_errors\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstrict\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[1;32m   1743\u001b[0m     storage_options\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstorage_options\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[1;32m   1744\u001b[0m )\n\u001b[1;32m   1745\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1746\u001b[0m f \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles\u001b[38;5;241m.\u001b[39mhandle\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/pandas/io/common.py:856\u001b[0m, in \u001b[0;36mget_handle\u001b[0;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[1;32m    851\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[1;32m    852\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[1;32m    853\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[1;32m    854\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[1;32m    855\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[0;32m--> 856\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(\n\u001b[1;32m    857\u001b[0m             handle,\n\u001b[1;32m    858\u001b[0m             ioargs\u001b[38;5;241m.\u001b[39mmode,\n\u001b[1;32m    859\u001b[0m             encoding\u001b[38;5;241m=\u001b[39mioargs\u001b[38;5;241m.\u001b[39mencoding,\n\u001b[1;32m    860\u001b[0m             errors\u001b[38;5;241m=\u001b[39merrors,\n\u001b[1;32m    861\u001b[0m             newline\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    862\u001b[0m         )\n\u001b[1;32m    863\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    864\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[1;32m    865\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: './data/category_industry_df.csv'"]}], "source": ["# 从 CSV 文件中读取数据\n", "category_industry_df = pd.read_csv('./data/category_industry_df.csv')\n", "# industry_strategy_df = pd.read_csv('./data/industry_strategy_df.csv')\n", "\n", "# 构建 category_industry_dict\n", "category_industry_dict = category_industry_df.set_index('category')['industry'].to_dict()\n", "\n", "# 构建 industry_strategy_dict\n", "# industry_strategy_dict = industry_strategy_df.groupby('industry')['strategy'].apply(list).to_dict()\n", "df = pd.read_csv('./data/live_narrative_product_strategy.csv')\n", "df['is_seller'] = df['is_seller'].map({1: 'seller', 0: 'affiliate'})\n", "df.rename(columns={'is_seller': 'seller_type', 'narrative_type': 'script_type', 'category': 'industry'}, inplace=True)\n", "df['script_type'] = df['script_type'].map({'product demonstration': 'product'})\n", "\n", "# 将策略转换为字典格式\n", "df_strategies = df[['industry', 'Strategy', 'Explanation', 'Examples']].rename(columns={\n", "    'Strategy': 'name',\n", "    'Explanation': 'explanation',\n", "    'Examples': 'examples'\n", "})\n", "\n", "# 按industry分组并创建字典\n", "industry_strategy_dict = df_strategies.groupby('industry').apply(lambda x: x.to_dict(orient='records')).to_dict()\n", "\n", "# 对每个category的strategies去重\n", "def deduplicate_strategies(strategies):\n", "    seen = set()\n", "    unique_strategies = []\n", "    for strategy in strategies:\n", "        strategy_tuple = tuple(strategy.items())\n", "        if strategy_tuple not in seen:\n", "            seen.add(strategy_tuple)\n", "            unique_strategies.append(strategy)\n", "    return unique_strategies\n", "\n", "# 更新字典中的每个category的strategies\n", "for industry in industry_strategy_dict:\n", "    industry_strategy_dict[industry] = deduplicate_strategies(industry_strategy_dict[industry])\n", "\n", "# 打印前5个策略\n", "for category, strategies in list(industry_strategy_dict.items())[:5]:\n", "    print(f\"Category: {category}\")\n", "    for strategy in strategies:\n", "        print(strategy)\n", "    print(\"\\n\")\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'Personal Testimony',\n", "  'explanation': 'Sharing personal experiences to build trust and relatability with the audience.',\n", "  'examples': '\"i have the black one and i love it\", \"i didn\\'t buy this for gaming. i bought this cause i work from home\", \"i work from home, so watching this all day is very pleasing to me\"'},\n", " {'name': 'Demonstrating Product Usage',\n", "  'explanation': 'Showing how the product works in real-life scenarios to help the audience visualize its practicality and benefits.',\n", "  'examples': '\"i\\'ll put it on and show you what it looks like on my neck\", \"we got the galaxy light projecting onto the ceiling right there\", \"it senses when you\\'re over it and then you do your business, tap, tap, tap, and it closes\"'},\n", " {'name': 'Emphasizing Versatility',\n", "  'explanation': 'Highlighting how the product can be used in various scenarios to appeal to a broader audience.',\n", "  'examples': '\"ideal for a carry on or for just a shorter trip\", \"you can project it onto your wall or like kind of at an angle\", \"do fun food cooking videos\"'},\n", " {'name': 'Personal Testimony',\n", "  'explanation': 'Sharing personal experiences to build trust and relate to the audience.',\n", "  'examples': '\"my husband uses it every day on his lunch break\", \"i actually don\\'t use it for gaming, but i did buy one for my son\", \"i love this one so much more because i can see my whole outfit\"'},\n", " {'name': 'Highlighting Product Features',\n", "  'explanation': 'Emphasizing specific features of the product to showcase its benefits.',\n", "  'examples': '\"super quiet\", \"it has round retro keys\", \"three different sizes are included the 20 inch the 24 inch and then the 28 inch\"'}]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["strategies = df[['Strategy', 'Explanation', 'Examples']].rename(columns={'Strategy': 'name', 'Explanation': 'explanation', 'Examples': 'examples'}).to_dict(orient='records')\n", "strategies[:5]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "AuthenticationError", "evalue": "Error code: 401 - {'error': {'message': 'ak not exist: fmGax', 'code': '-1001'}}", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAuthenticationError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 28\u001b[0m\n\u001b[1;32m     25\u001b[0m start_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[1;32m     27\u001b[0m \u001b[38;5;66;03m# 生成商品话术\u001b[39;00m\n\u001b[0;32m---> 28\u001b[0m result_product, total_tokens_product, prompt_tokens_product, completion_tokens_product \u001b[38;5;241m=\u001b[39m agent\u001b[38;5;241m.\u001b[39mgenerate_product_script(\n\u001b[1;32m     29\u001b[0m     industry\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mBeauty & Personal Care\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     30\u001b[0m     product_description\u001b[38;5;241m=\u001b[39mproduct_description,\n\u001b[1;32m     31\u001b[0m     strategies\u001b[38;5;241m=\u001b[39mstrategies,\n\u001b[1;32m     32\u001b[0m     product_name\u001b[38;5;241m=\u001b[39mproduct_name\n\u001b[1;32m     33\u001b[0m )\n\u001b[1;32m     35\u001b[0m \u001b[38;5;66;03m# 结束计时\u001b[39;00m\n\u001b[1;32m     36\u001b[0m end_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n", "Cell \u001b[0;32mIn[1], line 158\u001b[0m, in \u001b[0;36mOpenAIAgent.generate_product_script\u001b[0;34m(self, industry, strategies, product_name, product_description)\u001b[0m\n\u001b[1;32m     92\u001b[0m assistant_message \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m     93\u001b[0m \u001b[38;5;124mPlease generate product scripts based on the following information:\u001b[39m\n\u001b[1;32m     94\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    127\u001b[0m \u001b[38;5;124m\u001b[39m\u001b[38;5;130;01m}}\u001b[39;00m\n\u001b[1;32m    128\u001b[0m \u001b[38;5;124m\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m    130\u001b[0m user_message \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m    131\u001b[0m \u001b[38;5;124mIndustry: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mindustry\u001b[38;5;132;01m}\u001b[39;00m\n\u001b[1;32m    132\u001b[0m \u001b[38;5;124mProduct Description: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mproduct_description\u001b[38;5;132;01m}\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    155\u001b[0m \u001b[38;5;124m\u001b[39m\u001b[38;5;130;01m}}\u001b[39;00m\n\u001b[1;32m    156\u001b[0m \u001b[38;5;124m\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[0;32m--> 158\u001b[0m result, total_tokens, prompt_tokens, completion_tokens \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrun_openai_task(system_message, assistant_message, user_message)\n\u001b[1;32m    159\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m result, total_tokens, prompt_tokens, completion_tokens\n", "Cell \u001b[0;32mIn[1], line 61\u001b[0m, in \u001b[0;36mOpenAIAgent.run_openai_task\u001b[0;34m(self, system_message, assistant_message, user_message)\u001b[0m\n\u001b[1;32m     54\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mrun_openai_task\u001b[39m(\u001b[38;5;28mself\u001b[39m, system_message, assistant_message, user_message):\n\u001b[1;32m     55\u001b[0m     messages \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     56\u001b[0m         {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>e\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msystem\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msystem_message(system_message)},\n\u001b[1;32m     57\u001b[0m         {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124massistant\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39massistant_message(assistant_message)},\n\u001b[1;32m     58\u001b[0m         {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39muser_message(user_message)}\n\u001b[1;32m     59\u001b[0m     ]\n\u001b[0;32m---> 61\u001b[0m     completion \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclient\u001b[38;5;241m.\u001b[39mchat\u001b[38;5;241m.\u001b[39mcompletions\u001b[38;5;241m.\u001b[39mcreate(\n\u001b[1;32m     62\u001b[0m         extra_headers\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mX-TT-LOGID\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mabc\u001b[39m\u001b[38;5;124m\"\u001b[39m},  \u001b[38;5;66;03m# 请务必带上此header，方便定位问题\u001b[39;00m\n\u001b[1;32m     63\u001b[0m         model\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel,\n\u001b[1;32m     64\u001b[0m         messages\u001b[38;5;241m=\u001b[39mmessages,\n\u001b[1;32m     65\u001b[0m         temperature\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m,\n\u001b[1;32m     66\u001b[0m         frequency_penalty\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m,\n\u001b[1;32m     67\u001b[0m         presence_penalty\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m\n\u001b[1;32m     68\u001b[0m     )\n\u001b[1;32m     70\u001b[0m     json_data \u001b[38;5;241m=\u001b[39m completion\u001b[38;5;241m.\u001b[39mmodel_dump_json()\n\u001b[1;32m     71\u001b[0m     data_dict \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mloads(json_data)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_utils/_utils.py:275\u001b[0m, in \u001b[0;36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    273\u001b[0m             msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[38;5;241m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    274\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[0;32m--> 275\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/resources/chat/completions.py:829\u001b[0m, in \u001b[0;36mCompletions.create\u001b[0;34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, extra_headers, extra_query, extra_body, timeout)\u001b[0m\n\u001b[1;32m    788\u001b[0m \u001b[38;5;129m@required_args\u001b[39m([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m], [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[1;32m    789\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcreate\u001b[39m(\n\u001b[1;32m    790\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    826\u001b[0m     timeout: \u001b[38;5;28mfloat\u001b[39m \u001b[38;5;241m|\u001b[39m httpx\u001b[38;5;241m.\u001b[39mTimeout \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m|\u001b[39m NotGiven \u001b[38;5;241m=\u001b[39m NOT_GIVEN,\n\u001b[1;32m    827\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ChatCompletion \u001b[38;5;241m|\u001b[39m Stream[ChatCompletionChunk]:\n\u001b[1;32m    828\u001b[0m     validate_response_format(response_format)\n\u001b[0;32m--> 829\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_post(\n\u001b[1;32m    830\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/chat/completions\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    831\u001b[0m         body\u001b[38;5;241m=\u001b[39mmaybe_transform(\n\u001b[1;32m    832\u001b[0m             {\n\u001b[1;32m    833\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: messages,\n\u001b[1;32m    834\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m: model,\n\u001b[1;32m    835\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maudio\u001b[39m\u001b[38;5;124m\"\u001b[39m: audio,\n\u001b[1;32m    836\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfrequency_penalty\u001b[39m\u001b[38;5;124m\"\u001b[39m: frequency_penalty,\n\u001b[1;32m    837\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfunction_call\u001b[39m\u001b[38;5;124m\"\u001b[39m: function_call,\n\u001b[1;32m    838\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfunctions\u001b[39m\u001b[38;5;124m\"\u001b[39m: functions,\n\u001b[1;32m    839\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlogit_bias\u001b[39m\u001b[38;5;124m\"\u001b[39m: logit_bias,\n\u001b[1;32m    840\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlogprobs\u001b[39m\u001b[38;5;124m\"\u001b[39m: logprobs,\n\u001b[1;32m    841\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmax_completion_tokens\u001b[39m\u001b[38;5;124m\"\u001b[39m: max_completion_tokens,\n\u001b[1;32m    842\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmax_tokens\u001b[39m\u001b[38;5;124m\"\u001b[39m: max_tokens,\n\u001b[1;32m    843\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmetadata\u001b[39m\u001b[38;5;124m\"\u001b[39m: metadata,\n\u001b[1;32m    844\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodalities\u001b[39m\u001b[38;5;124m\"\u001b[39m: modalities,\n\u001b[1;32m    845\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mn\u001b[39m\u001b[38;5;124m\"\u001b[39m: n,\n\u001b[1;32m    846\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mparallel_tool_calls\u001b[39m\u001b[38;5;124m\"\u001b[39m: parallel_tool_calls,\n\u001b[1;32m    847\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprediction\u001b[39m\u001b[38;5;124m\"\u001b[39m: prediction,\n\u001b[1;32m    848\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpresence_penalty\u001b[39m\u001b[38;5;124m\"\u001b[39m: presence_penalty,\n\u001b[1;32m    849\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse_format\u001b[39m\u001b[38;5;124m\"\u001b[39m: response_format,\n\u001b[1;32m    850\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mseed\u001b[39m\u001b[38;5;124m\"\u001b[39m: seed,\n\u001b[1;32m    851\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mservice_tier\u001b[39m\u001b[38;5;124m\"\u001b[39m: service_tier,\n\u001b[1;32m    852\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstop\u001b[39m\u001b[38;5;124m\"\u001b[39m: stop,\n\u001b[1;32m    853\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstore\u001b[39m\u001b[38;5;124m\"\u001b[39m: store,\n\u001b[1;32m    854\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m: stream,\n\u001b[1;32m    855\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream_options\u001b[39m\u001b[38;5;124m\"\u001b[39m: stream_options,\n\u001b[1;32m    856\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtemperature\u001b[39m\u001b[38;5;124m\"\u001b[39m: temperature,\n\u001b[1;32m    857\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtool_choice\u001b[39m\u001b[38;5;124m\"\u001b[39m: tool_choice,\n\u001b[1;32m    858\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtools\u001b[39m\u001b[38;5;124m\"\u001b[39m: tools,\n\u001b[1;32m    859\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtop_logprobs\u001b[39m\u001b[38;5;124m\"\u001b[39m: top_logprobs,\n\u001b[1;32m    860\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtop_p\u001b[39m\u001b[38;5;124m\"\u001b[39m: top_p,\n\u001b[1;32m    861\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m: user,\n\u001b[1;32m    862\u001b[0m             },\n\u001b[1;32m    863\u001b[0m             completion_create_params\u001b[38;5;241m.\u001b[39mCompletionCreateParams,\n\u001b[1;32m    864\u001b[0m         ),\n\u001b[1;32m    865\u001b[0m         options\u001b[38;5;241m=\u001b[39mmake_request_options(\n\u001b[1;32m    866\u001b[0m             extra_headers\u001b[38;5;241m=\u001b[39mextra_headers, extra_query\u001b[38;5;241m=\u001b[39mextra_query, extra_body\u001b[38;5;241m=\u001b[39mextra_body, timeout\u001b[38;5;241m=\u001b[39mtimeout\n\u001b[1;32m    867\u001b[0m         ),\n\u001b[1;32m    868\u001b[0m         cast_to\u001b[38;5;241m=\u001b[39mChatCompletion,\n\u001b[1;32m    869\u001b[0m         stream\u001b[38;5;241m=\u001b[39mstream \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m    870\u001b[0m         stream_cls\u001b[38;5;241m=\u001b[39mStream[ChatCompletionChunk],\n\u001b[1;32m    871\u001b[0m     )\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_base_client.py:1280\u001b[0m, in \u001b[0;36mSyncAPIClient.post\u001b[0;34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[0m\n\u001b[1;32m   1266\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\n\u001b[1;32m   1267\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1268\u001b[0m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1275\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1276\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _StreamT:\n\u001b[1;32m   1277\u001b[0m     opts \u001b[38;5;241m=\u001b[39m FinalRequestOptions\u001b[38;5;241m.\u001b[39mconstruct(\n\u001b[1;32m   1278\u001b[0m         method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpost\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39mpath, json_data\u001b[38;5;241m=\u001b[39mbody, files\u001b[38;5;241m=\u001b[39mto_httpx_files(files), \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions\n\u001b[1;32m   1279\u001b[0m     )\n\u001b[0;32m-> 1280\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest(cast_to, opts, stream\u001b[38;5;241m=\u001b[39mstream, stream_cls\u001b[38;5;241m=\u001b[39mstream_cls))\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_base_client.py:957\u001b[0m, in \u001b[0;36mSyncAPIClient.request\u001b[0;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[1;32m    954\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    955\u001b[0m     retries_taken \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[0;32m--> 957\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_request(\n\u001b[1;32m    958\u001b[0m     cast_to\u001b[38;5;241m=\u001b[39mcast_to,\n\u001b[1;32m    959\u001b[0m     options\u001b[38;5;241m=\u001b[39moptions,\n\u001b[1;32m    960\u001b[0m     stream\u001b[38;5;241m=\u001b[39mstream,\n\u001b[1;32m    961\u001b[0m     stream_cls\u001b[38;5;241m=\u001b[39mstream_cls,\n\u001b[1;32m    962\u001b[0m     retries_taken\u001b[38;5;241m=\u001b[39mretries_taken,\n\u001b[1;32m    963\u001b[0m )\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_base_client.py:1061\u001b[0m, in \u001b[0;36mSyncAPIClient._request\u001b[0;34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[0m\n\u001b[1;32m   1058\u001b[0m         err\u001b[38;5;241m.\u001b[39mresponse\u001b[38;5;241m.\u001b[39mread()\n\u001b[1;32m   1060\u001b[0m     log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRe-raising status error\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m-> 1061\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_status_error_from_response(err\u001b[38;5;241m.\u001b[39mresponse) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m   1063\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_response(\n\u001b[1;32m   1064\u001b[0m     cast_to\u001b[38;5;241m=\u001b[39mcast_to,\n\u001b[1;32m   1065\u001b[0m     options\u001b[38;5;241m=\u001b[39moptions,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1069\u001b[0m     retries_taken\u001b[38;5;241m=\u001b[39mretries_taken,\n\u001b[1;32m   1070\u001b[0m )\n", "\u001b[0;31mAuthenticationError\u001b[0m: Error code: 401 - {'error': {'message': 'ak not exist: fmGax', 'code': '-1001'}}"]}], "source": ["import time\n", "\n", "# 生成商品话术\n", "product_name = \"Super Hydrating Face Mask\"\n", "product_description = \"A face mask that offers intense hydration and rejuvenation for all skin types.\"\n", "strategies = [\n", "    {\n", "        \"name\": \"Highlight Benefits\", \n", "        \"explanation\": \"Emphasize the key benefits of the product to attract customers.\", \n", "        \"examples\": \"Hey everyone! Today, I'm thrilled to introduce the Super Hydrating Face Mask. This mask is your ultimate solution for intense hydration and rejuvenation. Whether you have dry, oily, or combination skin, this mask works wonders to leave your skin feeling soft, supple, and refreshed. Imagine waking up to a glowing complexion every day!\"\n", "    },\n", "    {\n", "        \"name\": \"Customer Testimonials\", \n", "        \"explanation\": \"Include positive feedback from customers to build trust.\", \n", "        \"examples\": \"Let me share some amazing feedback from our happy customers! <PERSON> from New York says, 'The Super Hydrating Face Mask has transformed my skin! It's never felt so hydrated and smooth.' And <PERSON> from California adds, 'I love how it suits my sensitive skin without any irritation. A must-have in my skincare routine!'\"\n", "    },\n", "    {\n", "        \"name\": \"Limited Time Offer\", \n", "        \"explanation\": \"Create a sense of urgency with a limited time discount or offer.\", \n", "        \"examples\": \"Exciting news, everyone! For a limited time only, you can grab the Super Hydrating Face Mask at a special discount. Don't miss out on this exclusive offer to elevate your skincare game. Hurry, the clock is ticking, and this deal won't last long!\"\n", "    }\n", "]\n", "\n", "# 开始计时\n", "start_time = time.time()\n", "\n", "# 生成商品话术\n", "result_product, total_tokens_product, prompt_tokens_product, completion_tokens_product = agent.generate_product_script(\n", "    industry=\"Beauty & Personal Care\",\n", "    product_description=product_description,\n", "    strategies=strategies,\n", "    product_name=product_name\n", ")\n", "\n", "# 结束计时\n", "end_time = time.time()\n", "\n", "# 计算执行时间\n", "execution_time = end_time - start_time\n", "\n", "# 打印结果\n", "print(\"Product Script Result:\", result_product)\n", "print(\"Total Tokens (Product):\", total_tokens_product)\n", "print(\"Prompt Tokens (Product):\", prompt_tokens_product)\n", "print(\"Completion Tokens (Product):\", completion_tokens_product)\n", "print(\"Execution Time (seconds):\", execution_time)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'scripts': [{'title': 'Highlight Benefits',\n", "   'text': 'Hello, beauty lovers! Meet your new skincare hero: the Super Hydrating Face Mask. This mask is packed with intense hydration to rejuvenate your skin, leaving it soft and glowing. Perfect for all skin types, it’s your ticket to a radiant complexion!'},\n", "  {'title': 'Customer Testimonials',\n", "   'text': \"Listen to what our customers are saying! <PERSON> from Texas says, 'This mask is a game-changer! My skin feels so refreshed and hydrated.' And <PERSON> from Florida shares, 'Finally, a mask that works for my combination skin without any breakouts!'\"},\n", "  {'title': 'Limited Time Offer',\n", "   'text': 'Attention, skincare enthusiasts! For a limited time, enjoy a special discount on the Super Hydrating Face Mask. This exclusive offer is your chance to upgrade your skincare routine. Act fast, because this deal won’t last forever!'},\n", "  {'title': 'Highlight Benefits',\n", "   'text': 'Hey there! If you’re looking for a way to boost your skin’s hydration, look no further. The Super Hydrating Face Mask is designed to deeply nourish and rejuvenate, giving you that dewy glow you’ve always wanted.'},\n", "  {'title': 'Customer Testimonials',\n", "   'text': \"Don’t just take our word for it! <PERSON> from Chicago raves, 'My skin has never felt so hydrated and smooth. I’m obsessed!' And <PERSON> from Seattle says, 'This mask is a staple in my skincare routine. It’s perfect for my sensitive skin.'\"},\n", "  {'title': 'Limited Time Offer',\n", "   'text': 'Exciting news! We’re offering a limited time discount on the Super Hydrating Face Mask. Don’t miss your chance to experience ultimate hydration at a fraction of the price. Hurry, this offer is flying off the shelves!'},\n", "  {'title': 'Highlight Benefits',\n", "   'text': 'Hello, gorgeous! The Super Hydrating Face Mask is here to transform your skincare game. With its intense hydration properties, it’s perfect for all skin types, ensuring your skin feels refreshed and rejuvenated.'},\n", "  {'title': 'Customer Testimonials',\n", "   'text': \"Hear it from our satisfied customers! <PERSON> from Boston says, 'This mask is a lifesaver for my dry skin. It’s incredibly hydrating!' And <PERSON> from Denver adds, 'I love how it leaves my skin feeling soft and supple without any irritation.'\"},\n", "  {'title': 'Limited Time Offer',\n", "   'text': 'Heads up, beauty fans! We have a special limited time offer on the Super Hydrating Face Mask. Grab yours now and enjoy a radiant complexion. This deal is too good to miss, so act quickly!'},\n", "  {'title': 'Highlight Benefits',\n", "   'text': 'Hey beauties! Want to achieve that perfect glow? The Super Hydrating Face Mask is your answer. It provides intense hydration and rejuvenation, making it suitable for all skin types. Say hello to beautiful, glowing skin!'},\n", "  {'title': 'Customer Testimonials',\n", "   'text': \"Check out these amazing reviews! <PERSON> from Miami says, 'I can’t believe how hydrated my skin feels after using this mask.' And <PERSON> from Portland shares, 'It’s the best mask I’ve ever used for my combination skin.'\"},\n", "  {'title': 'Limited Time Offer',\n", "   'text': 'Great news! For a limited time, you can get the Super Hydrating Face Mask at a special price. Don’t wait, this exclusive offer is your chance to enhance your skincare routine. Grab it before it’s gone!'}]}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["result_product"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ProductInfo(product_name=Costume Jewelry & Accessories, product_desc=, category=Fashion Accessories)\n", "ProductInfo(product_name=Canvas Bucket Bag, product_desc=This Pre-Owned MCM VISETOS ESSENTIAL DRAWSTRING BUCKET BAG BLACK is a stunning addition to any bag collection. , Made from high-quality CANVAS, this black bucket bag has an excellent condition type, having been preloved with very minimal signs of use. It comes authenticated by Entrupy so you can be sure of its authenticity. , This MCM VISETOS ESSENTIAL DRAWSTRING BUCKET BAG BLACK measures 8 inches in height, 6 inches in width and 6.5 inches in depth making it the perfect size for everyday use or travel purposes. , Bucket Bag Silhouette: This bag is designed in a bucket silhouette with black interior and drawstring closure. , Adjustable Shoulder Strap: The handbag features an adjustable leather shoulder strap for comfortable wear. , Multipurpose: This bucket bag can be paired with any outfit and is suitable for all occasions whether you’re running errands or headed out for a night out with friends. , Pockets: The top opens to reveal a spacious black interior complete with zippered pocket and additional pockets to keep your essentials organized., category=Pre-Owned)\n", "ProductInfo(product_name=Lights & Lighting, product_desc=, category=Home Improvement)\n", "ProductInfo(product_name=Sport & Outdoor Clothing, product_desc=Messi Inter Miami soccer Jersey : , * Our soccer Jersey fabric is a soft and stretchy textile commonly used in clothing production.  , * This product has knitted rather than woven, which gives it excellent drape and elasticity.  , * Our fabric is breathable and comfortable to wear during Soccer/Football Game.  , * You can ware as T-shirts, dresses, and activewear.  , * It can be made from various fibers, such as cotton, polyester, or a blend of different materials., category=Sports & Outdoor)\n", "ProductInfo(product_name=Women's Underwear, product_desc=(random color)Power Belt , Dear buyers, please pay attention to this notice, please purchase this product and purchase according to the size table. Avoid buying the wrong size ,   , Note: Manual measurement may has 1-2 cm error. lf you are between size , oryou have a big under bust size. Please choose the large size ,   , ISIZE CHART HOT SHAPER POWER BELT , SMALL: 64-70CM 25-27 inch waist size , MEDIUM:71-77CM 28-30 inch waist size , LARGE:78-84CM 31-33 inch waist size , XL: 84-89CM 33-35 inch waist size , 2XL: 89-94CM 35-37 inch waist size , 3XL: 94-99CM 37-39 inch waist size ,   ,   , power Belt is a compression belt that helps to shape your midsection. It also burns stubborn fat by making you sweat more. Just by wearing it you will look two sizes smaller. By compressing your abdomen it gives you that hour glass figure you have always dreamed of. The belt is available in four different sizes and can be worn by both men and women. Wear it everywhere to start sweating away stubborn belly fat. , Pros , Wear it Everywhere You can wear Technomed Power Belt everywhere you go. Wear it while you are cleaning the house, at the gym, running errands or watching TV. ,  , category=Womenswear & Underwear)\n", "ProductInfo(product_name=Festive & Party Supplies, product_desc=Add festive cheer to your home with our 4ft PVC Round Top Automatic Wire Flocked Christmas Tree. This tree is perfect for those who want a compact yet elegant holiday decoration. , The PVC material of this Christmas Tree makes it durable and long-lasting. The 4ft height is perfect for smaller spaces or as an additional decoration in larger rooms. , Add your favorite ornaments or keep it minimalistic, either way this Round Top Automatic Wire Flocked Christmas Tree will be a beautiful addition to any festive setup!, category=Home Supplies)\n", "ProductInfo(product_name=Kitchen Utensils & Gadgets, product_desc=If you're looking for a fun way to enjoy your favorite slushy or ice cream treats, look no further than the Snow Keychain Slushy Cup! Made from high-quality food-grade silicone, this slushie maker cup is both safe and eco-friendly, with a soft body that's easy to squeeze and resistant to deformation. , The Snow Keychain Slushy Cup is perfect for on-the-go use thanks to its double-layer silica cooling technology. And with its convenient portable size and included brush and straw accessories in blue color option , Add some frozen fun into your day - get the Snow Keychain Slushy Cup today!, category=Kitchenware)\n", "ProductInfo(product_name=Women's Tops, product_desc= - Looking for a stylish and cozy outerwear option? Look no further than this green vintage washed flap pocket button shacket! , - This shacket is a hybrid between a shirt and a jacket, giving you the best of both worlds. It's made from soft and durable cotton fabric that has been washed for a vintage look and feel. , - The shacket features a classic collar, a button front, long sleeves with button cuffs, and four flap pockets on the chest and waist. It also has a curved hem and a relaxed fit that's perfect for layering over your favorite tops and tees. , - Whether you're heading to the office, meeting up with friends, or enjoying a casual weekend, this shacket will keep you warm and stylish. You can pair it with jeans, leggings, skirts, or dresses for a versatile and trendy outfit. , - Don't miss out on this must-have piece for your wardrobe. Order your green vintage washed flap pocket button shacket today and get ready to rock the shacket trend!, category=Womenswear & Underwear)\n", "ProductInfo(product_name=Baby Clothing & Shoes, product_desc=Girl Tartan Romper NEW , Small fit , 100% cotton, category=Baby & Maternity)\n", "ProductInfo(product_name=Men's Bottoms, product_desc=, category=Menswear & Underwear)\n", "ProductInfo(product_name=Home Decor, product_desc=HOUSEHOLD CANDLES: Our household candles are available in a pack of 5 and packs of 10 are designed to burn for up to 5 hours! These unscented white wax household candles are great value for money and for regular use. These little dinner candles create a clean burn, which is smokeless and non-drip. Perfect for emergencies and everyday use. , PERFECT FOR ALL OCCASIONS: Our long-burning household candles are perfect for indoor and outdoor use and can be used for all occasions! These household candles are ideal for everyday use, emergencies, and for creating a relaxing atmosphere on a budget. , Inexpensive & Long-burning: Our white tapered household candles are long-lasting. They are small in shape and, have a bright flame, are the perfect candles to light up any time under any circumstances. They are cheap candles, functional, inexpensive, and romantic., category=Home Supplies)\n", "ProductInfo(product_name=Classic & Novelty Toys, product_desc=· You can skip the instructions if you want a challenge , · perfect compatible with other building block set,assemble by yourself. , · Different kinds of building blcoks racing cars are available , · Promote your child’s hand-eye coordination, divergent thinking, problem-solving  skills, inspires creativity and brain development. Helps children learn collaboration and teamwork , · Good choices for car&MOC fans., category=Toys & Hobbies)\n", "ProductInfo(product_name=Garden Supplies, product_desc=, category=Home Improvement)\n", "ProductInfo(product_name=Measuring Tools, product_desc=None, category=Tools & Hardware)\n", "expected string or bytes-like object, got 'NoneType'\n", "ProductInfo(product_name=Dog & Cat Accessories, product_desc=Looking for a durable chew toy for your furry friend? Check out our 1 piece Dog Chew Toy, made specifically for aggressive chewers in mind. , The Pet Supplies Type of this product is Chew/Bone Toy which means that this is specifically designed to satisfy your pet's chewing needs. Its Pet Size of Medium makes it a great choice if you have medium-sized dogs or cats at home. , Your furry friend deserves nothing but the best! Bring home this Dog Chew Toy today and watch them have hours of fun while keeping their teeth clean and healthy at the same time!, category=Pet Supplies)\n", "ProductInfo(product_name=Boys' Clothes, product_desc=Actual Photo On Hend Ready to Ship Everyday ship-out 100% Bangla Cotton All colors are beautiful Unisex pombata pwde boy/girls Tag are Not Base on Ageplease check the size chart before , category=Kids' Fashion)\n", "ProductInfo(product_name=Home Decor, product_desc=Create the perfect spooky vibes with our Hanging Cauldron Wax & Oil Burner. A beautiful black burner that will add character to any product range or home space., category=Home Supplies)\n", "ProductInfo(product_name=Women's Suits & Overalls, product_desc=Imported\n", "1XL.2XL.3XL\n", "ACTIVE ZIP UP SET WITH CORD LOCK DETAIL\n", "95% Polyester 5% Spandex \n", "Black \n", "BBA , Active Zip Up Set With Cord Lock Detail ,  , category=Womenswear & Underwear)\n", "ProductInfo(product_name=Office Stationery & Supplies, product_desc=, category=Computers & Office Equipment)\n", "ProductInfo(product_name=Classic & Novelty Toys, product_desc=, category=Toys & Hobbies)\n", "ProductInfo(product_name=Children's & Infants' Books, product_desc=Introducing our Easy Colour Colouring Books! These books are perfect for all ages and skill levels, providing hours of relaxing entertainment.   , Comes in a pack of 2 colouring books. , The simple designs make it easy to colour within the lines and create beautiful artwork. Whether you're a beginner or an experienced artist, these books will provide a fun and stress-free experience. , Pick up our Easy Colour Colouring Books today to enjoy hours of relaxing colouring time! , Simple and Fun Designs , Perfect for All Ages , Suitable for Various Mediums (Crayons, Markers, Coloured Pencils), category=Books, Magazines & Audio)\n", "ProductInfo(product_name=Indoor Furniture, product_desc=Lap Tray with Cushion: This lap tray is size is 43 x 33 x 1.5 cm rectangular shape serving tray a very thick cushioned lap tray with hard wooden frame and surface is smooth and hard , Unique Designed Tray: This laptop tray or food tray has very amazing patterns with a black wooden frame and hard surface of the cushion lap tray , Multipurpose lap tray: Bean bag lap trays are cushioned to provide comforts for all kind of home & professional tasks, lap trays can be used as eating food tray or working desk , Easy to clean: The lap tray with cushion surface is very high quality and requires just wipe clean to reuse it as many times you want & thick cushioned lap tray is tightly stitched with a wooden tray , More lap tray designs available: This lap tray can be a unique gift for men & women, category=Furniture)\n", "ProductInfo(product_name=Bedding, product_desc=, category=Textiles & Soft Furnishings)\n", "ProductInfo(product_name=Men's Tops, product_desc=# T-SHIRT AND SWEATSHIRT INFORMATION:- Gildan 5000 T-Shirt And Gildan 18000 Sweatshirt- DTG Printing Style for a vintage, weathered look.- DTG is engineered to last longer than traditional iron on printing.- This is NOT iron on or heat press printing style., category=Menswear & Underwear)\n", "ProductInfo(product_name=Household Textiles, product_desc=This is a high quality pillow cover which has a soft touch. An embroidered name and number are optional. The embroidery will only be on one side. The football print will be on both sides There is a matching baby blanket available as well. , If want an embroidered name enter the name and number in the Order Specials Instructions box on the Shopping Cart page  , The pillow cover is machine washable., category=Textiles & Soft Furnishings)\n", "ProductInfo(product_name=Food Supplements, product_desc=?GOLI PH is professionally certified by the US FDA. Guaranteed to be legal, safe and effective! ！? , 100% AUTHENTIC FROM USA. , ✔️ CASH ON DELIVERY , ✔️ ON HAND STOCKS & READY TO SHIP , ✔️ VOUCHERS ARE AVAILABLE , ✔️ WE'RE OPEN FOR RE-SELLERS , ✔️ PLACE ORDER TODAY, SHIP TOMORROW , DELIVERY PERIOD , ⏰ 1-2 DAYS METRO MANILA , ⏰ 3-5 DAYS PROVINCIAL , Benefits of ?Ashwagandha Gummies： , ? Reduce stress, relieve anxiety , ? Enhance memory, immune system , ?Improve relaxation, healthy work and sleep , ?Restore muscle endurance, relieve adrenal fatigue , ?Promote heart health, enhance immunity , ?Enhance body metabolism and liver detoxification , ?How to use: , Take 2 capsules twice a day , In the morning or evening, before or after a meal , ?We need to clearly inform you that we are goli ph not goli , ?goli ph and goli are the same manufacturer, but thegoli is is professionally designed for the Philippine market , DISCLAIMER: During transit, content 'unavoidably' becomes moist. this is normal & experienced also by US customers (you can check it online). gummies are stored in a well conditioned place , Goli gums are made from pectin instead of gelatin. The texture of gums may vary depending on the environment in which they are stored, sometimes appearing sticky or shiny, but this is mainly caused by excess water in thebottle, which is a normal phenomenon. , ⚠️Do not use if cap seal is broken or missing. , ⚠️This statement has not been evaluated by the food and drug administration. , ⚠️This product is not intended to diagnose, treat, cure, or prevent any disease. , ⚠️ Consult your physician before use if you have any medical condition or are taking medication. , ⚠️If you feel uncomfortable after eating,please stop eating. , ⚠️Keep out of reach of children. , Improve your sleep and enhance your overall wellness with Goli ph Melatonin For Sleep. This wellness supplement is designed to help you achieve a better sleep by relieving fatigue, stress, and anxiety while boosting your immunity. , Incorporating this wellness supplement into your daily routine can significantly enhance the quality of both your sleep and overall health. Get ready to wake up feeling rejuvenated with Goli ph Melatonin For Sleep!, category=Health)\n", "ProductInfo(product_name=Indoor Furniture, product_desc=Features: - High back sturdy 25\" Seat Height for comfortable seating - Resilient, Long-lasting & Sturdy metal frame keeps them last longer and modern design gives comfort to you or your guest - 300 pound maximum weight capacity - Footrest for added comfort and stability -Adjustable Footpads - Great for front hall, sitting room, dining and more - Durable, Non-mar foot glides - Four legs and A Cross Brace underneath the seat provides extra stability and sturdy - Light assembly needed Specifications: - Finish: - Color: Beige fabric - - Overall Dimensions: 20.1\" W x 22.4\"D 37.9\"H - Seat Size: 16.1\"W x 16.9\"D - - Seat Height (Floor to Seat):25\" - Back Height (Seat to Top): 13.8\" - Weight: 16lbs - Weight Capacity:300lbs  ,  Disclaimer: Photo May Different From Actual Item in Terms of Color Due to the Lighting During Photo Shooting or the Monitor's Display., category=Furniture)\n", "ProductInfo(product_name=Phone Accessories, product_desc=, category=Phones & Electronics)\n", "ProductInfo(product_name=Dog & Cat Accessories, product_desc=The SmartRoll Electric Cat Ball is the perfect toy for your feline companion. This automatic rolling cat toy is designed to provide interactive indoor play and kitten training, keeping your cat entertained for hours. , The compact design of this rolling cat toy makes it perfect for indoor use. Whether you have limited space or just want to keep your cat entertained during rainy days or cold weather, the SmartRoll Electric Cat Ball is an excellent choice. Plus, it's easy to clean and store when not in use! , If you're looking for an engaging way to keep your furry friend entertained while improving their natural instincts at home then look no further than the SmartRoll Electic Toy - Order today!, category=Pet Supplies)\n", "ProductInfo(product_name=Nursing & Feeding, product_desc=Combo1:Grinding bowl + squeeze feeding bottle + pacifier feeder Combo2:Grinding bowl + squeeze bottle + pacifier feeder + Bowl + Spoon + Finger Toothbrush Combo3:Grinding bowl + squeeze bottle + pacifier feeder + Bowl + Spoon + Finger Toothbrush + silicone bib Combo4:squeeze bottle + pacifier feeder + Bowl + Spoon Please Choose the Correct Combo Set Please Choose the Correct Color What you see is What you Get!, category=Baby & Maternity)\n", "ProductInfo(product_name=Garden Supplies, product_desc=, category=Home Improvement)\n", "ProductInfo(product_name=Bedding, product_desc=, category=Textiles & Soft Furnishings)\n", "ProductInfo(product_name=Food Supplements, product_desc=Elevate your energy levels with Zoyava Shilajit Supplement Pure Himalayan 9000MG, a top-quality wellness supplement crafted with 7+ super ingredients to provide you with extra strength and high potency support. , Our shilajit comes in easy-to-swallow capsules that are vegan-friendly and sugar-free. With a shelf life of 36 months, you can enjoy its multivitamin health benefits for an extended period without worrying about spoilage. This product is sold as a single item containing 60 capsules per pack. , Add Zoyava Shilajit into your daily wellness routine today! , Pure Himalayan Shilajit: Made from the purest form of shilajit, this supplement contains 9000MG of this powerful ingredient known for its ability to enhance energy levels and support overall wellness. , 7+ Super Ingredients: In addition to shilajit, this supplement also includes a blend of other super ingredients carefully selected for optimal effectiveness such as ginkgo biloba, rhodiola rosea, ashwagandha, panax ginseng, stinging nettle, cordyceps mushroom, and turmeric. , Made in the USA: You can trust in the quality and purity of this supplement as it is made right here in the USA with globally sourced high quality ingredients., category=Health)\n", "ProductInfo(product_name=Staples & Cooking Essentials, product_desc=KETO Thai Sauce Bundle:  , <PERSON><PERSON> (Healthy Boy) [Ketogenic] Thin Soy Sauce 300ml x 1pc  , <PERSON><PERSON> (Healthy Boy) [Ketogenic] Oyster Sauce 300ml x 1pc  , <PERSON><PERSON> (Healthy Boy) [Ketogenic] Fish Sauce 300ml x 1pc  , Descriptions: ,  [Ketogenic] Thin Soy Sauce 300ml  , Ingredients: Soybean 62%, Rice 22%, Salt 11%, Water 5%.  , Contains: Soybean  , Descriptions:  , [Ketogenic] Oyster Sauce 300ml  , Ingredients: Water 72.279%, Oyster 20%, Salt 6%, Stabilizer : Xant<PERSON> (INS415) 1 %, Natural Color : Caramel (INS150c) 0.5%, Acidity Regulator : Citric Acid (INS330) 0.1%, Preservative : - Sodium Benzoate (INS211) 0.03%, - Potassium Sorbate (INS202) 0.03%, Artificial Flavor 0.05%, Sweetener : Steviol <PERSON> (INS960a) 0.011%  , Contains: Oyster  , Descriptions:  , [Ketogenic] Fish Sauce 300ml  , Ingredients: Anchovy (Fish) 80%, Salt 20%.  , Contains: Anchovy (Fish)  , Brand: Dek Som Boon (also known as Healthy Boy)  , Product Origins: Thailand , Made from soybeans and naturally fermented.  , Completely free from sugar, starch, monosodium glutamate (MSG), and preservatives.  , Enjoy its mellow taste that complements a variety of dishes with ease.  , Introducing Keto Oyster Sauce, 350g, crafted from carefully selected real oysters sourced from the sea.  , Versatile for cooking a wide variety of dishes Or to marinate meat, as its perfect sauce coating enhances the flavor beautifully.  , - No flour - No sugar - No MSG - No artificial colors added  , Discover Dek Som Boon brand Keto Fish Sauce, 300 ml, expertly made from anchovies. Naturally fermented for 12 months, it boasts a deliciously good salty taste.  , Free from sugar, monosodium glutamate (MSG), and preservatives.  , Versatile and perfect for cooking a wide variety of dishes, adding a delightful depth of flavor to your culinary creations. , category=Food & Beverages)\n", "ProductInfo(product_name=Non-natural Crystal, product_desc=Natural stone lucky charm bracelet , How to select your crystal , First things first: Identify what you feel you’re missing before looking into what stones can provide you. This will help you indicate what’s going on within yourself before depending on outside sources. , From there, just let your intuition choose what’s best! ,  , Heartbeat warning! Suitable for all girls with paper crystal bracelet , Wow! Family members ~ , Look at these crystal bracelets! The whole beauty is on my heart. , Unique personality but not grandiose ~ there is a kind of beauty that goes straight into the hearts of the people! , The feeling of a young girl is full of moral meaning, but also a great one! , Small budget! Customization , There are lovers' boudoir styles!, category=Jewelry Accessories & Derivatives)\n", "ProductInfo(product_name=Humanities & Social Sciences, product_desc=This spiritual guide to the self is a handbook of tazkiyah or 'self-purification'. Not only does it illustrate the maladies of the human spiritual condition, it recognises the struggles and insecurities we all succumb to from time to time, and offers up the remedies too.The antidotes to our ailments are drawn from Qur'anic verses and authenticate ahadith (Prophetic sayings), inspiring mindfulness of the Almi<PERSON> Ch<PERSON>sher (SWT) and His Beloved Prophet (PBUH).This guidebook, drawing on 11th and 12th Century works of 'Proof of Islam' and wondrous sage <PERSON> can be applied in our busy lives in this modern hi-tech era. It will prove accessible for people belonging to all ages, all denominations - believers or non-believers alike., category=Books, Magazines & Audio)\n", "ProductInfo(product_name=Home Appliances, product_desc=Use electric rotary dust collectors for home cleaning, bidding farewell to inefficient and annoying traditional cleaning methods. No need to bend or kneel to clean, protecting your joints and knees from damage. Cordless bathroom scrubbers are the perfect gift for the elderly and family members. , The upgraded electric rotary scrubber has an adjustable speed of 300/350RPM, which can be easily switched by pressing the on/off button for 3 seconds. The super motor drive of the cordless cleaning brush ensures that you save time and effort in cleaning the entire room, effectively preventing secondary pollution caused by splashing stains at low speeds, and quickly removing stubborn stains at high speeds. Attention: The use of cleaning agents has a better effect. , The electric rotary scrubber only takes 3.5 to 4 hours to charge, with a usage time of up to 90 minutes, without considering plug and cable issues. The ultra light shower scrubber can be easily operated even by children. , The electric rotary scrubber is equipped with four different shapes of brush heads, which can effectively clean scenes such as bathtubs, bathrooms, stoves, floors, corners, and glass; The cleaning brush head has an IPX7 waterproof rating. Attention: Do not immerse the entire bathroom washer in water! , The extension handle of the electric rotary scrubber is very easy to install and disassemble. The cordless sanding brush can be extended to 25-47 inches, the brush head can be adjusted to 45 °, and even high places can be easily cleaned. The metal extension handle has excellent durability. , A portable new cordless shower brush that conforms to ergonomic design, allowing you to clean your house in the most comfortable position, saving time and energy. In addition, the cordless cleaning brush is equipped with a hook that can be hung on the wall for easy storage. Perfect as a Mother's Day gift., category=Household Appliances)\n", "ProductInfo(product_name=Men's Shoes, product_desc=If You Have Any Questions, You Can Find Us Through The Window, We Will Solve The Problem For You In Time! Thank You For Your Support, Welcome To Buy! , Listing year: 2023 , Season: Summer , Heel style: flat heel , Closing method: sleeve , Upper material: PU , Style: Casual , Applicable to: Youth, category=Shoes)\n", "ProductInfo(product_name=Lights & Lighting, product_desc=, category=Home Improvement)\n", "ProductInfo(product_name=Kitchen Appliances, product_desc=, category=Household Appliances)\n", "ProductInfo(product_name=Large Home Appliances, product_desc=Product introduction; High appearance, multifunctional, deep cleaning, environmentally friendly materials, one click dehydration, sterilization, foldable storage, fresh and clean taste, category=Household Appliances)\n", "ProductInfo(product_name=Humanities & Social Sciences, product_desc=Demonology: Volume 2: Personalized Incantations for the 72 Demons of the Ars GoetiaThis grimoire contains personalized incantations for each Demon of the Ars Goetia. Each Demon has their own attributes and strengths and we can utilize these powers to achieve our specific goals. The first volume of Demonology goes over the specifics of each Demon of the Ars Goetia and has excerpts from the Key of Solomon that can be used to summon and bind these entities, Volume 2 of Demonology has incantations that are specific to each Demon, crafted by the researcher <PERSON><PERSON><PERSON>. After a brief forward from the author the book gives detailed invocations that can be used as is or adapted to harness the power of these ancient entities we call Demons., category=Books, Magazines & Audio)\n", "ProductInfo(product_name=Sport & Outdoor Clothing, product_desc=Description , Report Item / Suspicious Activity, category=Sports & Outdoor)\n", "ProductInfo(product_name=Men's Sleepwear & Loungewear, product_desc=This Men Christmas Print Contrast Piping Satin PJ Set is perfect for the holiday season. Get cozy and comfortable in this stylish PJ set. , The contrast piping detail adds a touch of sophistication to these classic pajamas. The slight stretch in the material ensures a comfortable fit that moves with you throughout the night. , This Men Christmas Print Contrast Piping Satin PJ Set is perfect for relaxing on chilly winter nights or wrapping presents on Christmas morning. Treat yourself or give as a gift to your loved ones!, category=Menswear & Underwear)\n", "ProductInfo(product_name=Bags, product_desc=This Pre-Owned Gucci Marmont Leather Shoulder Bag is a stunning addition to any collection. Crafted from high-quality leather in a beautiful shade of red, this bag exudes luxury and sophistication. , The condition description for the product is good while the condition type for it has been verified as Good by entrupy authenticator/agency which ensures that you can trust in its quality. , In summary, if you're looking for a stylish shoulder bag that provides plenty of space while adding an extra level sophistication to any outfit ,this Pre-Owned Gucci Marmont Leather Shoulder Bag is an excellent choice! , Shoulder Bag Silhouette: The bag silhouette of this product is shoulder bags, making it easy and comfortable to carry on your shoulder. , Spacious Design: With a height of 5 inches, width of 6 inches, and depth of 7 inches, this bag offers plenty of space for all your essentials. , Premium Leather Fabrication: Made from high-quality leather material that looks luxurious and feels great to the touch., category=Pre-Owned)\n", "ProductInfo(product_name=Bedding, product_desc=Printed 100% Egyptian Cotton Duvet Quilt Cover & Pillowcase Bedding Set All Sizes , Lovely Item to Gift Your Loved ones.  , “Item Description” , Material: 100% Egyptian Cotton , (200 Thread Count Approx.) , Beautiful print with matching Pillow Cases. Top quality linen. Renowned for its softness, strength and durability. The collection requires minimal ironing and is easy to care for, keeping you cool in the summer and warm in the winter. These duvets cover sets are delightfully soft and invite you to indulge in blissful slumber every night. This luxurious 100% Egyptian cotton duvet set provides a stylish and sophisticated look for every bed. , Sizes , Single Duvet Cover With 1x Pillow Case (135 x 200 cm) , Double Duvet Cover With 2x Pillow Cases (200 x 200 cm)  , King Duvet Cover With 2x Pillow Cases (230 x 220 cm)  , Super King Duvet Cover With 2x Pillow Cases (260 x 220 cm) , Housewife Pillow Case (50 x 75 cm) :: Fits Pillow (48 x 75 cm) , Dryness , Tumble dry, remove promptly from dryer to avoid wrinkles. , Care Label , Machine washable at 40 degrees, wash with similar colors, do not bleach. , Durability , Capable of withstanding wear and tear or decay, a durable fabric. Softness: Fabric retains its natural softness to give you an extra luxury. Cool and Dry: Draws moisture away from the skin to help balance body temperature. , Pattern , Rosey Mustard / Blossom Gold, category=Textiles & Soft Furnishings)\n", "ProductInfo(product_name=Haircare & Styling, product_desc=Augeas500ml herbal hair dye shampoo , Capacity increase can be used for longer , INSTRUCTIONS: , Pump shampoo from the bottle the quantity depends on person use with dry hair , Mix the shampoo on hand till get a bit foam , Gently massage the hair from roots to tip,until lather is formed massage and keep it on hair 5- 10 minutes * wear glove every time , Time up rinse off your hair with clean water or can use with other shampoo and conditioner , Want to have the perfect hair suggests use every 10-15 days , Cover the gray hair, please stary over 20 minutes. , everyone's hair quality is different, the effect also has deviation, which is within the normal range. , ~REMINDERS~ , Please use your hands before using this product, if you have allergies, please stop using it , Do not use if the skin is damaged , Temporary \"black henna\" tattoos may increase your risk of allergy. , ~Warm tip~ , If the damage is caused by transportation problems, please do not give bad comments. Please contact us actively. , ✅If you feel good after using the product, please give me 5-star praise, which is very important to us. We hope to share it with more people. ,  THANK YOU AND ENJOY  , Product Ingredient; , NO1: Water, cetearyl alcohol, nitrogen argon oxlde (25%), , propylene glycol,ceteareth-20,P-p-phenylenediamine, in , Capryllc/maronlc triglycerlde,4-amino-2-methylbenzene, , HC yellow , Einger ZINGIBER OFFICINALE) extract, Platycladus orientalls extract, , Pseudostellaria heterophyllla extract,flavor,oleaginol ether 5, , 2.4-domineochloroethanol HCI,lsoascorblc acld (lso ve) , No 2:Water, hydrogen peroxlde (50%)cety stearyl alcohol,cetyl stearyl-20. , Caprylc/sunflower triglyceride, polydimethylslloxane, , A lot of EDTA, Phenactin, phosphorlc acld. , *Production License; 20170096, category=Beauty & Personal Care)\n", "ProductInfo(product_name=Electric & Remote Control Toys, product_desc=2023 Upgraded Kids Construction Toys for Boys 12+Year Old , Great present for any construction equipment lover , A hit for construction lovers! Our remote control Excavator toy will keep children and adults busy for hours, Keeps child's attention and encourages imagination. Great for kids boys and Girls age 12+ year old on Birthday, Christmas, Children's Days or any day! Add this functional excavator to other vehicles for extra fun!, category=Toys & Hobbies)\n", "ProductInfo(product_name=Car Repair Tools, product_desc=This 15 Piece Cup-type design tool set is designed to grip like a socket, thus minimising the possibility of crushing filters. With this set, change your filters and oil like a professional. Each cup performs as a socket to remove difficult to reach and stubborn filters with less hassle. Die-cast precision cup type wrench. The tool set comes in a handy carry case (design of this case may vary to the one shown in item photo). The set includes a 1/2\" to 3/8\" adaptor + 14 Oil filter cups. The perfect tool set for the professional mechanic or DIY enthusiast. , category=Automotive & Motorcycle)\n", "ProductInfo(product_name=Hijabs, product_desc=Sateen Pashmina Satin Silk Wrap Shawls Scarf Hijab Soft Shiny Sheen Bridal Party ,   , Very smooth ️   Incredibly Soft ️   Silky glossy finish ️ ,   , Sateen is a material that has been popular since the early 1900s ,   , Sateen is a smooth fabric. The sateen weave means that there is a silky glossy finish on one side and a matte flat finish on the underside.Condition: 100% Brand New Packed , Size:  Length 72\" Width 28\"  approx.New in Fashion, Trendy Scarves*****Many Colours Available***** , This Sateen Pashmina Satin Silk Wrap Shawl Scarf is a must-have accessory for any fashion-forward woman. Made with incredibly soft and smooth sateen material, it features a silky glossy finish that adds an elegant touch to any outfit. Perfect for all seasons, this scarf can be worn as a hijab or draped over your shoulders as an elegant wrap on chilly days. The plain pattern allows it to be paired with any outfit – from casual everyday wear to bridal party attire. Crafted with the finest materials, this scarf is the epitome of luxury and comfort combined. Its silky shine and softness make it perfect for adding a touch of glamour to your look. Whether you're going out on the town or just running errands, this Sateen Pashmina Satin Silk Wrap Shawl Scarf is sure to become one of your favorite accessories!, category=Muslim Fashion)\n", "ProductInfo(product_name=Dog & Cat Food, product_desc=£100 Natural Bulk bundle Box - 13 different Natural treats & 140 individual items.This Bulk bundle is an ideal Purchase if you have a pack of Dogs & struggle to find something to suite all.Containing an assortment of 13 of our most popular Treats & 140 individual items there is sure to be something for everyone insideNot Forgetting the 500g of Tripe sticks which is part of the <PERSON>w's stomach and a really crunchy chew & very popular with our furry friends.Also the 200g of Beef skin with No hair which again is a really tough chew so brillaint to clean your Dogs teeth while keeping them Mentally stimulated.Stock up with our bulk bundle & never run out ofTreats again.INSIDE;10 x Pig Ears10 x Beef Ears with Hair10 x Cow Hooves10 × Beef trachea pieces20 × Chicken feet20 × Hairy Rabbit Ears20 × Chicken necks10 × Pig snouts10 × Chicken sausage10 x Liver sausage10 x Venison sausage500g × Tripe sticks200g × Beef skin No FurAll Chews in the box our suitable for Dogs aged 4 months +, please ensure all Dogs are supervised when feeding and they have access to Clean Fresh water.* Only Natural Ingredients *, category=Pet Supplies)\n", "ProductInfo(product_name=Women's Suits & Overalls, product_desc=About this item , 【True Soft Hooded Blanket】Our wearable blankets are made of super cozy plush fabric and when you wear it you can feel warm around you like being wrapped up in a cloud. Especially in cold seasons, it ‘s a luxurious and comfortable experience for you to stay home or take a walk wearing a blanket sweatshirt. , 【Thoughtful Design Details】We designed some details to ensure the features of warmth, convenience and practicality. The extra roomie hoodie wraps head and neck. Big sleeves for extra comfiness. There is a big pocket hiding your phone or the keys. Tightened cuffs can prevent cold wind from entering the body. Loose hem helps to move freely. , 【Free Size Fits All】The large oversized hoody blanket fit almost various body types not only adults but teens.Just pick your color. , 【Versatile Usage】Extreme comfort wearable blanket helps stay warm while you are reading, playing, watching TV on bed, sofa etc. Bring it to the outdoor party, camping trip, walk your pet or sleepover. And as a sweetheart gift, please give it to people you loved such as families, friends, parents, children, on Mother’s Day, Father’s day, Christmas, Valentine’s Day, Thanksgiving, New Year’s Eve, birthdays, anniversaries. , 【High Quality and Easy Care】100% premium microfiber polyester. Machine washable at low 30°C or in cold water and then tumble dry separately on low. No more ironing. ,   , Product information , Say goodbye to the chilly with warm wearable blanket ~ , This winner, Oversized Sweatshirt Blanket gives you an unparalleled experience. Super soft, super warm, super comfortable and easy to clean, make our life nice. There are several colors(Gray/Blue/Pink/ ) and just pick your favorite color. One size fits all. You can easily tuck your legs up into the torso area without it being tight. , Perfect for indoor and outdoor --Use to provide consecutive warmth in cold weather , Snuggling on your sofa and bed with blanket to enjoy your relaxed hours while watching favorite TV Show, , Use : Hotel, Airplane, Picnic, Home, Hospital, Travel, category=Womenswear & Underwear)\n", "ProductInfo(product_name=Dog & Cat Accessories, product_desc=The Licking Pad: A Must-Have for Every Pet Owner , The Licking Pad is a unique and innovative product that provides a wide range of benefits for your pet. It is a slow feeder that can help to improve digestion, reduce anxiety, promote dental health, and provide enrichment. , Slow Feeding , The Licking Pad's textured surface forces your pet to slow down their eating speed. This can help to prevent overeating, which can lead to obesity and other health problems. Slow feeding is also beneficial for dogs who have a tendency to gulp their food, which can cause indigestion and vomiting. , Anxiety Relief , Licking is a calming and self-soothing behavior for dogs and cats. The Licking Pad can be used to help reduce anxiety during stressful situations, such as grooming, bath time, nail trims, and vet visits. It can also be used to help relieve separation anxiety and boredom. , Dental Health , The Licking Pad's textured surface can help to scrape plaque and tartar from your pet's tongue and teeth. This can help to improve their breath and prevent gum disease. , Enrichment , The Licking Pad is a great way to keep your pet entertained and busy. It is also a good way to introduce new foods to your pet or to get them to eat their medication. , Safe and Durable , The Licking Pad is made of BPA-free silicone and is dishwasher safe. It is also sturdy and durable, making it perfect for even the most active pets. , How to Use the Licking Pad , To use the Licking Pad, simply spread your pet's favorite food or treat on the surface. You can use wet food, peanut butter, yogurt, or even mashed fruits and vegetables. Once the food is spread on the pad, stick it to a smooth surface and let your pet enjoy! , The Licking Pad is a versatile and beneficial product that can improve your pet's health and well-being. Order yours today!, category=Pet Supplies)\n", "ProductInfo(product_name=Entertainment, product_desc=Mystery Dragon! This is for one adult mystery dragon by cinderwing3d. Guaranteed you will get a dual / tri colored, or multicolor gradients! You will never receive a single color dragon. , The photos are examples of our work and actual color / style dragon will be different. We may do the current model or past models. , DO NOT DROP! We inspect all 3D prints before being sent out for top quality! Si<PERSON> may appear different than you would expect, please check the individual listing for approximate size before ordering. , Due to the intricate design used while 3D printing it is possible small pieces of plastic could break and fall off if print is dropped or used in an inappropriate manner. Prints may have sharp design parts. Ages 12+ is recommended. These are susceptible to breaking if misused. , Due to photography, and screen settings, the colors used in our 3D printing may vary slightly from screen to product including different lighting. All 3D prints were printed by us and may have minor imperfections due to the nature of 3D printing. This will not affect the quality, durability or function of the item. 3D Printing is a unique process. Every print will be different. In addition, superficial imperfections may occur, such as layer seams, visible lines. FDM 3d printing is not solid (100% infill), various levels of infill is used to reduce time printing, cost, weight, and waste while maintaining the best quality and structure possible. (shell with infill). , ✨If there is an issue upon receiving it, please contact us ASAP to determine the best course of action to solve the issue. We want you to start enjoying your new 3D prints ASAP! ?If there is a problem with shipping, the claim will need to go through the shipping carrier, as the product is out of our hands after shipping. We are not responsible for shipping or holiday delays. If there is an error on our part, the item will need to be returned to us to be inspected for future quality control. Prior authorization is required. A return shipping label will be sent upon approval and item must be packaged in comparable packaging to which it was received. Only returns requested within 24 hours of receiving your order will be accepted. ?Larger orders may be packaged differently than small orders. We will package the orders for the safest transportation of the product to prevent damage. Larger orders may require a longer time frame. Follow our etsy shop for new prints. Or check out our shop for our socials. , Authorized seller of Cinderwing3D design. Props not included, category=Collectibles)\n", "ProductInfo(product_name=Classic & Novelty Toys, product_desc=· You can skip the instructions if you want a challenge ,   , · perfect compatible with other building block set,assemble by yourself. ,   , · Different kinds of building blcoks racing cars are available ,   , · Promote your child’s hand-eye coordination, divergent thinking, problem-solving      skills, inspires creativity and brain development. Helps children learn collaboration and teamwork ,   , · Good choices for car&MOC fans., category=Toys & Hobbies)\n", "ProductInfo(product_name=Men's Tops, product_desc=The \"New Men's Zip-up Grandad Cardigan\" with an \"Aztec Diamond Print\" and \"Long Sleeves\" is a stylish and cozy knitted jumper top designed for men.Style: This cardigan features a \"Grandad\" or \"Mandarin\" collar, which is a stylish, round neckline without a fold-down collar. It provides a unique and modern look.Zip-Up Closure: The cardigan is designed with a convenient full-length zip-up front closure. This makes it easy to put on and take off, and it also allows you to adjust the level of warmth., category=Menswear & Underwear)\n", "ProductInfo(product_name=Classic & Novelty Toys, product_desc=· You can skip the instructions if you want a challenge , · perfect compatible with other building block set,assemble by yourself. , · Different kinds of building blcoks racing cars are available , · Promote your child’s hand-eye coordination, divergent thinking, problem-solving  skills, inspires creativity and brain development. Helps children learn collaboration and teamwork , · Good choices for car&MOC fans., category=Toys & Hobbies)\n", "ProductInfo(product_name=Flylight, product_desc=A magical tradition for every lost tooth!  Read the story, press the button, and shine a lost-tooth alert to your tooth fairy. , The Tooth Fairy Flylight boxed set comes with the beautifully illustrated storybook and the Flylight projector nightlight, packaged together in a single box.  , The bedtime story is perfect for reading each wiggly night leading up to the lost tooth.  The delightful new fairytale will answer some big tooth fairy questions and have you name your very own tooth fairy.  Then, once that wiggly tooth falls out, it's time to signal your tooth fairy with the magical Flylight.  Turn a lost tooth into a memorable milestone with the Tooth Fairy Flylight. Every lost tooth deserves the spotlighttm  , Complete the set by adding on The Little Tooth Notebook journal to record all the toothy details, for just $5., category=Books, Magazines & Audio)\n", "ProductInfo(product_name=Motorcycle Parts, product_desc=Bosch Strider Horn For Motorcycle and Car 105db - 118db , Note: Some photos may diifferent from actual product in terms of lighting during photo shoot and monitor display , Note: dont install your item if your not proffesional mechanic , it may damage the item and may void the warranty of the item, category=Automotive & Motorcycle)\n", "ProductInfo(product_name=Car Interior Accessories, product_desc=Compact: Small size, 360 degree rotation, save space, can be placed on the desktop.Solar Powered: Use solar energy to drive the intelligent rotary purifier, as there is sunlight, the automatic rotation accelerates the diffusion of fragrance.Design: Solar freshener can help you improve air quality.Exquisite Appearance: Made of aluminum alloy, planet shaped, simple, fashionable and beautiful.Gift: With crystal ball, perfect gift for your family and friends., category=Automotive & Motorcycle)\n", "ProductInfo(product_name=Men's Tops, product_desc=·         Authentic Sheepskin Leather: ZLP crafted this Product from Genuine Sheepskin Leather for Maximum Durability and Comfort. , ·         Quilted Shoulder Design: Featuring a Quilted Design on the Shoulders and Sleeves for added Style and Protection. , Stand Collar with Snap Button: Stand Collar with a Snap Button Closure gives a Stylish, Snug Fit. , ·         Robust YKK Zipper Closure: Equipped with a Robust YKK Zipper Closure for Secure Fastening. , Dive into the world of classic Biker style with this ZLP Men's Black Racer Genuine Sheepskin Leather Jacket. Made from high-quality sheepskin leather, this jacket promises durability and comfort., category=Menswear & Underwear)\n", "ProductInfo(product_name=Women's Suits & Overalls, product_desc=These Move Free Bike Short Set with Pockets are designed with high-stretch fabric for a slimming effect, a corset-like fit, and a comfortable high-waist design. The side pockets and hip-peach design offer convenience and figure-flattering appeal, perfect for yoga, fitness, and exercise., category=Womenswear & Underwear)\n", "ProductInfo(product_name=Dog & Cat Food, product_desc=VitaBeef Adult Dog Food / Vita Beef Adult Dog Dry Dog Food 10Kg ,  , Product Overview: , VitaBeef Adult Dog food is formulated to maintain your dog's proper health and well being. VitaBeef focuses on healthy digestion and proper nutrients absorption of your dog. Properly portioned meat and plant proteins, enhanced with synbiotics (probiotics and prebiotics) make VitaBeef a complete and balanced dog food. With its benefits and irresistable palatability, VitaBeef Dog food will surely become your dog's favourite food. , Essential Benefits: , - High quality protein (Beef and Salmon) for balance nutrition and strong muscles. , - Balanced Calcium and Phosphorus for strong bones and teeth. , - Balanced Omega 3 & 6 essential fatty acids from tuna oil to maintain healthy skin and shiny coat. , - Probiotics & Prebiotics to ensure healthy digestive and immune system. , - Yucca Schidigera extract for stool odour control. , Ingredients: , Soybean Meal, Corn, Meat & Bone meal (Beef), Wheat Bran, Cassava, Salmon hydrolysate, Tallow, Flavorings agents, Poultry fat, Tuna oil, Salt, Vitamins & Minerals, Natural preservatives, Methionine, Probiotics & Prebiotic, Choline chloride, Mycotoxin binder, Yucca Schidigera Extract. , Guaranteed Analysis: , Crude <PERSON>tein (Min.) 18% , Crude Fat (Min.) 7% , Crude Fiber (Max.) 4.5% , Moisture (Max.) 8.5%, category=Pet Supplies)\n", "ProductInfo(product_name=Bath & Body Care, product_desc=Get silky smooth skin with the Rose & Honey Roll On Wax Kit for Hair Removal. This kit includes a 100ml volume rose wax cartridge and a honey wax cartridge that are both natural and non-toxic. , This kit is perfect for adults looking to remove unwanted hair at home without harsh chemicals or painful techniques. The multi pack design ensures you have enough product for multiple uses. With a shelf life of 12 months, this kit will last you through many hair removal sessions to come! , Made in China by China's top manufacturer, this Roll On Wax Kit is sure to exceed your expectations. Say goodbye to unwanted body hair with ease using the Rose & Honey Roll On Wax Kit!, category=Beauty & Personal Care)\n", "ProductInfo(product_name=Women's Tops, product_desc=Our Longline Balloon Sleeve Knitted Cardigan, a stylish and cosy addition to your wardrobe. Crafted with comfort and fashion in mind, this cardigan is designed to keep you warm while effortlessly elevating your look. , The longline silhouette of this cardigan adds a touch of elegance and versatility to any outfit. It is made from high-quality, soft-knit fabric that feels luxurious against your skin. The balloon sleeves create a chic and modern aesthetic, adding a playful twist to the classic cardigan design. , With its relaxed fit, this knitted cardigan is perfect for layering over your favourite tops or dresses. Whether you're heading to the office, meeting up with friends, or simply running errands, this cardigan is a go-to piece for any occasion. It can be dressed up or down, making it a versatile choice for both casual and formal settings. , Available in a range of timeless colours, our Longline Balloon Sleeve Knitted Cardigan effortlessly complements any style or personal preference. It features a button-up front and functional pockets, combining fashion and functionality in one cosy package. , Invest in this must-have wardrobe staple that combines comfort, style, and versatility. Our Longline Balloon Sleeve Knitted Cardigan is the perfect blend of fashion-forward design and everyday practicality, making it a go-to piece for any fashion-savvy individual., category=Womenswear & Underwear)\n", "ProductInfo(product_name=Drinkware, product_desc=None, category=Kitchenware)\n", "expected string or bytes-like object, got 'NoneType'\n", "ProductInfo(product_name=Bathroom Supplies, product_desc=Features: , Easy to install and to stick firmly with high-quality adhesive tape. , Multilayer design to ensure fast draining as well as to provide dry and clean soap after using soap. , Detachable drain pan to dispose water. , Smooth surface for comfortable use and long service life. , Snap joints for strong bearing capacity. , Specifications: , Material: HIPS , Optional Style: Single Layer, 2 Layer, 3 Layer, 4 Layer , Size: approx. 3x12.5x8.5cm/1.18x4.92x3.35in, 15.5cm/6.10in, 22.5cm/8.86in, 29cm/11.42in , Optional Color: Gray, White , Quantity: 1 Pc , Package Content: , 1 x Soap Box , 1 Set Drill-Free sticker, category=Home Supplies)\n", "ProductInfo(product_name=Dog & Cat Accessories, product_desc=TPR Material: Made of high-quality TPR material, this toy is durable and safe for your pet to play with. , <PERSON><PERSON> Tooth Cleaning: The unique love heart bone shape of this toy helps clean your pet's teeth and massage their gums, promoting healthy teeth and gums. , Soft Rubber Texture: The soft rubber texture of the toy makes it easy for your cat to grip and play with, providing hours of entertainment., category=Pet Supplies)\n", "ProductInfo(product_name=Computer Accessories, product_desc=1. Brand new and high quality.2. Wireless Keyboard includes 64 keys for efficient data input.3. Wireless Bluetooth 3.0 technology, which ensures your smoothly remote control within 10 meters.4. Build-in touchpad: Support multi-fingers function will offer a great aid when you do not have a mouse in hand.5. Design the dedicated Bluetooth to code button.6. Folding design, compact design make it easy to storage your backpack or briefcase and take it anywhere for using.7. Compatible with Android and Windows. You can also use it with most Bluetooth Capable mobile phones, laptops, tablets and PCs (Note: iOS & Mac device can not use the touchpad).8. Power by 140mAh lithium-ion battery. , Specification: , General , Model: B033 , Keyboard , Material: ABS Metal , Working Distance: 10m , Charging port: Micro USB , Battery Types: Lithium Battery (Included) , Battery Capcity: 140mAh , Size: 30.3x9.5x0.6cm (Before folding), 15.2x9.5x1.2cm (After folding) , Keyboard Weight: 201g , Technical Parameter , Bluetooth Version: V3.0 , Package Include:  , Package Contents:  1 x Wireless Keyboard, 1 x USB Cable, category=Computers & Office Equipment)\n", "ProductInfo(product_name=Kids' Fashion Accessories, product_desc=The Hype Boys Watch and Body Spray Set is the perfect gift for any stylish and active child. This multi-coloured set includes a Hype watch and a 150ml Hype body spray. , Key Features: , Ceramic case material , White & black dial colour with logo , Analog watch face , Round watch face shape , Sports watch style , Quartz movement type from Japan with waterproof feature , The ceramic case material of the watch adds durability, ensuring it can withstand tough wear during any activity. The white & black dial colour with logo adds style to its functionality, making it the perfect accessory for any outfit. This analog round-faced sports-style Hype Watch features quartz movement from Japan that ensures accurate timekeeping along with its waterproof feature that can withstand water-resistant depths of <30 m. The easy-to-care-for & maintain instructions make it ideal for children who are always on-the-go. The addition of a 150ml body spray in this gift set will make your child feel fresh after every activity they engage in, making this purchase an excellent value-for-money choice!, category=Kids' Fashion)\n", "ProductInfo(product_name=Motorcycle Accessories, product_desc=JRP HANDLE GRIP THAILAND , ▪️PER PAIR , ▪️WITH BLACK BAR END , ▪️HIGH QUALITY MATERIAL , ▪️MADE IN THAILAND , ▪️NO FADE , welcome to order our store,YYDS , We ensure that all products are inspected before packing and shipping. , *We do not have control over delivery once the courier pick up the order , *Delivery of an order may vary depending on courier led time. , *Only the courier is responsible during delivery period. , *Please check your order right away once you receive our package. , *If there are issue.please feel free to sent us a message.., category=Automotive & Motorcycle)\n", "ProductInfo(product_name=Women's Underwear, product_desc=Look great and feel even better with our Built-In Shapewear 2-in-1 Mesh Draped Cami Top! Crafted with a delicate mesh outer layer and a high elasticity shaping inner layer, this two-piece top-bodysuit combo provides both comfort and support. Its innovative structural design seamlessly blends the outer garment with the integrated shaping bodysuit, providing enhanced support and exceptional elasticity, perfect for everyday wear.Why you'll love it!• Cinch, carve, & conceal with this iconic combo of bodysuit & top—all in one!• Mesh fabric offers comfort, breathability, & a soft touch.• Waist & abdomen shaping bodysuit cinches & smoothes curves.• Remove and insert the built-in pads to your preferred fit.• Double-row snaps crotch design, with cotton fabric for easy bathroom use.• Versatile styling matches any outfit., category=Womenswear & Underwear)\n", "ProductInfo(product_name=Home Organizers, product_desc=, category=Home Supplies)\n", "ProductInfo(product_name=Tea & Coffeeware, product_desc=Shape: Basket , Number of Pieces: 4 , Compatible: <PERSON><PERSON>g K Compact, Keurig K-Classic, <PERSON><PERSON><PERSON> , Devices K200, category=Kitchenware)\n", "ProductInfo(product_name=Audio & Video, product_desc=Instructions: , 1. Please charge the headset before using it for the first time. , 2. Open the charging box and take out the headphones. , 3. Turn on Bluetooth in your device, search and connect. , ❥Specifications: , 1. Name: g20Clip-on wireless headphones , 2. Communication distance: 15m , 3 music time: 4-5 hours , 4Charging time: 1 hour , 5Battery capacity: 40mah (earbuds) 300mah (charging box) , 6. Applicable to: Android, iOS, macOS and windows systems, category=Phones & Electronics)\n", "ProductInfo(product_name=Kitchen Appliances, product_desc=, category=Household Appliances)\n", "ProductInfo(product_name=Ball Sports Equipment, product_desc=3 PACK EMOJI GOLF BALLS BEER GOLF HAPPY , FEATURES : , • Fun for all ages , • Makes a great gift , • Improve your mood, category=Sports & Outdoor)\n", "ProductInfo(product_name=Computer Accessories, product_desc=Note: This is a Keycap, Not a Keyboard!!! , Product information , [High Quality PBT Material]:The keycaps are made of high quality PBT material with first-class durability and good typing feel. Advanced five-side sublimation technology makes the pattern not easy to fade.[Sturdy and Durable]: The keycaps are made of 1.4mm thick PBT keycaps, CHERRY height makes you more comfortable to use and feel great. Suspended high keycaps, curved layout, ergonomic design, bring you a long-lasting and comfortable using experience.[U.S. Standard Layout]:125pcs PBT Cherry Profile keycaps adopt U.S. standard layout, which is a good choice for your DIY mechanical keyboard. Can be applied to 61/68/75/84/87/98/104/108 mechanical keyboard keys.[Demon Slayer Theme]:Immerse yourself in the world of Ghostbusters with these keycaps. The keycaps are finely designed and brightly colored, perfectly capturing the essence of the animation. Showcase your fan's heart like no other.[Guaranteed Customer Satisfaction]:We are committed to providing the highest level of customer satisfaction. If you encounter any problems with our keycaps, please feel free to contact our professional customer support team. We will be happy to assist you and ensure you are completely satisfied with your purchase. Enhance your gaming experience today and shop for these premium keycaps!, category=Computers & Office Equipment)\n", "ProductInfo(product_name=Pre-loved size 16 summer dress Casual Womenswear Style Women Lady Casual Wear Comfort Fitted Sleeveless, product_desc=This pre-loved size 16 summer dress is a must-have for any fashion-forward individual looking to add a unique piece to their wardrobe. The dress is perfect for any occasion, whether it's a casual day out or a formal event. The seller describes the dress as \"one-of-a-kind\", which means that you will be the only person in the world wearing it.The dress offers both style and comfort, making it a versatile addition to your wardrobe. The size 16 is a standard size that fits most people, so you can be confident that it will fit you comfortably. The dress is perfect for the summer season, as it is lightweight and breathable, making it comfortable to wear in hot weather.The seller encourages you to upgrade your summer wardrobe with this dress, as it is a unique piece that will make you stand out from the crowd. The dress is in excellent condition, as it has been worn before and is being sold as pre-loved. This means that you can be confident that you are getting a high-quality piece that will last you for many summers to come.In conclusion, this pre-loved size 16 summer dress is a perfect addition to any wardrobe. It offers both style and comfort, making it a versatile piece that can be worn for any occasion. The dress is in excellent condition and is being sold as pre-loved, so you can be confident that you are getting a high-quality piece that will last you for many summers to come., category=Pre-Owned)\n", "ProductInfo(product_name=Men's Tops, product_desc=The unisex soft-style t-shirt puts a new spin on casual comfort. Made from very soft materials, this tee is 100% cotton for solid colors. Heather colors and sports grey include polyester. The shoulders have twill tape for improved durability. There are no side seams. The collar is made with ribbed knitting to prevent curling damage. , .: Made with 100% ring-spun cotton, a lightweight fabric (4.5 oz/yd² (153 g/m²)), this unisex t-shirt feels like a bliss to wear all year round. .: The classic fit with the crew neckline deliver a clean, versatile style that can match any occasion, whether it's formal or semi-formal. .: All shirts feature a pearlized, tear-away label for total wearing comfort. .: Made using ethically grown and harvested US cotton. <PERSON><PERSON> is also a proud member of the US Cotton Trust Protocol ensuring ethical and sustainable means of production. This blank tee is certified by Oeko-Tex for safety and quality assurance..: Fabric blends: Heather colors - 35% ring-spun cotton, 65% polyester;  Sport Grey - 90% cotton, 10% polyester., category=Books, Magazines & Audio)\n", "ProductInfo(product_name=Hooded Thobe, product_desc=Sepia brown long sleeve hooded thobe. , •Top quality handmade cordon embroidery. , •Copper linnen full fur. , •Made for comfort.          , •Perfect for winter!, category=Muslim Fashion)\n", "ProductInfo(product_name=Hardware, product_desc=Enhance the security of your home with your personal door lock, the advanced home security door lock and reinforcement system. Designed to provide superior protection, this safety latch is built to withstand 800 lbs of force, making it 12 times stronger than a typical deadbolt. Our innovative design includes a groove for easy opening in emergency situations, ensuring you and your family's safety is never compromised. , Installation is a breeze, taking less than 5 minutes with a power screwdriver and drill. Our product is ideal for tenants and landlords, offering an extra layer of security for your family without the need for complex installation. , Not only does it prevent unauthorized entry, but it's also a childproofing solution, making it suitable for households with children, elderly parents, or anyone living alone. Keep your home safe or even your office space secure, the trusted choice in home security. , Choose peace of mind, choose Infinixe., category=Tools & Hardware)\n", "ProductInfo(product_name=Bedding, product_desc=Package Includes: 1 x Mattress Topper Protector Only , Box stitching with a piped edge and an elastic band across the four corners. Sherpa fleece on one side and microfiber on the other. , All-Year Use: Warm Plush Teddy Bear Fleece Material Texture on One Side, Cool Soft Microfiber Material on the Reverse. Because these Mattress Toppers are reversible, they may be used on both sides. The inside filling is made entirely of microfiber. , Material Quality: Anti-Bacterial and Dust Mite Resistant Mattress Toppers. 100% Super Soft Sherpa Teddy Fleece Quilted Hypoallergenic Toppers. , Size & Dimensions -- Single (90x190+5cm) - Double (135x190+5cm) - 4FT (122x190+5cm) - King (150x200+5cm) - Super King (180x200+5cm) , Care Instruction -- At 40°C, machine washable. Do Not Bleach Tumble Dry Low - Do Not Iron!. Please also follow the washing instructions on the label., category=Textiles & Soft Furnishings)\n", "ProductInfo(product_name=Natural Crystal, product_desc=Natural Green Fluorite Crystal Wand Pendant Single Point Crystal Necklace Green Crystal Charm Necklace Adjustable Rope for Daily Use , This necklace is handmade with high-quality rope chain. It measures about 24 inches in length and can be adjusted to fit your neck size. The pendant is lightweight and comfortable to wear. It is also easy to match with any outfit, as it has a neutral and elegant design.This necklace is perfect for anyone who loves natural stone jewelry, boho style gifts. It is a unique and meaningful gift for yourself or someone special. You can wear it alone or layer it with other necklaces for a stylish look. , - Material : Natural Green Fluorite Stone- Pendant Size: about 0.8*2 inch- Necklace Length: about 24 inch, adjustable, category=Jewelry Accessories & Derivatives)\n", "ProductInfo(product_name=Hardware, product_desc=Specification: , Product Name : Leak Sealer Spray , Material :Polyurethane + asphalt solvent , Alkali resistance ：Yes , Water resistance : High , Dry Time ： 2 Hours , Weight ：700ml , Color: Black , Function :Roof crack waterproofing /Great for repairing water pipes, leaks in lofts, gutter/down pipes, shed roofs, car doors, flat roofs , How to use: , 1.Clean the base surface with brush. Dust on the surface will affect the bonding effect. Make sure the surface dry and clean. , 2.Find the leaking point of the crack , 3.Open the cover, shake the spay bottle for 10 seconds before use. , 4.Aim at the leaking point of the crack and spray evenly, then repeat thin spray for several times. , 5.The surface cures in 20 to 30 minutes, in fact it takes 24 hours to fully cure., category=Tools & Hardware)\n", "ProductInfo(product_name=Power Tools, product_desc=Feature:    , 1. Fast and Efficient Turning:  Our wood dowel maker utilizes a turning method similar to that of lathe tools, resulting in quick and precise cuts.The efficient turning process saves time and effort, allowing you to complete your projects quickly and accurately. , 2. Greater Hole Diameter:  Our tmilling dowel round rod auxiliary tool's cutting diameter is 0.05mm larger than the marked diameter, so there is no need to worry about reducing the polishing size or residual material. This added diameter ensures that you have ample space for polishing, without worrying about accidentally removing too much material. , 3. Multiple Adjustable Sizes:  Our turning round rod tool comes in various sizes to accommodate different needs. With adjustable sizing options, you can use our tool for a wide range of projects, making it versatile and convenient. , 4. Durable Carbide Cutting Blades:  Our round rods wood sticks maker features carbide cutting blades that provide a smooth and sharp cutting experience. These durable blades ensure long-lasting performance and precision cuts for all your projects. , 5. Compact Size and Practical Accessories:  Our round rod milling cutter is made from high-quality materials and has a small and portable design that saves space. The small and compact size makes it easy to store and transport, while the high-quality materials ensure durability and reliable performance.This dowel maker jig set comes with a needle and drill bit, making it even more convenient to use. ,   , Specification: , Application: Wood Working Tool , Model Number: Dowel Maker Jig , Round hole specification: Diameter: 8/9/10/12/15/16/18/20(mm) , Product material: CNC aluminum alloy / anodized treatment , Blade material: discarded carbide blade , Blade specification: 15*15*2.5 30°R150 , Drill diameter: 4mm , Round nail head: , Hexagonal shank: Width across sides 9mm, diagonal Diameter 10mm (electric drill must be able to hold more than 10mm) , Self-tapping end: wood bottom hole 4mm , Overall length: about 42mm , Product size: about 85.00x80.00x50.00mm (3.35x3.15x1.97inch) ,   , Package List: , 1 x Dowel Maker Jig Set ; ,   , Note: , Due to the different monitor and light effect, the actual color of the item might be slightly different from the color showed on the pictures. Thank you! , Please allow 1-2cm measuring deviation due to manual measurement., category=Tools & Hardware)\n", "ProductInfo(product_name=Motorcycle Parts, product_desc=YAMAKOTO SIDE MIRROR  BRACKET  ,   , Note: Some photos may diifferent from actual product in terms of lighting during photo shoot and monitor display , Note: dont install your item if your not proffesional mechanic , it may damage the item and may void the warranty of the item, category=Automotive & Motorcycle)\n", "ProductInfo(product_name=Hair Accessories, product_desc=Welcome to  our shop! , Style: Women's , Style: simple , Treatment process: baking varnish , Applicable scenarios: gifts, school, daily life, etc. , Material: Acrylic , Quantity: 20 pcs/set , The package includes:20*hairpin , NOTES: , Due to different lighting and screen settings, the color of the product may be slightly different from the picture, please understand. , Due to manual measurement, please allow 1-3cm error. , When not using this product, please store it in a dry place and avoid contact with water as much as possible, which will extend the service life of the product. , If you have any questions or any problems with the goods,please feel free to contact us using \"Chat Now\" and we will reply as soon as possible., category=Fashion Accessories)\n", "ProductInfo(product_name=Men's Tops, product_desc=FAST AND FREE SHIPPING ??\n", " , Key Features: , Crafted from a blend of cotton and polyester, these tees offer both comfort and durability. The fabric wicks away moisture to keep you feeling fresh all day long. The tag-free design ensures that you won't experience any irritation or discomfort while wearing them. ,  These fitted white tees come in a pack of three so that you can always have one on hand when needed. They're great for layering under jackets or wearing alone with your favorite pair of jeans., category=Menswear & Underwear)\n", "ProductInfo(product_name=Home Appliances, product_desc=, category=Household Appliances)\n", "ProductInfo(product_name=Classic & Novelty Toys, product_desc=This Little Children Toy Push Game is perfect for focus training and stress relief. With its interactive design, kids can pop the bubbles over and over again, providing endless entertainment. The compact size makes it easy to carry around for on-the-go fun. Key features:- Interactive design - Stress-relieving - Compact size The Little Children Toy Push Game is a must-have for parents looking to keep their kids entertained while also helping them develop focus and attention skills. The pop-it design is both satisfying and calming, making it ideal for stress relief as well. Whether at home or on the go, this toy is sure to keep little ones engaged and entertained!, category=Toys & Hobbies)\n", "ProductInfo(product_name=Functional Bags, product_desc=product description:-Great choice for families. The size of 27××25×16cm can accommodate one person, including a large lunch box, water and some fruits.-Safe insulated lunch bag: high-quality Oxford fiber fabric makes it durable, tear-resistant and easy to clean-Safe lunch bag without BPA.-Food grade aluminum foil has thick EPE foam inside, which can keep your food fresh, warm/cold for more than 4 hours.-Suitable for summer and winter use.-Convenient and portable refrigerated lunch bag: refrigerator is available. Keep it in the refrigerator at any time to make it fresh. Or, you can put warm food in it, and this lunch bag will cool down within 4 hours.- , Multifunctional: This insulated lunch bag is also ideal for picnic bags, pre-dinner preparation bags, lunch boxes, shopping bags.-This is for family packaging, gray insulated lunch bags for men, cute lunch bags for women or children.-Fashionable portable products suitable for everyone., category=Luggage & Bags)\n", "ProductInfo(product_name=Drinkware, product_desc=, category=Kitchenware)\n", "ProductInfo(product_name=Women's Dresses, product_desc= Product information: Pattern: stripes/Plaid Color: Pink Waist type: high waist Size: S,M,L,XL Skirt length: long skirt Main fabric composition: Polyester (polyester fiber) Skirt Category: Dress Craft: fold ,  ,  Size Information: ,  , Note: , 1. Asian sizes are 1 to 2 sizes smaller than European and American people. Choose the larger size if your size between two sizes. Please allow 2-3cm differences due to manual measurement. 2. Please check the size chart carefully before you buy the item, if you don't know how to choose size, please contact our customer service. 3.As you know, the different computers display colors differently, the color of the actual item may vary slightly from the following images. , Packing list:  , Dress X1 , Product Image:, category=Fashion Accessories)\n", "ProductInfo(product_name=Comfort, Luxury, product_desc=Add comfort to your daily prayers with our Velvet Prayer Mat Thick Luxury Soft Padded Musallah NON SLIP Janamaz Muslim Rug. Made from soft velvet material, this prayer mat ensures you stay comfortable throughout your prayers. , This Soft Padded Prayer Mat is perfect for those who want a comfortable surface to pray on. Whether you're at home or in the mosque, this Janamaz Muslim Rug will help you stay focused on your worship without any discomfort or distraction. Get yours today! , Luxurious and Thick: The velvet touch material makes this prayer mat thick and luxurious, providing extra cushioning for your knees. , Non-Slip: The non-slip backing ensures that the prayer mat stays in place during use, providing additional safety during prayers. , Unisex Design: Our Velvet Prayer Mat is suitable for both men and women to use in all seasons. , Size - 70cm x 110cm, category=Muslim Fashion)\n"]}], "source": ["import pandas as pd\n", "import json\n", "import asyncio\n", "\n", "# 读取CSV文件\n", "data_df = pd.read_csv('./data/data_sample.csv')\n", "data_df = data_df.head(100)\n", "\n", "# 定义提取数据的函数\n", "def extract_data(row):\n", "    changed_data = row['changed_data']\n", "    if changed_data:\n", "        try:\n", "            data_json = json.loads(changed_data)\n", "            product_name = data_json.get('productName')\n", "            title = data_json.get('title')\n", "            description = data_json.get('description')\n", "            # 提取第一个 imageUrl\n", "            image_url = data_json['imageInfos'][0]['imageUrl'] if 'imageInfos' in data_json and data_json['imageInfos'] else None\n", "            return row['product_id'], product_name, title, description, row['first_category_name'], image_url\n", "        except (j<PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IndexError):\n", "            pass\n", "    return row['product_id'], None, None, None, row['first_category_name'], None\n", "\n", "# 应用函数提取数据，并创建新 DataFrame\n", "extracted_data = data_df.apply(lambda row: extract_data(row), axis=1)\n", "new_df = pd.DataFrame(extracted_data.tolist(), columns=['product_id', 'productName', 'title', 'description', 'first_category_name', 'imageUrl'])\n", "\n", "# 尝试读取已有的结果文件\n", "try:\n", "    processed_df = pd.read_csv('product_scripts_generation.csv')\n", "except FileNotFoundError:\n", "    processed_df = pd.DataFrame(columns=['product_id', 'productName', 'title', 'description', 'first_category_name', 'imageUrl', 'result'])\n", "\n", "# 找出尚未处理的项目\n", "unprocessed_df = new_df.merge(processed_df, on=['product_id', 'productName', 'title', 'description', 'first_category_name', 'imageUrl'], how='left', indicator=True)\n", "unprocessed_df = unprocessed_df[unprocessed_df['_merge'] == 'left_only'].drop(columns=['_merge', 'result'])\n", "\n", "# 定义处理每一行的异步函数，并添加错误处理逻辑\n", "def process_row(row):\n", "    product = ProductInfo(\n", "        product_name=row['productName'],\n", "        product_desc=row['description'],\n", "        category=row['first_category_name']\n", "    )\n", "    print(product)\n", "    # 尝试第一次运行\n", "    result_code, result = product_generate(product, industry_strategy_dict, category_industry_dict, agent)\n", "\n", "    # 如果返回值是 None，再尝试一次\n", "    if result_code is None:\n", "        result_code, result = product_generate(product, industry_strategy_dict, category_industry_dict, agent)\n", "\n", "    if result_code == 1:\n", "        result = {\n", "            \"productName\": f\"{product.product_name}\",\n", "            \"firstCategoryName\": f\"{product.category}\",\n", "            \"scripts\": result['scripts']\n", "            \n", "        }\n", "        \n", "        return result\n", "\n", "    return None\n", "\n", "# 初始化要添加的列\n", "unprocessed_df['result'] = None\n", "\n", "# 对每一行调用 product_generate 并将结果存储到新列中\n", "def main():\n", "    for index, row in unprocessed_df.iterrows():\n", "        result = process_row(row)\n", "        unprocessed_df.at[index, 'result'] = result\n", "\n", "# 运行事件循环\n", "try:\n", "    main()\n", "except Exception as e:\n", "    print(e)\n", "\n", "# 将新结果合并到处理后的 DataFrame 中\n", "final_df = pd.concat([processed_df, unprocessed_df])\n", "\n", "# 保存处理后的数据到一个新的或覆盖已有的 CSV 文件\n", "final_df.to_csv('product_scripts_generation.csv', index=False)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country_code</th>\n", "      <th>product_id</th>\n", "      <th>product_name</th>\n", "      <th>product_desc</th>\n", "      <th>first_category_name</th>\n", "      <th>scripts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>US</td>\n", "      <td>1729747757054266086</td>\n", "      <td>Phones &amp; Tablets</td>\n", "      <td>Tablet Spark pro 256GB (PremiumCopy Not origin...</td>\n", "      <td>Phones &amp; Electronics</td>\n", "      <td>[{'title': 'Direct Call to Action', 'text': 'D...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>US</td>\n", "      <td>1729401766432509682</td>\n", "      <td>Women's Tops</td>\n", "      <td>Perfect for those cosy nights in, this brand n...</td>\n", "      <td>Womenswear &amp; Underwear</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'I ab...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>US</td>\n", "      <td>1729725741624757117</td>\n", "      <td>Girls' Clothes</td>\n", "      <td>Children's size : , Can fit to L-XL-XXL , bust...</td>\n", "      <td>Kids' Fashion</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'I re...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>US</td>\n", "      <td>1729402081902956147</td>\n", "      <td>Indoor Furniture</td>\n", "      <td>This Liverpool FC - Defender chair is an absol...</td>\n", "      <td>Furniture</td>\n", "      <td>[{'title': 'Emphasizing Product Features', 'te...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>US</td>\n", "      <td>1729403389778497755</td>\n", "      <td>Car Interior Accessories</td>\n", "      <td>Transform your daily commute with the Hanging ...</td>\n", "      <td>Automotive &amp; Motorcycle</td>\n", "      <td>[{'title': 'Direct Call to Action', 'text': 'U...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>US</td>\n", "      <td>1729632152206608990</td>\n", "      <td>Costume Jewelry &amp; Accessories</td>\n", "      <td>NaN</td>\n", "      <td>Fashion Accessories</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'I ab...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>US</td>\n", "      <td>1729487269542073046</td>\n", "      <td>Canvas Bucket Bag</td>\n", "      <td>This Pre-Owned MCM VISETOS ESSENTIAL DRAWSTRIN...</td>\n", "      <td>Pre-Owned</td>\n", "      <td>[{'title': 'Emphasizing Product Features', 'te...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>US</td>\n", "      <td>1729700123834747299</td>\n", "      <td>Lights &amp; Lighting</td>\n", "      <td>NaN</td>\n", "      <td>Home Improvement</td>\n", "      <td>[{'title': 'Emphasizing Product Features', 'te...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>US</td>\n", "      <td>1729385781638632124</td>\n", "      <td>Sport &amp; Outdoor Clothing</td>\n", "      <td>Messi Inter Miami soccer Jersey : , * Our socc...</td>\n", "      <td>Sports &amp; Outdoor</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'I've...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>US</td>\n", "      <td>1729428580464625852</td>\n", "      <td>Women's Underwear</td>\n", "      <td>(random color)Power Belt , Dear buyers, please...</td>\n", "      <td>Womenswear &amp; Underwear</td>\n", "      <td>[{'title': 'Personal Testimony', 'text': 'I we...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  country_code           product_id                   product_name  \\\n", "0           US  1729747757054266086               Phones & Tablets   \n", "1           US  1729401766432509682                   Women's Tops   \n", "2           US  1729725741624757117                 Girls' Clothes   \n", "3           US  1729402081902956147               Indoor Furniture   \n", "4           US  1729403389778497755       Car Interior Accessories   \n", "5           US  1729632152206608990  Costume Jewelry & Accessories   \n", "6           US  1729487269542073046              Canvas Bucket Bag   \n", "7           US  1729700123834747299              Lights & Lighting   \n", "8           US  1729385781638632124       Sport & Outdoor Clothing   \n", "9           US  1729428580464625852              Women's Underwear   \n", "\n", "                                        product_desc      first_category_name  \\\n", "0  Tablet Spark pro 256GB (PremiumCopy Not origin...     Phones & Electronics   \n", "1  Perfect for those cosy nights in, this brand n...   Womenswear & Underwear   \n", "2  Children's size : , Can fit to L-XL-XXL , bust...            Kids' Fashion   \n", "3  This Liverpool FC - Defender chair is an absol...                Furniture   \n", "4  Transform your daily commute with the Hanging ...  Automotive & Motorcycle   \n", "5                                                NaN      Fashion Accessories   \n", "6  This Pre-Owned MCM VISETOS ESSENTIAL DRAWSTRIN...                Pre-Owned   \n", "7                                                NaN         Home Improvement   \n", "8  Messi Inter Miami soccer Jersey : , * Our socc...         Sports & Outdoor   \n", "9  (random color)Power Belt , Dear buyers, please...   Womenswear & Underwear   \n", "\n", "                                             scripts  \n", "0  [{'title': 'Direct Call to Action', 'text': 'D...  \n", "1  [{'title': 'Personal Testimony', 'text': 'I ab...  \n", "2  [{'title': 'Personal Testimony', 'text': 'I re...  \n", "3  [{'title': 'Emphasizing Product Features', 'te...  \n", "4  [{'title': 'Direct Call to Action', 'text': 'U...  \n", "5  [{'title': 'Personal Testimony', 'text': 'I ab...  \n", "6  [{'title': 'Emphasizing Product Features', 'te...  \n", "7  [{'title': 'Emphasizing Product Features', 'te...  \n", "8  [{'title': 'Personal Testimony', 'text': 'I've...  \n", "9  [{'title': 'Personal Testimony', 'text': 'I we...  "]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df_output = pd.read_csv('final_extracted_data_sample_new.csv')\n", "df_output['result'] = df_output['result'].apply(lambda x: eval(x)['scripts'])\n", "df_output['country_code'] = 'US'\n", "df_output = df_output[['country_code', 'product_id', 'productName', 'description', 'first_category_name', 'result']]\n", "df_output.rename(columns={'productName': 'product_name', 'description': 'product_desc', 'result': 'scripts'}, inplace=True)\n", "df_output.to_csv('product_scripts_generation_final.csv', index=False)\n", "df_output.head(10)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}