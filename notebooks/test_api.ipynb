{"cells": [{"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["from laplace import Client\n", "import json\n", "\n", "# psm = \"sd://oec.dms.llm?idc=ma&cluster=default\"\n", "# psm = \"tcp://[2605:340:cda2:1235:4e66:2acf:4d91:127a]:9267\"\n", "\n", "psm = \"tcp://[fdbd:dc61:18:205::19]:9478\"\n", "model_server = \"ECOM_MFIP_SERVER\"\n", "\n", "def laplace_request_transformer(request):\n", "    def encode_deal(data):\n", "        if isinstance(data, str):\n", "            return data.encode()\n", "        elif isinstance(data, dict):\n", "            return json.dumps(data).encode()\n", "        else:\n", "            return data\n", "    laplace_request = {}\n", "    for k, v in request.items():\n", "        if isinstance(v, list):\n", "            laplace_request[k] = [json.dumps(v).encode()]\n", "        else:\n", "            laplace_request[k] = [encode_deal(v)]\n", "    return laplace_request"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Library/Python/3.9/lib/python/site-packages/bytedtrace/__init__.py:108: UserWarning: [bytedtrace] global tracer is already initialized.\n", "  warnings.warn('[bytedtrace] global tracer is already initialized.')\n", "2025-03-05 16:16:58,456 - root - ERROR - [laplace] matx_inference ex\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/thriftpy2/transport/socket.py\", line 98, in open\n", "    self.sock.connect(addr)\n", "TimeoutError: [<PERSON>rr<PERSON> 60] Operation timed out\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/laplace/client/rpc_func.py\", line 310, in __call__\n", "    rsp = self._rpc_client.matx_inference(req)\n", "  File \"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/euler/client.py\", line 321, in call\n", "    client = self._get_thrift_client(env=ctx.persistent.get('env'))\n", "  File \"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/euler/client.py\", line 394, in _get_thrift_client\n", "    return self._get_thrift_client_without_mesh(ensure_text(env))\n", "  File \"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/euler/client.py\", line 407, in _get_thrift_client_without_mesh\n", "    return make_client(self._service_cls,\n", "  File \"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/euler/client.py\", line 696, in make_client\n", "    trans.open()\n", "  File \"thriftpy2/transport/framed/cyframed.pyx\", line 113, in thriftpy2.transport.framed.cyframed.TCyFramedTransport.open\n", "  File \"/Users/<USER>/Library/Python/3.9/lib/python/site-packages/thriftpy2/transport/socket.py\", line 105, in open\n", "    raise TTransportException(\n", "thriftpy2.transport.base.TTransportException: TTransportException(type=1, message=\"Could not connect to ('fdbd:dc61:18:205::19', 9478)\")\n"]}, {"ename": "TTransportException", "evalue": "TTransportException(type=1, message=\"Could not connect to ('fdbd:dc61:18:205::19', 9478)\")", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTimeoutError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/thriftpy2/transport/socket.py:98\u001b[0m, in \u001b[0;36mTSocket.open\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     96\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock\u001b[38;5;241m.\u001b[39msettimeout(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mconnect_timeout)\n\u001b[0;32m---> 98\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43maddr\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    100\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msocket_timeout:\n", "\u001b[0;31mTimeoutError\u001b[0m: [<PERSON>rrno 60] Operation timed out", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mTTransportException\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[21], line 19\u001b[0m\n\u001b[1;32m     17\u001b[0m new_batch_request \u001b[38;5;241m=\u001b[39m laplace_request_transformer(batch_request)\n\u001b[1;32m     18\u001b[0m client \u001b[38;5;241m=\u001b[39m Client(psm, timeout\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m150000\u001b[39m)\n\u001b[0;32m---> 19\u001b[0m output \u001b[38;5;241m=\u001b[39m \u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmatx_inference\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel_server\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnew_batch_request\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m------------------------------\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     21\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m output \u001b[38;5;129;01min\u001b[39;00m output\u001b[38;5;241m.\u001b[39moutput_bytes_lists[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124moutput\u001b[39m\u001b[38;5;124m'\u001b[39m]:\n", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/laplace/client/rpc_func.py:310\u001b[0m, in \u001b[0;36mmatx_inference.__call__\u001b[0;34m(self, model_name, input_lists, caller, ab_server, use_cache, update_cache, trace, input_dtypes)\u001b[0m\n\u001b[1;32m    308\u001b[0m     req\u001b[38;5;241m.\u001b[39mBase\u001b[38;5;241m.\u001b[39mCaller \u001b[38;5;241m=\u001b[39m caller\n\u001b[1;32m    309\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 310\u001b[0m     rsp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_rpc_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmatx_inference\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    311\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m rsp\n\u001b[1;32m    312\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/euler/client.py:321\u001b[0m, in \u001b[0;36mClient.call\u001b[0;34m(self, name, *args, **kwargs)\u001b[0m\n\u001b[1;32m    319\u001b[0m     client \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client\n\u001b[1;32m    320\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 321\u001b[0m     client \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_thrift_client\u001b[49m\u001b[43m(\u001b[49m\u001b[43menv\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mctx\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpersistent\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43menv\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    322\u001b[0m fn \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(client, name)\n\u001b[1;32m    324\u001b[0m ctx\u001b[38;5;241m.\u001b[39m_url \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_url\n", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/euler/client.py:394\u001b[0m, in \u001b[0;36mClient._get_thrift_client\u001b[0;34m(self, env)\u001b[0m\n\u001b[1;32m    392\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_mesh_mode():\n\u001b[1;32m    393\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_thrift_client_in_mesh()\n\u001b[0;32m--> 394\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_thrift_client_without_mesh\u001b[49m\u001b[43m(\u001b[49m\u001b[43mensure_text\u001b[49m\u001b[43m(\u001b[49m\u001b[43menv\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/euler/client.py:407\u001b[0m, in \u001b[0;36mClient._get_thrift_client_without_mesh\u001b[0;34m(self, env)\u001b[0m\n\u001b[1;32m    404\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhost\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m connection_args \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mport\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m connection_args:\n\u001b[1;32m    405\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_instance \u001b[38;5;241m=\u001b[39m (connection_args[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhost\u001b[39m\u001b[38;5;124m\"\u001b[39m], connection_args[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mport\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[0;32m--> 407\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mmake_client\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_service_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    408\u001b[0m \u001b[43m                   \u001b[49m\u001b[43mproto_factory\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_proto_factory\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    409\u001b[0m \u001b[43m                   \u001b[49m\u001b[43mtrans_factory\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_trans_factory\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    410\u001b[0m \u001b[43m                   \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mconnection_args\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/euler/client.py:696\u001b[0m, in \u001b[0;36mmake_client\u001b[0;34m(service, host, port, unix_socket, proto_factory, trans_factory, socket_family, socket_timeout, connect_timeout)\u001b[0m\n\u001b[1;32m    694\u001b[0m trans \u001b[38;5;241m=\u001b[39m trans_factory\u001b[38;5;241m.\u001b[39mget_transport(socket)\n\u001b[1;32m    695\u001b[0m proto \u001b[38;5;241m=\u001b[39m proto_factory\u001b[38;5;241m.\u001b[39mget_protocol(trans)\n\u001b[0;32m--> 696\u001b[0m \u001b[43mtrans\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    697\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m TClient(service, proto)\n", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/thriftpy2/transport/framed/cyframed.pyx:113\u001b[0m, in \u001b[0;36mthriftpy2.transport.framed.cyframed.TCyFramedTransport.open\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32m~/Library/Python/3.9/lib/python/site-packages/thriftpy2/transport/socket.py:105\u001b[0m, in \u001b[0;36mTSocket.open\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    103\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (socket\u001b[38;5;241m.\u001b[39merror, \u001b[38;5;167;01mOSError\u001b[39;00m):\n\u001b[1;32m    104\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclose()\n\u001b[0;32m--> 105\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m TTransportException(\n\u001b[1;32m    106\u001b[0m         \u001b[38;5;28mtype\u001b[39m\u001b[38;5;241m=\u001b[39mTTransportException\u001b[38;5;241m.\u001b[39mNOT_OPEN,\n\u001b[1;32m    107\u001b[0m         message\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCould not connect to \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m \u001b[38;5;28mstr\u001b[39m(addr))\n", "\u001b[0;31mTTransportException\u001b[0m: TTransportException(type=1, message=\"Could not connect to ('fdbd:dc61:18:205::19', 9478)\")"]}], "source": ["\n", "\n", "prompt = \"You are an assistant for a TikTok seller. You will be given the product name and description, as well as a script generated by LLM. Your task is to generate a live broadcast script with a more human-like style. There is a script generated by LLM as a reference, you can polish it. The script you generate should be on the below structure, [Label_level_1] is the first class label system, including Opening, Interaction, Explaining Product and Urging Purchase. Under every first class, there are some second level labels <Label_level_2>, you should generate the specific content under each second level label. The structure of the labels are as follows: \\n [Opening]\\n<Warm and Friendly Greeting>\\n(Content)\\n<Personal Introduction>\\n(Content)\\n<Expressing Gratitude>\\n(Content)\\n[Interaction]\\n<Personal Engagement>\\n(Content)\\n<Encouraging Interaction>\\n(Content)\\n<Personal Connection>\\n(Content)\\n[Explaining Product]\\n<Demonstrating Product Usage>\\n(Content)\\n<Highlighting Product Features>\\n(Content)\\n<Detailed Product Description>\\n(Content)\\n<Addressing Pain Points>\\n(Content)\\n<Personal Testimony>\\n(Content)\\n[Urging Purchase]\\n<Creating Urgency and Scarcity>\\n(Content)\\n<Highlighting Discounts and Offers>\\n(Content)\\n<Call to Action (CTA)>\\n(Content)\\nProduct information and script generated by LLM are as follows:\\nproduct name:\\nSSSKIN (Dapat 2) Vitamin C Body Serum\\nproduct description:\\nKehilangan kilau dan kelembutan kulit tubuhmu? Hal ini seringkali membuat kamu kurang percaya diri, jangan biarkan mengganggu masalah kulit tubuh mengganggu hari-harimu. Gunakan SSSKIN Vitamin C Body Serum untuk tampil dengan kulit sehat dan cerah. Serum ini tidak hanya menghidrasi dan menjaga kelembapan kulit, tetapi juga mencerahkan dan meremajakan kulit yang lelah. Formula khusus yang kaya dengan komposisi Skin Lightening membantu mengurangi hiperpigmentasi, membuat kulit tampak lebih cerah dan merata.\\nManfaat Utama:\\nMenghidrasi dan Menjaga Kelembapan: Formula khusus membantu menjaga kelembapan kulit, membuatnya terasa lembut dan sehat.\\nMencerahkan Kulit: KOMPOSISI Skin Lightening membantu meratakan warna kulit, mengurangi hiperpigmentasi, dan memberikan kilau alami.\\nMeremajakan Kulit: Rutin menggunakan Body Serum ini akan membantu meremajakan kulit, membuatnya tampak lebih segar dan bercahaya.\\nCara Pakai:\\nTuangkan sedikit Body Serum ke telapak tangan.\\nGosok kedua telapak tangan untuk meratakan serum.\\nAplikasikan secara merata ke seluruh kulit tubuh setelah mandi, saat kulit masih lembap.\\nPijat dengan lembut untuk membantu penyerapan.\\nKomposisi:\\nVitamin C\\nKomposisi Skin Lightening\\nPelembap Alami\\nJangan tunggu lagi untuk buat kulit tubuh lebih sehat, pesan sekarang! \\nBPOM:\\n NA18230111964\\nNetto:\\n 100ml\\nPERHATIAN:\\nGaransi berlaku 1x24 jam setelah paket diterima dengan melampirkan video unboxing. Tanpa Video Unboxing Garansi Hangus! (Membeli berarti menyetujui Syarat & Ketentuan SSSKIN)\\nscript generated by LLM:\\n[Opening]\\n<Warm and Friendly Greeting> Hai semuanya! Terima kasih sudah bergabung di live ini!\\n<Expressing Gratitude> Saya sangat senang bisa berbagi produk luar biasa ini dengan kalian.\\n[Interaction]\\n<Personal Engagement> Siapa di sini yang sudah pernah mencoba vitamin C body serum?\\n<Encouraging Interaction> Ayo, tulis di kolom komentar pengalaman kalian!\\n[Explaining Product]\\n<Demonstrating Product Usage> Lihat, saya akan tunjukkan cara menggunakan SSSKIN Vitamin C Body Serum. Tuangkan sedikit ke telapak tangan, gosok, dan aplikasikan ke seluruh tubuh setelah mandi.\\n<Highlighting Product Features> Serum ini menghidrasi, mencerahkan, dan meremajakan kulit. Dengan komposisi Skin Lightening, kulit kalian akan tampak lebih cerah dan merata.\\n[Urging Purchase]\\n<Creating Urgency and Scarcity> Jangan tunggu lagi! Stok terbatas, pesan sekarang juga!\\n<Call to Action (CTA)> Klik link di bio untuk mendapatkan SSSKIN Vitamin C Body Serum dan rasakan perbedaannya!\\n\\nThe content you generate must include all first-level labels. Each first-level tag does not need to include all second-level labels, but it must include at least two second-level labels. \\nOnce you have filled in all the content, stop generating output. \\n\"\n", "batch_request = {\n", "    \"input\": prompt,\n", "    \"params\": {\n", "        \"mfip_n\": 1,\n", "        \"mfip_temperature\": 0.75,\n", "        \"mfip_top_k\": 1,\n", "        \"mfip_top_p\": 0.75,\n", "        \"mfip_max_new_tokens\": 4096,\n", "        \"mfip_stop_words\": [\"<|eot_id|>\"],\n", "        \"mfip_inprompt_flag\": False,\n", "        \"mfip_enable_print_in_prompt\": False,\n", "        \"mfip_enable_print_out_prompt\": False,\n", "    },\n", "    \"api_key\":\"client psm\",    # 限流场景\n", "}\n", "new_batch_request = laplace_request_transformer(batch_request)\n", "client = Client(psm, timeout=150000)\n", "output = client.matx_inference(model_server, new_batch_request)\n", "print(\"------------------------------\")\n", "for output in output.output_bytes_lists['output']:\n", "    print(output.decode())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}