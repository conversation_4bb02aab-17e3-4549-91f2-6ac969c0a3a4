{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import openai\n", "from tqdm import tqdm\n", "\n", "\n", "class OpenAIAgent:\n", "    def __init__(self, api_key, model=\"gpt-4-0613\"):\n", "        self.client = openai.AzureOpenAI(\n", "            azure_endpoint=\"https://search-va.byteintl.net/gpt/openapi/online/v2/crawl\",\n", "            api_version=\"2024-03-01-preview\",\n", "            api_key=api_key\n", "        )\n", "        self.model = model\n", "\n", "    def system_message(self, message):\n", "        return message\n", "\n", "    def assistant_message(self, message):\n", "        return message\n", "\n", "    def user_message(self, message):\n", "        return message\n", "\n", "    @staticmethod\n", "    def clean_json_output(output):\n", "        output = output.strip()\n", "        if output.startswith(\"```json\"):\n", "            output = output[7:]\n", "        if output.endswith(\"```\"):\n", "            output = output[:-3]\n", "        cleaned_output = output.strip()\n", "\n", "        try:\n", "            json_data = json.loads(cleaned_output)\n", "        except json.JSONDecodeError as e:\n", "            print(f\"JSON decoding error: {e}\")\n", "            return cleaned_output\n", "\n", "        def clean_json(data):\n", "            if isinstance(data, dict):\n", "                return {key: clean_json(value) for key, value in data.items()}\n", "            elif isinstance(data, list):\n", "                return [clean_json(item) for item in data]\n", "            elif isinstance(data, str):\n", "                return \"\" if data.lower() in [\"unknown\", \"na\", \"null\"] else data\n", "            else:\n", "                return data\n", "\n", "        cleaned_json_data = clean_json(json_data)\n", "        return cleaned_json_data\n", "\n", "    def run_openai_task(self, system_message, assistant_message, user_message):\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": self.system_message(system_message)},\n", "            {\"role\": \"assistant\", \"content\": self.assistant_message(assistant_message)},\n", "            {\"role\": \"user\", \"content\": self.user_message(user_message)}\n", "        ]\n", "\n", "        completion = self.client.chat.completions.create(\n", "            extra_headers={\"X-TT-LOGID\": \"abc\"},  # 请务必带上此header，方便定位问题\n", "            model=self.model,\n", "            messages=messages,\n", "            temperature=0,\n", "            frequency_penalty=0,\n", "            presence_penalty=0\n", "        )\n", "\n", "        json_data = completion.model_dump_json()\n", "        data_dict = json.loads(json_data)\n", "\n", "        result = self.clean_json_output(data_dict['choices'][0]['message']['content'])\n", "        usage_data = data_dict.get('usage', {})\n", "        total_tokens = usage_data.get('total_tokens', 0)\n", "        prompt_tokens = usage_data.get('prompt_tokens', 0)\n", "        completion_tokens = usage_data.get('completion_tokens', 0)\n", "\n", "        return result, total_tokens, prompt_tokens, completion_tokens\n", "\n", "    def generate_opening_script(self, industry, attribute, gmv_percentile, strategies, country_code = 'US'):\n", "\n", "        language_mapping = {\n", "            'US': 'English',\n", "            'MY': 'Malay',\n", "            'PH': 'Filipino',\n", "            'TH': 'Thai',\n", "            'SG': 'English',\n", "            'ID': 'Indonesian',\n", "            'VN': 'Vietnamese'\n", "        }\n", "        system_message = \"You are an assistant for a TikTok seller. Your task is to generate engaging opening scripts for live streams based on given strategies.\"\n", "\n", "        strategy_explanations = \"\\n\".join([f\"1. **{strategy['name']}**: {strategy['explanation']}\" for strategy in strategies])\n", "        strategy_names = \", \".join([strategy['name'] for strategy in strategies])\n", "        examples = \"\\n\".join([f\"**Examples for {strategy['name']}**: {strategy['examples']}\" for strategy in strategies])\n", "\n", "        assistant_message = f\"\"\"\n", "        Please generate opening scripts based on the following information:\n", "\n", "        - **Industry**: {industry}\n", "        - **Attribute**: {attribute}\n", "        - **GMV Percentile**: {gmv_percentile}\n", "        - **Top Strategies**: {strategy_names}\n", "\n", "        **Strategy Explanations**:\n", "        {strategy_explanations}\n", "\n", "        {examples}\n", "\n", "        **Generate Opening Scripts**:\n", "        Please generate 3-5 opening scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies.\n", "\n", "        Return the output in JSON format like this:\n", "        {{\n", "            \"scripts\": [\n", "                \"Script 1\",\n", "                \"Script 2\",\n", "                \"Script 3\"\n", "            ]\n", "        }}\n", "        \"\"\"\n", "\n", "        user_message = f\"\"\"\n", "        Industry: {industry}\n", "        Attribute: {attribute}\n", "        GMV Percentile: {gmv_percentile}\n", "        Top Strategies: {strategy_names}\n", "\n", "        Please generate 3-5 opening scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies. Please use {language_mapping[country_code]} to generate the scripts.\n", "\n", "        Return the output in JSON format like this:\n", "        {{\n", "            \"scripts\": [\n", "                \"Script 1\",\n", "                \"Script 2\",\n", "                \"Script 3\"\n", "            ]\n", "        }}\n", "        \"\"\"\n", "\n", "        result, total_tokens, prompt_tokens, completion_tokens = self.run_openai_task(system_message, assistant_message, user_message)\n", "        return result, total_tokens, prompt_tokens, completion_tokens\n", "\n", "    def generate_closing_script(self, industry, attribute, gmv_percentile, strategies, country_code = 'US'):\n", "\n", "        language_mapping = {\n", "            'US': 'English',\n", "            'MY': 'Malay',\n", "            'PH': 'Filipino',\n", "            'TH': 'Thai',\n", "            'SG': 'English',\n", "            'ID': 'Indonesian',\n", "            'VN': 'Vietnamese'\n", "        }\n", "        \n", "        system_message = \"You are an assistant for a TikTok seller. Your task is to generate engaging closing scripts for live streams based on given strategies.\"\n", "\n", "        strategy_explanations = \"\\n\".join([f\"1. **{strategy['name']}**: {strategy['explanation']}\" for strategy in strategies])\n", "        strategy_names = \", \".join([strategy['name'] for strategy in strategies])\n", "        examples = \"\\n\".join([f\"**Examples for {strategy['name']}**: {strategy['examples']}\" for strategy in strategies])\n", "\n", "        assistant_message = f\"\"\"\n", "        Please generate closing scripts based on the following information:\n", "\n", "        - **Industry**: {industry}\n", "        - **Attribute**: {attribute}\n", "        - **GMV Percentile**: {gmv_percentile}\n", "        - **Top Strategies**: {strategy_names}\n", "\n", "        **Strategy Explanations**:\n", "        {strategy_explanations}\n", "\n", "        {examples}\n", "\n", "        **Generate Closing Scripts**:\n", "        Please generate 3-5 closing scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies.\n", "\n", "        Return the output in JSON format like this:\n", "        {{\n", "            \"scripts\": [\n", "                \"Script 1\",\n", "                \"Script 2\",\n", "                \"Script 3\"\n", "            ]\n", "        }}\n", "        \"\"\"\n", "\n", "        user_message = f\"\"\"\n", "        Industry: {industry}\n", "        Attribute: {attribute}\n", "        GMV Percentile: {gmv_percentile}\n", "        Top Strategies: {strategy_names}\n", "\n", "        Please generate 3-5 closing scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies. Please use {language_mapping[country_code]} to generate the scripts.\n", "\n", "        Return the output in JSON format like this:\n", "        {{\n", "            \"scripts\": [\n", "                \"Script 1\",\n", "                \"Script 2\",\n", "                \"Script 3\"\n", "            ]\n", "        }}\n", "        \"\"\"\n", "\n", "        result, total_tokens, prompt_tokens, completion_tokens = self.run_openai_task(system_message, assistant_message, user_message)\n", "        return result, total_tokens, prompt_tokens, completion_tokens\n", "\n", "    def generate_product_script(self, industry, attribute, gmv_percentile, strategies, product_name):\n", "        system_message = \"You are an assistant for a TikTok seller. Your task is to generate engaging product scripts for live streams based on given strategies.\"\n", "\n", "        strategy_explanations = \"\\n\".join([f\"1. **{strategy['name']}**: {strategy['explanation']}\" for strategy in strategies])\n", "        strategy_names = \", \".join([strategy['name'] for strategy in strategies])\n", "        examples = \"\\n\".join([f\"**Examples for {strategy['name']}**: {strategy['examples']}\" for strategy in strategies])\n", "\n", "        assistant_message = f\"\"\"\n", "        Please generate product scripts based on the following information:\n", "\n", "        - **Industry**: {industry}\n", "        - **Attribute**: {attribute}\n", "        - **GMV Percentile**: {gmv_percentile}\n", "        - **Top Strategies**: {strategy_names}\n", "        - **Product Name**: {product_name}\n", "\n", "        **Strategy Explanations**:\n", "        {strategy_explanations}\n", "\n", "        {examples}\n", "\n", "        **Generate Product Scripts**:\n", "        Please generate 3-5 product scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies.\n", "\n", "        Return the output in JSON format like this:\n", "        {{\n", "            \"scripts\": [\n", "                \"Script 1\",\n", "                \"Script 2\",\n", "                \"Script 3\"\n", "            ]\n", "        }}\n", "        \"\"\"\n", "\n", "        user_message = f\"\"\"\n", "        Industry: {industry}\n", "        Attribute: {attribute}\n", "        GMV Percentile: {gmv_percentile}\n", "        Top Strategies: {strategy_names}\n", "        Product Name: {product_name}\n", "\n", "        Please generate 3-5 product scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies.\n", "\n", "        Return the output in JSON format like this:\n", "        {{\n", "            \"scripts\": [\n", "                \"Script 1\",\n", "                \"Script 2\",\n", "                \"Script 3\"\n", "            ]\n", "        }}\n", "        \"\"\"\n", "\n", "        result, total_tokens, prompt_tokens, completion_tokens = self.run_openai_task(system_message, assistant_message, user_message)\n", "        return result, total_tokens, prompt_tokens, completion_tokens\n", "\n"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Opening Script Result: {'scripts': [\"Hey everyone! How are you all doing today? I see some familiar faces already! It's so great to have you here. Today, we're diving into some amazing beauty and personal care products that I absolutely love. Stick around because you won't want to miss what's coming up!\", \"Hello, beautiful people! I just got up, I just got up, I just got up and I'm so excited to share my morning routine with you. Let's get into these beauty essentials that have transformed my skin. Who's ready to glow with me?\", \"Welcome back, everyone! Push it out, push it out, push it out! We're going to explore some incredible beauty finds today. I can't wait to show you what I've been loving lately. Let's make this session unforgettable!\", \"Hi there, gorgeous! How's everyone feeling today? I see some familiar faces, and I'm thrilled to connect with you all again. Today, we're focusing on beauty must-haves that you need in your life. Let's get started!\", \"Hey, hey, hey! Check check check check these amazing products out! I'm so excited to share my top beauty picks with you today. Let's dive in and discover what makes these items so special. Are you ready?\"]}\n", "Total Tokens (Opening): 686\n", "Prompt Tokens (Opening): 422\n", "Comple<PERSON> (Opening): 264\n"]}], "source": ["# 示例使用\n", "\n", "# 初始化代理类对象\n", "# agent = OpenAIAgent(api_key=\"nCUBvtcbgzNQokqFxDQyfnKKAvIlHHVq\", model = 'gpt-4o-2024-08-06')\n", "agent = OpenAIAgent(api_key=\"X3zrNBdXZ1wQS1EnqMXP4bkFBBMELMPB\", model = 'gpt-4o-2024-08-06')\n", "\n", "# 示例策略\n", "strategies = [\n", "    {\"name\": \"Personal Connection\", \n", "    \"explanation\": \"Establishing a personal connection with the audience through personal stories or direct engagement.\",\n", "    \"examples\":'\"How are you all doing today?\", \"I see some familiar faces!\", \"kaylee, what\\'s up baby?\"'\n", "    },\n", "    {\"name\": \"<PERSON>eti<PERSON>\", \n", "    \"explanation\": \"Repeating key phrases or words to create emphasis and capture attention.\",\n", "    \"examples\":'\"check check check check,\" \"i just got up i just got up i just got up,\" \"push it out push it out push it out\"'}\n", "]\n", "\n", "# 生成开场话术\n", "result_opening, total_tokens_opening, prompt_tokens_opening, completion_tokens_opening = agent.generate_opening_script(\n", "    industry=\"Beauty & Personal Care\",\n", "    attribute=\"Affiliate\",\n", "    gmv_percentile=\"G1\",\n", "    strategies=strategies\n", ")\n", "\n", "print(\"Opening Script Result:\", result_opening)\n", "print(\"Total Tokens (Opening):\", total_tokens_opening)\n", "print(\"Prompt Tokens (Opening):\", prompt_tokens_opening)\n", "print(\"Completion Tokens (Opening):\", completion_tokens_opening)\n", "\n"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Closing Script Result: {'scripts': [\"Hey everyone, it's been amazing connecting with you today! I see some familiar faces, and I just want to say thank you for joining me. Remember, beauty is all about feeling good, feeling good, feeling good! Don't forget to check out the affiliate links for exclusive deals. Until next time, stay fabulous!\", \"Wow, what a fantastic session! I love seeing all your comments and questions. Kaylee, what's up baby? You always bring the energy! Remember, skincare is key, skincare is key, skincare is key! Check out the links for some great products. See you soon!\", \"Thank you all for tuning in! It's always a pleasure to share these beauty tips with you. How are you all doing today? I hope you're feeling inspired! Remember, glow up, glow up, glow up! Don't forget to explore the affiliate links for special offers. Take care!\", \"It's been a wonderful time chatting with you all! I see some familiar faces, and it warms my heart. Remember, self-care is important, self-care is important, self-care is important! Check out the links for amazing products. Until next time, keep shining!\", \"Thanks for spending your time with me today! I love connecting with each of you. How are you all doing today? Remember, beauty is confidence, beauty is confidence, beauty is confidence! Don't forget to check the affiliate links for exclusive deals. See you soon!\"]}\n", "Total Tokens (Closing): 728\n", "Prompt Tokens (Closing): 422\n", "Completion Tokens (Closing): 306\n"]}], "source": ["# 生成结尾话术\n", "result_closing, total_tokens_closing, prompt_tokens_closing, completion_tokens_closing = agent.generate_closing_script(\n", "    industry=\"Beauty & Personal Care\",\n", "    attribute=\"Affiliate\",\n", "    gmv_percentile=\"G1\",\n", "    strategies=strategies\n", ")\n", "\n", "print(\"Closing Script Result:\", result_closing)\n", "print(\"Total Tokens (Closing):\", total_tokens_closing)\n", "print(\"Prompt Tokens (Closing):\", prompt_tokens_closing)\n", "print(\"Completion Tokens (Closing):\", completion_tokens_closing)\n", "\n"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["# # 生成商品话术\n", "# product_name = \"Super Hydrating Face Mask\"\n", "# result_product, total_tokens_product, prompt_tokens_product, completion_tokens_product = agent.generate_product_script(\n", "#     industry=\"Beauty & Personal Care\",\n", "#     attribute=\"Affiliate\",\n", "#     gmv_percentile=\"G1\",\n", "#     strategies=strategies,\n", "#     product_name=product_name\n", "# )\n", "\n", "# print(\"Product Script Result:\", result_product)\n", "# print(\"Total Tokens (Product):\", total_tokens_product)\n", "# print(\"Prompt Tokens (Product):\", prompt_tokens_product)\n", "# print(\"Completion Tokens (Product):\", completion_tokens_product)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>num_vids_analysed</th>\n", "      <th>script_type</th>\n", "      <th>category</th>\n", "      <th>seller_type</th>\n", "      <th>live_gmv_percentile</th>\n", "      <th>Strategy</th>\n", "      <th>Explanation</th>\n", "      <th>Count</th>\n", "      <th>Examples</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Personal Testimony</td>\n", "      <td>Sharing personal experiences to build trust an...</td>\n", "      <td>10</td>\n", "      <td>\"i have the black one and i love it\", \"i didn'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Demonstrating Product Usage</td>\n", "      <td>Showing how the product works in real-life sce...</td>\n", "      <td>9</td>\n", "      <td>\"i'll put it on and show you what it looks lik...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Emphasizing Versatility</td>\n", "      <td>Highlighting how the product can be used in va...</td>\n", "      <td>8</td>\n", "      <td>\"ideal for a carry on or for just a shorter tr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>Personal Testimony</td>\n", "      <td>Sharing personal experiences to build trust an...</td>\n", "      <td>15</td>\n", "      <td>\"my husband uses it every day on his lunch bre...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>Highlighting Product Features</td>\n", "      <td>Emphasizing specific features of the product t...</td>\n", "      <td>10</td>\n", "      <td>\"super quiet\", \"it has round retro keys\", \"thr...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   num_vids_analysed script_type category seller_type live_gmv_percentile  \\\n", "0                 50         NaN   beauty   affiliate                  G1   \n", "1                 50         NaN   beauty   affiliate                  G1   \n", "2                 50         NaN   beauty   affiliate                  G1   \n", "3                 50         NaN   beauty   affiliate                  G3   \n", "4                 50         NaN   beauty   affiliate                  G3   \n", "\n", "                        Strategy  \\\n", "0             Personal Testimony   \n", "1    Demonstrating Product Usage   \n", "2        Emphasizing Versatility   \n", "3             Personal Testimony   \n", "4  Highlighting Product Features   \n", "\n", "                                         Explanation  Count  \\\n", "0  Sharing personal experiences to build trust an...     10   \n", "1  Showing how the product works in real-life sce...      9   \n", "2  Highlighting how the product can be used in va...      8   \n", "3  Sharing personal experiences to build trust an...     15   \n", "4  Emphasizing specific features of the product t...     10   \n", "\n", "                                            Examples  \n", "0  \"i have the black one and i love it\", \"i didn'...  \n", "1  \"i'll put it on and show you what it looks lik...  \n", "2  \"ideal for a carry on or for just a shorter tr...  \n", "3  \"my husband uses it every day on his lunch bre...  \n", "4  \"super quiet\", \"it has round retro keys\", \"thr...  "]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["# 读取CSV文件并处理数据\n", "# df = pd.read_csv('./data/live_narrative_intro_strategy.csv')\n", "# df['script_type'] = 'opening'\n", "# df['is_seller'] = df['is_seller'].map({1: 'seller', 0: 'affiliate'})\n", "# df.rename(columns={'is_seller': 'seller_type'}, inplace=True)\n", "\n", "# df1 = pd.read_csv('./data/live_narrative_ending_strategy.csv')\n", "# df1['script_type'] = 'closing'\n", "# df1['is_seller'] = df1['is_seller'].map({1: 'seller', 0: 'affiliate'})\n", "# df1.rename(columns={'is_seller': 'seller_type'}, inplace=True)\n", "# df = pd.concat([df, df1])\n", "\n", "# df = pd.read_csv('./data/live_narrative_strategy_all.csv')\n", "df = pd.read_csv('./data/live_narrative_strategy_new.csv')\n", "\n", "df['is_seller'] = df['is_seller'].map({1: 'seller', 0: 'affiliate'})\n", "df.rename(columns={'is_seller': 'seller_type', 'narrative_type': 'script_type'}, inplace=True)\n", "df['script_type'] = df['script_type'].map({'intro': 'opening', 'ending': 'closing'})\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'Personal Testimony',\n", "  'explanation': 'Sharing personal experiences to build trust and relatability with the audience.',\n", "  'examples': '\"i have the black one and i love it\", \"i didn\\'t buy this for gaming. i bought this cause i work from home\", \"i work from home, so watching this all day is very pleasing to me\"'},\n", " {'name': 'Demonstrating Product Usage',\n", "  'explanation': 'Showing how the product works in real-life scenarios to help the audience visualize its practicality and benefits.',\n", "  'examples': '\"i\\'ll put it on and show you what it looks like on my neck\", \"we got the galaxy light projecting onto the ceiling right there\", \"it senses when you\\'re over it and then you do your business, tap, tap, tap, and it closes\"'},\n", " {'name': 'Emphasizing Versatility',\n", "  'explanation': 'Highlighting how the product can be used in various scenarios to appeal to a broader audience.',\n", "  'examples': '\"ideal for a carry on or for just a shorter trip\", \"you can project it onto your wall or like kind of at an angle\", \"do fun food cooking videos\"'},\n", " {'name': 'Personal Testimony',\n", "  'explanation': 'Sharing personal experiences to build trust and relate to the audience.',\n", "  'examples': '\"my husband uses it every day on his lunch break\", \"i actually don\\'t use it for gaming, but i did buy one for my son\", \"i love this one so much more because i can see my whole outfit\"'},\n", " {'name': 'Highlighting Product Features',\n", "  'explanation': 'Emphasizing specific features of the product to showcase its benefits.',\n", "  'examples': '\"super quiet\", \"it has round retro keys\", \"three different sizes are included the 20 inch the 24 inch and then the 28 inch\"'}]"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["strategies = df[['Strategy', 'Explanation', 'Examples']].rename(columns={'Strategy': 'name', 'Explanation': 'explanation', 'Examples': 'examples'}).to_dict(orient='records')\n", "strategies[:5]"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>category</th>\n", "      <th>industry</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Womenswear &amp; Underwear</td>\n", "      <td>fashion</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Menswear &amp; Underwear</td>\n", "      <td>fashion</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Household Appliances</td>\n", "      <td>home</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Sports &amp; Outdoor</td>\n", "      <td>health</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Phones &amp; Electronics</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 category                     industry\n", "0  Womenswear & Underwear                      fashion\n", "1    Menswear & Underwear                      fashion\n", "2    Household Appliances                         home\n", "3        Sports & Outdoor                       health\n", "4    Phones & Electronics  electronics, office & books"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["df_category = pd.read_csv('./data/category_industry_df.csv')\n", "df_category.head()"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>category</th>\n", "      <th>industry</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Womenswear &amp; Underwear</td>\n", "      <td>fashion</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Menswear &amp; Underwear</td>\n", "      <td>fashion</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Household Appliances</td>\n", "      <td>home</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Sports &amp; Outdoor</td>\n", "      <td>health</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Phones &amp; Electronics</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Muslim Fashion</td>\n", "      <td>fashion</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Computers &amp; Office Equipment</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Fashion Accessories</td>\n", "      <td>fashion</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Luggage &amp; Bags</td>\n", "      <td>fashion</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Home Supplies</td>\n", "      <td>home</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Beauty &amp; Personal Care</td>\n", "      <td>beauty</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Pet Supplies</td>\n", "      <td>home</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Shoes</td>\n", "      <td>fashion</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Toys &amp; Hobbies</td>\n", "      <td>collectibles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Kitchenware</td>\n", "      <td>home</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Jewelry Accessories &amp; Derivatives</td>\n", "      <td>fashion</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Tools &amp; Hardware</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Automotive &amp; Motorcycle</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Textiles &amp; Soft Furnishings</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Collectibles</td>\n", "      <td>collectibles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Home Improvement</td>\n", "      <td>home</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Furniture</td>\n", "      <td>home</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Kids' Fashion</td>\n", "      <td>fashion</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Baby &amp; Maternity</td>\n", "      <td>home</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Books, Magazines &amp; Audio</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>Pre-Owned</td>\n", "      <td>multi-category</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>Food &amp; Beverages</td>\n", "      <td>consumables</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>Health</td>\n", "      <td>health</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>Office Equipment and Supplies</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>Outdoors</td>\n", "      <td>home</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>Commercial Appliances</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>Mobile Phones</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>Large Appliances</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>Virtual Products</td>\n", "      <td>electronics, office &amp; books</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>Default</td>\n", "      <td>multi-category</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             category                     industry\n", "0              Womenswear & Underwear                      fashion\n", "1                Menswear & Underwear                      fashion\n", "2                Household Appliances                         home\n", "3                    Sports & Outdoor                       health\n", "4                Phones & Electronics  electronics, office & books\n", "5                      Muslim Fashion                      fashion\n", "6        Computers & Office Equipment  electronics, office & books\n", "7                 Fashion Accessories                      fashion\n", "8                      Luggage & Bags                      fashion\n", "9                       Home Supplies                         home\n", "10             Beauty & Personal Care                       beauty\n", "11                       Pet Supplies                         home\n", "12                              Shoes                      fashion\n", "13                     Toys & Hobbies                 collectibles\n", "14                        Kitchenware                         home\n", "15  Jewelry Accessories & Derivatives                      fashion\n", "16                   Tools & Hardware  electronics, office & books\n", "17            Automotive & Motorcycle  electronics, office & books\n", "18        Textiles & Soft Furnishings  electronics, office & books\n", "19                       Collectibles                 collectibles\n", "20                   Home Improvement                         home\n", "21                          Furniture                         home\n", "22                      Kids' Fashion                      fashion\n", "23                   <PERSON> & Maternity                         home\n", "24           Books, Magazines & Audio  electronics, office & books\n", "25                          Pre-Owned               multi-category\n", "26                   Food & Beverages                  consumables\n", "27                             Health                       health\n", "28      Office Equipment and Supplies  electronics, office & books\n", "29                           Outdoors                         home\n", "30              Commercial Appliances  electronics, office & books\n", "31                      Mobile Phones  electronics, office & books\n", "32                   Large Appliances  electronics, office & books\n", "33                   Virtual Products  electronics, office & books\n", "34                            Default               multi-category"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["df_category"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>num_vids_analysed</th>\n", "      <th>script_type</th>\n", "      <th>industry</th>\n", "      <th>seller_type</th>\n", "      <th>live_gmv_percentile</th>\n", "      <th>Strategy</th>\n", "      <th>Explanation</th>\n", "      <th>Count</th>\n", "      <th>Examples</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Personal Testimony</td>\n", "      <td>Sharing personal experiences to build trust an...</td>\n", "      <td>10</td>\n", "      <td>\"i have the black one and i love it\", \"i didn'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Demonstrating Product Usage</td>\n", "      <td>Showing how the product works in real-life sce...</td>\n", "      <td>9</td>\n", "      <td>\"i'll put it on and show you what it looks lik...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Emphasizing Versatility</td>\n", "      <td>Highlighting how the product can be used in va...</td>\n", "      <td>8</td>\n", "      <td>\"ideal for a carry on or for just a shorter tr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>Personal Testimony</td>\n", "      <td>Sharing personal experiences to build trust an...</td>\n", "      <td>15</td>\n", "      <td>\"my husband uses it every day on his lunch bre...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>Highlighting Product Features</td>\n", "      <td>Emphasizing specific features of the product t...</td>\n", "      <td>10</td>\n", "      <td>\"super quiet\", \"it has round retro keys\", \"thr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>562</th>\n", "      <td>39</td>\n", "      <td>closing</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G3</td>\n", "      <td>Personal Connection</td>\n", "      <td>Using personal touches, such as addressing vie...</td>\n", "      <td>8</td>\n", "      <td>\"my mom does them\", \"my mom does flowers\", \"my...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>563</th>\n", "      <td>46</td>\n", "      <td>closing</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>Expressing Gratitude</td>\n", "      <td>Thanking the audience for their participation ...</td>\n", "      <td>15</td>\n", "      <td>\"thank you all so much for coming,\" \"thank you...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>564</th>\n", "      <td>46</td>\n", "      <td>closing</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>Creating Urgency and Scarcity</td>\n", "      <td>Creating a sense of urgency and scarcity to pr...</td>\n", "      <td>12</td>\n", "      <td>\"when the time ended the price come back to or...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>565</th>\n", "      <td>46</td>\n", "      <td>closing</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>Personal Connection</td>\n", "      <td>Using personal language and expressions of lov...</td>\n", "      <td>10</td>\n", "      <td>\"we love you guys,\" \"i love you all,\" \"love yo...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>566</th>\n", "      <td>46</td>\n", "      <td>closing</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>Call to Action</td>\n", "      <td>Encouraging viewers to take specific actions, ...</td>\n", "      <td>10</td>\n", "      <td>\"don't forget. follow me,\" \"catch your time to...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>567 rows × 9 columns</p>\n", "</div>"], "text/plain": ["     num_vids_analysed script_type        industry seller_type  \\\n", "0                   50         NaN          beauty   affiliate   \n", "1                   50         NaN          beauty   affiliate   \n", "2                   50         NaN          beauty   affiliate   \n", "3                   50         NaN          beauty   affiliate   \n", "4                   50         NaN          beauty   affiliate   \n", "..                 ...         ...             ...         ...   \n", "562                 39     closing  multi-category      seller   \n", "563                 46     closing  multi-category      seller   \n", "564                 46     closing  multi-category      seller   \n", "565                 46     closing  multi-category      seller   \n", "566                 46     closing  multi-category      seller   \n", "\n", "    live_gmv_percentile                       Strategy  \\\n", "0                    G1             Personal Testimony   \n", "1                    G1    Demonstrating Product Usage   \n", "2                    G1        Emphasizing Versatility   \n", "3                    G3             Personal Testimony   \n", "4                    G3  Highlighting Product Features   \n", "..                  ...                            ...   \n", "562                  G3            Personal Connection   \n", "563                  G5           Expressing Gratitude   \n", "564                  G5  Creating Urgency and Scarcity   \n", "565                  G5            Personal Connection   \n", "566                  G5                 Call to Action   \n", "\n", "                                           Explanation  Count  \\\n", "0    Sharing personal experiences to build trust an...     10   \n", "1    Showing how the product works in real-life sce...      9   \n", "2    Highlighting how the product can be used in va...      8   \n", "3    Sharing personal experiences to build trust an...     15   \n", "4    Emphasizing specific features of the product t...     10   \n", "..                                                 ...    ...   \n", "562  Using personal touches, such as addressing vie...      8   \n", "563  Thanking the audience for their participation ...     15   \n", "564  Creating a sense of urgency and scarcity to pr...     12   \n", "565  Using personal language and expressions of lov...     10   \n", "566  Encouraging viewers to take specific actions, ...     10   \n", "\n", "                                              Examples  \n", "0    \"i have the black one and i love it\", \"i didn'...  \n", "1    \"i'll put it on and show you what it looks lik...  \n", "2    \"ideal for a carry on or for just a shorter tr...  \n", "3    \"my husband uses it every day on his lunch bre...  \n", "4    \"super quiet\", \"it has round retro keys\", \"thr...  \n", "..                                                 ...  \n", "562  \"my mom does them\", \"my mom does flowers\", \"my...  \n", "563  \"thank you all so much for coming,\" \"thank you...  \n", "564  \"when the time ended the price come back to or...  \n", "565  \"we love you guys,\" \"i love you all,\" \"love yo...  \n", "566  \"don't forget. follow me,\" \"catch your time to...  \n", "\n", "[567 rows x 9 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# df['industry'] = df['category'].map(df_category.set_index('category')['industry'])\n", "# 重命名列\n", "df.rename(columns={'category': 'industry'}, inplace=True)\n", "home_rows = df[df['industry'] == 'home'].copy()\n", "home_rows['industry'] = 'multi-category'\n", "df = pd.concat([df, home_rows], ignore_index=True)\n", "display(df)"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>operation_country</th>\n", "      <th>seller_type</th>\n", "      <th>gmv_percentile_level</th>\n", "      <th>main_first_category_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TH</td>\n", "      <td>seller</td>\n", "      <td>G4</td>\n", "      <td>Textiles &amp; Soft Furnishings</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>TH</td>\n", "      <td>seller</td>\n", "      <td>G4</td>\n", "      <td>Tools &amp; Hardware</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>TH</td>\n", "      <td>seller</td>\n", "      <td>G4</td>\n", "      <td>mixed</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TH</td>\n", "      <td>seller</td>\n", "      <td>G4</td>\n", "      <td>Household Appliances</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>TH</td>\n", "      <td>seller</td>\n", "      <td>G4</td>\n", "      <td>Home Improvement</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  operation_country seller_type gmv_percentile_level  \\\n", "0                TH      seller                   G4   \n", "1                TH      seller                   G4   \n", "2                TH      seller                   G4   \n", "3                TH      seller                   G4   \n", "4                TH      seller                   G4   \n", "\n", "      main_first_category_name  \n", "0  Textiles & Soft Furnishings  \n", "1             Tools & Hardware  \n", "2                        mixed  \n", "3         Household Appliances  \n", "4             Home Improvement  "]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["new_df = pd.read_csv('./data/df_group_sea.csv')\n", "new_df['is_seller'] = new_df['is_seller'].map({1: 'seller', 0: 'affiliate'})\n", "new_df.rename(columns={'is_seller': 'seller_type'}, inplace=True)\n", "new_df.head()"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["seller       1104\n", "affiliate    1080\n", "Name: seller_type, dtype: int64\n", "G4    364\n", "G0    364\n", "G3    364\n", "G2    364\n", "G1    364\n", "G5    364\n", "Name: gmv_percentile_level, dtype: int64\n", "Textiles & Soft Furnishings          72\n", "Pet Supplies                         72\n", "Kitchenware                          72\n", "Phones & Electronics                 72\n", "Muslim Fashion                       72\n", "Books, Magazines & Audio             72\n", "Womenswear & Underwear               72\n", "Food & Beverages                     72\n", "no main category                     72\n", "Automotive & Motorcycle              72\n", "Luggage & Bags                       72\n", "Home Supplies                        72\n", "Computers & Office Equipment         72\n", "Health                               72\n", "Sports & Outdoor                     72\n", "Menswear & Underwear                 72\n", "Beauty & Personal Care               72\n", "mixed                                72\n", "Household Appliances                 72\n", "Home Improvement                     72\n", "Toys & Hobbies                       72\n", "Baby & Maternity                     72\n", "Fashion Accessories                  72\n", "Jewelry Accessories & Derivatives    72\n", "Collectibles                         72\n", "Shoes                                72\n", "Tools & Hardware                     66\n", "Kids' Fashion                        66\n", "Furniture                            66\n", "Pre-Owned                            36\n", "Virtual Products                     12\n", "Bookings & Vouchers                   6\n", "Name: main_first_category_name, dtype: int64\n", "MY    372\n", "PH    372\n", "TH    360\n", "SG    360\n", "ID    360\n", "VN    360\n", "Name: operation_country, dtype: int64\n"]}], "source": ["print(new_df['seller_type'].value_counts())\n", "print(new_df['gmv_percentile_level'].value_counts())\n", "print(new_df['main_first_category_name'].value_counts())\n", "print(new_df['operation_country'].value_counts())\n", "\n"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>num_vids_analysed</th>\n", "      <th>script_type</th>\n", "      <th>industry</th>\n", "      <th>seller_type</th>\n", "      <th>live_gmv_percentile</th>\n", "      <th>Strategy</th>\n", "      <th>Explanation</th>\n", "      <th>Count</th>\n", "      <th>Examples</th>\n", "      <th>country_code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Personal Testimony</td>\n", "      <td>Sharing personal experiences to build trust an...</td>\n", "      <td>10</td>\n", "      <td>\"i have the black one and i love it\", \"i didn'...</td>\n", "      <td>MY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Demonstrating Product Usage</td>\n", "      <td>Showing how the product works in real-life sce...</td>\n", "      <td>9</td>\n", "      <td>\"i'll put it on and show you what it looks lik...</td>\n", "      <td>MY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>Emphasizing Versatility</td>\n", "      <td>Highlighting how the product can be used in va...</td>\n", "      <td>8</td>\n", "      <td>\"ideal for a carry on or for just a shorter tr...</td>\n", "      <td>MY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>Personal Testimony</td>\n", "      <td>Sharing personal experiences to build trust an...</td>\n", "      <td>15</td>\n", "      <td>\"my husband uses it every day on his lunch bre...</td>\n", "      <td>MY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>50</td>\n", "      <td>NaN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>Highlighting Product Features</td>\n", "      <td>Emphasizing specific features of the product t...</td>\n", "      <td>10</td>\n", "      <td>\"super quiet\", \"it has round retro keys\", \"thr...</td>\n", "      <td>MY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3397</th>\n", "      <td>39</td>\n", "      <td>closing</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G3</td>\n", "      <td>Personal Connection</td>\n", "      <td>Using personal touches, such as addressing vie...</td>\n", "      <td>8</td>\n", "      <td>\"my mom does them\", \"my mom does flowers\", \"my...</td>\n", "      <td>VN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3398</th>\n", "      <td>46</td>\n", "      <td>closing</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>Expressing Gratitude</td>\n", "      <td>Thanking the audience for their participation ...</td>\n", "      <td>15</td>\n", "      <td>\"thank you all so much for coming,\" \"thank you...</td>\n", "      <td>VN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3399</th>\n", "      <td>46</td>\n", "      <td>closing</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>Creating Urgency and Scarcity</td>\n", "      <td>Creating a sense of urgency and scarcity to pr...</td>\n", "      <td>12</td>\n", "      <td>\"when the time ended the price come back to or...</td>\n", "      <td>VN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3400</th>\n", "      <td>46</td>\n", "      <td>closing</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>Personal Connection</td>\n", "      <td>Using personal language and expressions of lov...</td>\n", "      <td>10</td>\n", "      <td>\"we love you guys,\" \"i love you all,\" \"love yo...</td>\n", "      <td>VN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3401</th>\n", "      <td>46</td>\n", "      <td>closing</td>\n", "      <td>multi-category</td>\n", "      <td>seller</td>\n", "      <td>G5</td>\n", "      <td>Call to Action</td>\n", "      <td>Encouraging viewers to take specific actions, ...</td>\n", "      <td>10</td>\n", "      <td>\"don't forget. follow me,\" \"catch your time to...</td>\n", "      <td>VN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3402 rows × 10 columns</p>\n", "</div>"], "text/plain": ["      num_vids_analysed script_type        industry seller_type  \\\n", "0                    50         NaN          beauty   affiliate   \n", "1                    50         NaN          beauty   affiliate   \n", "2                    50         NaN          beauty   affiliate   \n", "3                    50         NaN          beauty   affiliate   \n", "4                    50         NaN          beauty   affiliate   \n", "...                 ...         ...             ...         ...   \n", "3397                 39     closing  multi-category      seller   \n", "3398                 46     closing  multi-category      seller   \n", "3399                 46     closing  multi-category      seller   \n", "3400                 46     closing  multi-category      seller   \n", "3401                 46     closing  multi-category      seller   \n", "\n", "     live_gmv_percentile                       Strategy  \\\n", "0                     G1             Personal Testimony   \n", "1                     G1    Demonstrating Product Usage   \n", "2                     G1        Emphasizing Versatility   \n", "3                     G3             Personal Testimony   \n", "4                     G3  Highlighting Product Features   \n", "...                  ...                            ...   \n", "3397                  G3            Personal Connection   \n", "3398                  G5           Expressing Gratitude   \n", "3399                  G5  Creating Urgency and Scarcity   \n", "3400                  G5            Personal Connection   \n", "3401                  G5                 Call to Action   \n", "\n", "                                            Explanation  Count  \\\n", "0     Sharing personal experiences to build trust an...     10   \n", "1     Showing how the product works in real-life sce...      9   \n", "2     Highlighting how the product can be used in va...      8   \n", "3     Sharing personal experiences to build trust an...     15   \n", "4     Emphasizing specific features of the product t...     10   \n", "...                                                 ...    ...   \n", "3397  Using personal touches, such as addressing vie...      8   \n", "3398  Thanking the audience for their participation ...     15   \n", "3399  Creating a sense of urgency and scarcity to pr...     12   \n", "3400  Using personal language and expressions of lov...     10   \n", "3401  Encouraging viewers to take specific actions, ...     10   \n", "\n", "                                               Examples country_code  \n", "0     \"i have the black one and i love it\", \"i didn'...           MY  \n", "1     \"i'll put it on and show you what it looks lik...           MY  \n", "2     \"ideal for a carry on or for just a shorter tr...           MY  \n", "3     \"my husband uses it every day on his lunch bre...           MY  \n", "4     \"super quiet\", \"it has round retro keys\", \"thr...           MY  \n", "...                                                 ...          ...  \n", "3397  \"my mom does them\", \"my mom does flowers\", \"my...           VN  \n", "3398  \"thank you all so much for coming,\" \"thank you...           VN  \n", "3399  \"when the time ended the price come back to or...           VN  \n", "3400  \"we love you guys,\" \"i love you all,\" \"love yo...           VN  \n", "3401  \"don't forget. follow me,\" \"catch your time to...           VN  \n", "\n", "[3402 rows x 10 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 假设 df 已经定义并包含所需的列\n", "\n", "# 定义国家及其对应的行数\n", "country_codes = ['MY', 'PH', 'TH', 'SG', 'ID', 'VN']\n", "\n", "# 创建一个空的列表来存储每个国家的 DataFrame\n", "dfs = []\n", "\n", "# 遍历每个国家及其对应的行数\n", "for country in country_codes:\n", "    # 复制 df 并添加国家代码列\n", "    country_df = df.copy()\n", "    country_df['country_code'] = country\n", "    dfs.append(country_df)\n", "\n", "\n", "# 合并所有国家的 DataFrame\n", "final_df = pd.concat(dfs, ignore_index=True)\n", "\n", "# 输出结果\n", "display(final_df)"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing groups:   0%|          | 0/96 [01:25<?, ?it/s]\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[54], line 32\u001b[0m\n\u001b[1;32m     30\u001b[0m     script, _, _, _ \u001b[38;5;241m=\u001b[39m agent\u001b[38;5;241m.\u001b[39mgenerate_opening_script(industry, seller_type, gmv_percentile_level, strategy_combination, country_code)\n\u001b[1;32m     31\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m script_type \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mclosing\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[0;32m---> 32\u001b[0m     script, _, _, _ \u001b[38;5;241m=\u001b[39m agent\u001b[38;5;241m.\u001b[39mgenerate_closing_script(industry, seller_type, gmv_percentile_level, strategy_combination, country_code)\n\u001b[1;32m     34\u001b[0m strategies_list\u001b[38;5;241m.\u001b[39mappend([s[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mname\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m strategy_combination])\n\u001b[1;32m     35\u001b[0m scripts_list\u001b[38;5;241m.\u001b[39mappend(script[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mscripts\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "Cell \u001b[0;32mIn[42], line 206\u001b[0m, in \u001b[0;36mOpenAIAgent.generate_closing_script\u001b[0;34m(self, industry, attribute, gmv_percentile, strategies, country_code)\u001b[0m\n\u001b[1;32m    162\u001b[0m assistant_message \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m    163\u001b[0m \u001b[38;5;124mPlease generate closing scripts based on the following information:\u001b[39m\n\u001b[1;32m    164\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    185\u001b[0m \u001b[38;5;124m\u001b[39m\u001b[38;5;130;01m}}\u001b[39;00m\n\u001b[1;32m    186\u001b[0m \u001b[38;5;124m\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m    188\u001b[0m user_message \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m    189\u001b[0m \u001b[38;5;124mIndustry: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mindustry\u001b[38;5;132;01m}\u001b[39;00m\n\u001b[1;32m    190\u001b[0m \u001b[38;5;124mAttribute: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mattribute\u001b[38;5;132;01m}\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    203\u001b[0m \u001b[38;5;124m\u001b[39m\u001b[38;5;130;01m}}\u001b[39;00m\n\u001b[1;32m    204\u001b[0m \u001b[38;5;124m\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[0;32m--> 206\u001b[0m result, total_tokens, prompt_tokens, completion_tokens \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrun_openai_task(system_message, assistant_message, user_message)\n\u001b[1;32m    207\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m result, total_tokens, prompt_tokens, completion_tokens\n", "Cell \u001b[0;32mIn[42], line 60\u001b[0m, in \u001b[0;36mOpenAIAgent.run_openai_task\u001b[0;34m(self, system_message, assistant_message, user_message)\u001b[0m\n\u001b[1;32m     53\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mrun_openai_task\u001b[39m(\u001b[38;5;28mself\u001b[39m, system_message, assistant_message, user_message):\n\u001b[1;32m     54\u001b[0m     messages \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     55\u001b[0m         {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>e\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msystem\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msystem_message(system_message)},\n\u001b[1;32m     56\u001b[0m         {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124massistant\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39massistant_message(assistant_message)},\n\u001b[1;32m     57\u001b[0m         {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrole\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39muser_message(user_message)}\n\u001b[1;32m     58\u001b[0m     ]\n\u001b[0;32m---> 60\u001b[0m     completion \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mclient\u001b[38;5;241m.\u001b[39mchat\u001b[38;5;241m.\u001b[39mcompletions\u001b[38;5;241m.\u001b[39mcreate(\n\u001b[1;32m     61\u001b[0m         extra_headers\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mX-TT-LOGID\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mabc\u001b[39m\u001b[38;5;124m\"\u001b[39m},  \u001b[38;5;66;03m# 请务必带上此header，方便定位问题\u001b[39;00m\n\u001b[1;32m     62\u001b[0m         model\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel,\n\u001b[1;32m     63\u001b[0m         messages\u001b[38;5;241m=\u001b[39mmessages,\n\u001b[1;32m     64\u001b[0m         temperature\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m,\n\u001b[1;32m     65\u001b[0m         frequency_penalty\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m,\n\u001b[1;32m     66\u001b[0m         presence_penalty\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m\n\u001b[1;32m     67\u001b[0m     )\n\u001b[1;32m     69\u001b[0m     json_data \u001b[38;5;241m=\u001b[39m completion\u001b[38;5;241m.\u001b[39mmodel_dump_json()\n\u001b[1;32m     70\u001b[0m     data_dict \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mloads(json_data)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_utils/_utils.py:275\u001b[0m, in \u001b[0;36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    273\u001b[0m             msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[38;5;241m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    274\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[0;32m--> 275\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m func(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/resources/chat/completions.py:829\u001b[0m, in \u001b[0;36mCompletions.create\u001b[0;34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, extra_headers, extra_query, extra_body, timeout)\u001b[0m\n\u001b[1;32m    788\u001b[0m \u001b[38;5;129m@required_args\u001b[39m([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m], [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[1;32m    789\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcreate\u001b[39m(\n\u001b[1;32m    790\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    826\u001b[0m     timeout: \u001b[38;5;28mfloat\u001b[39m \u001b[38;5;241m|\u001b[39m httpx\u001b[38;5;241m.\u001b[39mTimeout \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m|\u001b[39m NotGiven \u001b[38;5;241m=\u001b[39m NOT_GIVEN,\n\u001b[1;32m    827\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ChatCompletion \u001b[38;5;241m|\u001b[39m Stream[ChatCompletionChunk]:\n\u001b[1;32m    828\u001b[0m     validate_response_format(response_format)\n\u001b[0;32m--> 829\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_post(\n\u001b[1;32m    830\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/chat/completions\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    831\u001b[0m         body\u001b[38;5;241m=\u001b[39mmaybe_transform(\n\u001b[1;32m    832\u001b[0m             {\n\u001b[1;32m    833\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: messages,\n\u001b[1;32m    834\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m: model,\n\u001b[1;32m    835\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maudio\u001b[39m\u001b[38;5;124m\"\u001b[39m: audio,\n\u001b[1;32m    836\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfrequency_penalty\u001b[39m\u001b[38;5;124m\"\u001b[39m: frequency_penalty,\n\u001b[1;32m    837\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfunction_call\u001b[39m\u001b[38;5;124m\"\u001b[39m: function_call,\n\u001b[1;32m    838\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfunctions\u001b[39m\u001b[38;5;124m\"\u001b[39m: functions,\n\u001b[1;32m    839\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlogit_bias\u001b[39m\u001b[38;5;124m\"\u001b[39m: logit_bias,\n\u001b[1;32m    840\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlogprobs\u001b[39m\u001b[38;5;124m\"\u001b[39m: logprobs,\n\u001b[1;32m    841\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmax_completion_tokens\u001b[39m\u001b[38;5;124m\"\u001b[39m: max_completion_tokens,\n\u001b[1;32m    842\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmax_tokens\u001b[39m\u001b[38;5;124m\"\u001b[39m: max_tokens,\n\u001b[1;32m    843\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmetadata\u001b[39m\u001b[38;5;124m\"\u001b[39m: metadata,\n\u001b[1;32m    844\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodalities\u001b[39m\u001b[38;5;124m\"\u001b[39m: modalities,\n\u001b[1;32m    845\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mn\u001b[39m\u001b[38;5;124m\"\u001b[39m: n,\n\u001b[1;32m    846\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mparallel_tool_calls\u001b[39m\u001b[38;5;124m\"\u001b[39m: parallel_tool_calls,\n\u001b[1;32m    847\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprediction\u001b[39m\u001b[38;5;124m\"\u001b[39m: prediction,\n\u001b[1;32m    848\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpresence_penalty\u001b[39m\u001b[38;5;124m\"\u001b[39m: presence_penalty,\n\u001b[1;32m    849\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse_format\u001b[39m\u001b[38;5;124m\"\u001b[39m: response_format,\n\u001b[1;32m    850\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mseed\u001b[39m\u001b[38;5;124m\"\u001b[39m: seed,\n\u001b[1;32m    851\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mservice_tier\u001b[39m\u001b[38;5;124m\"\u001b[39m: service_tier,\n\u001b[1;32m    852\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstop\u001b[39m\u001b[38;5;124m\"\u001b[39m: stop,\n\u001b[1;32m    853\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstore\u001b[39m\u001b[38;5;124m\"\u001b[39m: store,\n\u001b[1;32m    854\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m: stream,\n\u001b[1;32m    855\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream_options\u001b[39m\u001b[38;5;124m\"\u001b[39m: stream_options,\n\u001b[1;32m    856\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtemperature\u001b[39m\u001b[38;5;124m\"\u001b[39m: temperature,\n\u001b[1;32m    857\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtool_choice\u001b[39m\u001b[38;5;124m\"\u001b[39m: tool_choice,\n\u001b[1;32m    858\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtools\u001b[39m\u001b[38;5;124m\"\u001b[39m: tools,\n\u001b[1;32m    859\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtop_logprobs\u001b[39m\u001b[38;5;124m\"\u001b[39m: top_logprobs,\n\u001b[1;32m    860\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtop_p\u001b[39m\u001b[38;5;124m\"\u001b[39m: top_p,\n\u001b[1;32m    861\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m: user,\n\u001b[1;32m    862\u001b[0m             },\n\u001b[1;32m    863\u001b[0m             completion_create_params\u001b[38;5;241m.\u001b[39mCompletionCreateParams,\n\u001b[1;32m    864\u001b[0m         ),\n\u001b[1;32m    865\u001b[0m         options\u001b[38;5;241m=\u001b[39mmake_request_options(\n\u001b[1;32m    866\u001b[0m             extra_headers\u001b[38;5;241m=\u001b[39mextra_headers, extra_query\u001b[38;5;241m=\u001b[39mextra_query, extra_body\u001b[38;5;241m=\u001b[39mextra_body, timeout\u001b[38;5;241m=\u001b[39mtimeout\n\u001b[1;32m    867\u001b[0m         ),\n\u001b[1;32m    868\u001b[0m         cast_to\u001b[38;5;241m=\u001b[39mChatCompletion,\n\u001b[1;32m    869\u001b[0m         stream\u001b[38;5;241m=\u001b[39mstream \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m    870\u001b[0m         stream_cls\u001b[38;5;241m=\u001b[39mStream[ChatCompletionChunk],\n\u001b[1;32m    871\u001b[0m     )\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_base_client.py:1280\u001b[0m, in \u001b[0;36mSyncAPIClient.post\u001b[0;34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[0m\n\u001b[1;32m   1266\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\n\u001b[1;32m   1267\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1268\u001b[0m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1275\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1276\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _StreamT:\n\u001b[1;32m   1277\u001b[0m     opts \u001b[38;5;241m=\u001b[39m FinalRequestOptions\u001b[38;5;241m.\u001b[39mconstruct(\n\u001b[1;32m   1278\u001b[0m         method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpost\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39mpath, json_data\u001b[38;5;241m=\u001b[39mbody, files\u001b[38;5;241m=\u001b[39mto_httpx_files(files), \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions\n\u001b[1;32m   1279\u001b[0m     )\n\u001b[0;32m-> 1280\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest(cast_to, opts, stream\u001b[38;5;241m=\u001b[39mstream, stream_cls\u001b[38;5;241m=\u001b[39mstream_cls))\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_base_client.py:957\u001b[0m, in \u001b[0;36mSyncAPIClient.request\u001b[0;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[1;32m    954\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    955\u001b[0m     retries_taken \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[0;32m--> 957\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_request(\n\u001b[1;32m    958\u001b[0m     cast_to\u001b[38;5;241m=\u001b[39mcast_to,\n\u001b[1;32m    959\u001b[0m     options\u001b[38;5;241m=\u001b[39moptions,\n\u001b[1;32m    960\u001b[0m     stream\u001b[38;5;241m=\u001b[39mstream,\n\u001b[1;32m    961\u001b[0m     stream_cls\u001b[38;5;241m=\u001b[39mstream_cls,\n\u001b[1;32m    962\u001b[0m     retries_taken\u001b[38;5;241m=\u001b[39mretries_taken,\n\u001b[1;32m    963\u001b[0m )\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/openai/_base_client.py:993\u001b[0m, in \u001b[0;36mSyncAPIClient._request\u001b[0;34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[0m\n\u001b[1;32m    990\u001b[0m log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSending HTTP Request: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, request\u001b[38;5;241m.\u001b[39mmethod, request\u001b[38;5;241m.\u001b[39murl)\n\u001b[1;32m    992\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 993\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client\u001b[38;5;241m.\u001b[39msend(\n\u001b[1;32m    994\u001b[0m         request,\n\u001b[1;32m    995\u001b[0m         stream\u001b[38;5;241m=\u001b[39mstream \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_should_stream_response_body(request\u001b[38;5;241m=\u001b[39mrequest),\n\u001b[1;32m    996\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m    997\u001b[0m     )\n\u001b[1;32m    998\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m httpx\u001b[38;5;241m.\u001b[39mTimeoutException \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m    999\u001b[0m     log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEncountered httpx.TimeoutException\u001b[39m\u001b[38;5;124m\"\u001b[39m, exc_info\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpx/_client.py:914\u001b[0m, in \u001b[0;36mClient.send\u001b[0;34m(self, request, stream, auth, follow_redirects)\u001b[0m\n\u001b[1;32m    906\u001b[0m follow_redirects \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    907\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mfollow_redirects\n\u001b[1;32m    908\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(follow_redirects, UseClientDefault)\n\u001b[1;32m    909\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m follow_redirects\n\u001b[1;32m    910\u001b[0m )\n\u001b[1;32m    912\u001b[0m auth \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_build_request_auth(request, auth)\n\u001b[0;32m--> 914\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_handling_auth(\n\u001b[1;32m    915\u001b[0m     request,\n\u001b[1;32m    916\u001b[0m     auth\u001b[38;5;241m=\u001b[39mauth,\n\u001b[1;32m    917\u001b[0m     follow_redirects\u001b[38;5;241m=\u001b[39mfollow_redirects,\n\u001b[1;32m    918\u001b[0m     history\u001b[38;5;241m=\u001b[39m[],\n\u001b[1;32m    919\u001b[0m )\n\u001b[1;32m    920\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    921\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m stream:\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpx/_client.py:942\u001b[0m, in \u001b[0;36mClient._send_handling_auth\u001b[0;34m(self, request, auth, follow_redirects, history)\u001b[0m\n\u001b[1;32m    939\u001b[0m request \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mnext\u001b[39m(auth_flow)\n\u001b[1;32m    941\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 942\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_handling_redirects(\n\u001b[1;32m    943\u001b[0m         request,\n\u001b[1;32m    944\u001b[0m         follow_redirects\u001b[38;5;241m=\u001b[39mfollow_redirects,\n\u001b[1;32m    945\u001b[0m         history\u001b[38;5;241m=\u001b[39mhistory,\n\u001b[1;32m    946\u001b[0m     )\n\u001b[1;32m    947\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    948\u001b[0m         \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpx/_client.py:979\u001b[0m, in \u001b[0;36mClient._send_handling_redirects\u001b[0;34m(self, request, follow_redirects, history)\u001b[0m\n\u001b[1;32m    976\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_event_hooks[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrequest\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n\u001b[1;32m    977\u001b[0m     hook(request)\n\u001b[0;32m--> 979\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_single_request(request)\n\u001b[1;32m    980\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    981\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_event_hooks[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpx/_client.py:1015\u001b[0m, in \u001b[0;36mClient._send_single_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m   1010\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[1;32m   1011\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAttempted to send an async request with a sync Client instance.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1012\u001b[0m     )\n\u001b[1;32m   1014\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request\u001b[38;5;241m=\u001b[39mrequest):\n\u001b[0;32m-> 1015\u001b[0m     response \u001b[38;5;241m=\u001b[39m transport\u001b[38;5;241m.\u001b[39mhandle_request(request)\n\u001b[1;32m   1017\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response\u001b[38;5;241m.\u001b[39mstream, SyncByteStream)\n\u001b[1;32m   1019\u001b[0m response\u001b[38;5;241m.\u001b[39mrequest \u001b[38;5;241m=\u001b[39m request\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpx/_transports/default.py:233\u001b[0m, in \u001b[0;36mHTTPTransport.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    220\u001b[0m req \u001b[38;5;241m=\u001b[39m httpcore\u001b[38;5;241m.\u001b[39mRequest(\n\u001b[1;32m    221\u001b[0m     method\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mmethod,\n\u001b[1;32m    222\u001b[0m     url\u001b[38;5;241m=\u001b[39mhttpcore\u001b[38;5;241m.\u001b[39mURL(\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    230\u001b[0m     extensions\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mextensions,\n\u001b[1;32m    231\u001b[0m )\n\u001b[1;32m    232\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[0;32m--> 233\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_pool\u001b[38;5;241m.\u001b[39mhandle_request(req)\n\u001b[1;32m    235\u001b[0m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(resp\u001b[38;5;241m.\u001b[39mstream, typing\u001b[38;5;241m.\u001b[39mIterable)\n\u001b[1;32m    237\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m Response(\n\u001b[1;32m    238\u001b[0m     status_code\u001b[38;5;241m=\u001b[39mresp\u001b[38;5;241m.\u001b[39mstatus,\n\u001b[1;32m    239\u001b[0m     headers\u001b[38;5;241m=\u001b[39mresp\u001b[38;5;241m.\u001b[39mheaders,\n\u001b[1;32m    240\u001b[0m     stream\u001b[38;5;241m=\u001b[39mResponseStream(resp\u001b[38;5;241m.\u001b[39mstream),\n\u001b[1;32m    241\u001b[0m     extensions\u001b[38;5;241m=\u001b[39mresp\u001b[38;5;241m.\u001b[39mextensions,\n\u001b[1;32m    242\u001b[0m )\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py:256\u001b[0m, in \u001b[0;36mConnectionPool.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    253\u001b[0m         closing \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_assign_requests_to_connections()\n\u001b[1;32m    255\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_close_connections(closing)\n\u001b[0;32m--> 256\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exc \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    258\u001b[0m \u001b[38;5;66;03m# Return the response. Note that in this case we still have to manage\u001b[39;00m\n\u001b[1;32m    259\u001b[0m \u001b[38;5;66;03m# the point at which the response is closed.\u001b[39;00m\n\u001b[1;32m    260\u001b[0m \u001b[38;5;28;<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response\u001b[38;5;241m.\u001b[39mstream, typing\u001b[38;5;241m.\u001b[39mIterable)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py:236\u001b[0m, in \u001b[0;36mConnectionPool.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    232\u001b[0m connection \u001b[38;5;241m=\u001b[39m pool_request\u001b[38;5;241m.\u001b[39mwait_for_connection(timeout\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[1;32m    234\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;66;03m# Send the request on the assigned connection.\u001b[39;00m\n\u001b[0;32m--> 236\u001b[0m     response \u001b[38;5;241m=\u001b[39m connection\u001b[38;5;241m.\u001b[39mhandle_request(\n\u001b[1;32m    237\u001b[0m         pool_request\u001b[38;5;241m.\u001b[39mrequest\n\u001b[1;32m    238\u001b[0m     )\n\u001b[1;32m    239\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ConnectionNotAvailable:\n\u001b[1;32m    240\u001b[0m     \u001b[38;5;66;03m# In some cases a connection may initially be available to\u001b[39;00m\n\u001b[1;32m    241\u001b[0m     \u001b[38;5;66;03m# handle a request, but then become unavailable.\u001b[39;00m\n\u001b[1;32m    242\u001b[0m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[1;32m    243\u001b[0m     \u001b[38;5;66;03m# In this case we clear the connection and try again.\u001b[39;00m\n\u001b[1;32m    244\u001b[0m     pool_request\u001b[38;5;241m.\u001b[39mclear_connection()\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/connection.py:103\u001b[0m, in \u001b[0;36mHTTPConnection.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    100\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connect_failed \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m    101\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exc\n\u001b[0;32m--> 103\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connection\u001b[38;5;241m.\u001b[39mhandle_request(request)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/http11.py:136\u001b[0m, in \u001b[0;36mHTTP11Connection.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    134\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m Trace(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse_closed\u001b[39m\u001b[38;5;124m\"\u001b[39m, logger, request) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[1;32m    135\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_response_closed()\n\u001b[0;32m--> 136\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m exc\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/http11.py:106\u001b[0m, in \u001b[0;36mHTTP11Connection.handle_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m     95\u001b[0m     \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[1;32m     97\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m Trace(\n\u001b[1;32m     98\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mreceive_response_headers\u001b[39m\u001b[38;5;124m\"\u001b[39m, logger, request, kwargs\n\u001b[1;32m     99\u001b[0m ) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[1;32m    100\u001b[0m     (\n\u001b[1;32m    101\u001b[0m         http_version,\n\u001b[1;32m    102\u001b[0m         status,\n\u001b[1;32m    103\u001b[0m         reason_phrase,\n\u001b[1;32m    104\u001b[0m         headers,\n\u001b[1;32m    105\u001b[0m         trailing_data,\n\u001b[0;32m--> 106\u001b[0m     ) \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_receive_response_headers(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    107\u001b[0m     trace\u001b[38;5;241m.\u001b[39mreturn_value \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    108\u001b[0m         http_version,\n\u001b[1;32m    109\u001b[0m         status,\n\u001b[1;32m    110\u001b[0m         reason_phrase,\n\u001b[1;32m    111\u001b[0m         headers,\n\u001b[1;32m    112\u001b[0m     )\n\u001b[1;32m    114\u001b[0m network_stream \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_network_stream\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/http11.py:177\u001b[0m, in \u001b[0;36mHTTP11Connection._receive_response_headers\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    174\u001b[0m timeout \u001b[38;5;241m=\u001b[39m timeouts\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mread\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m    176\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m--> 177\u001b[0m     event \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_receive_event(timeout\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[1;32m    178\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(event, h11\u001b[38;5;241m.\u001b[39mResponse):\n\u001b[1;32m    179\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_sync/http11.py:217\u001b[0m, in \u001b[0;36mHTTP11Connection._receive_event\u001b[0;34m(self, timeout)\u001b[0m\n\u001b[1;32m    214\u001b[0m     event \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_h11_state\u001b[38;5;241m.\u001b[39mnext_event()\n\u001b[1;32m    216\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m event \u001b[38;5;129;01mis\u001b[39;00m h11\u001b[38;5;241m.\u001b[39mNEED_DATA:\n\u001b[0;32m--> 217\u001b[0m     data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_network_stream\u001b[38;5;241m.\u001b[39mread(\n\u001b[1;32m    218\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mREAD_NUM_BYTES, timeout\u001b[38;5;241m=\u001b[39mtimeout\n\u001b[1;32m    219\u001b[0m     )\n\u001b[1;32m    221\u001b[0m     \u001b[38;5;66;03m# If we feed this case through h11 we'll raise an exception like:\u001b[39;00m\n\u001b[1;32m    222\u001b[0m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[1;32m    223\u001b[0m     \u001b[38;5;66;03m#     httpcore.RemoteProtocolError: can't handle event type\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    227\u001b[0m     \u001b[38;5;66;03m# perspective. Instead we handle this case distinctly and treat\u001b[39;00m\n\u001b[1;32m    228\u001b[0m     \u001b[38;5;66;03m# it as a ConnectError.\u001b[39;00m\n\u001b[1;32m    229\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m data \u001b[38;5;241m==\u001b[39m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_h11_state\u001b[38;5;241m.\u001b[39mtheir_state \u001b[38;5;241m==\u001b[39m h11\u001b[38;5;241m.\u001b[39mSEND_RESPONSE:\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/site-packages/httpcore/_backends/sync.py:128\u001b[0m, in \u001b[0;36mSyncStream.read\u001b[0;34m(self, max_bytes, timeout)\u001b[0m\n\u001b[1;32m    126\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m map_exceptions(exc_map):\n\u001b[1;32m    127\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sock\u001b[38;5;241m.\u001b[39msettimeout(timeout)\n\u001b[0;32m--> 128\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sock\u001b[38;5;241m.\u001b[39mrecv(max_bytes)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/ssl.py:1263\u001b[0m, in \u001b[0;36mSSLSocket.recv\u001b[0;34m(self, buflen, flags)\u001b[0m\n\u001b[1;32m   1259\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m flags \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m   1260\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m   1261\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnon-zero flags not allowed in calls to recv() on \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m\n\u001b[1;32m   1262\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__class__\u001b[39m)\n\u001b[0;32m-> 1263\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mread(buflen)\n\u001b[1;32m   1264\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1265\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mrecv(buflen, flags)\n", "File \u001b[0;32m/opt/homebrew/anaconda3/lib/python3.11/ssl.py:1136\u001b[0m, in \u001b[0;36mSSLSocket.read\u001b[0;34m(self, len, buffer)\u001b[0m\n\u001b[1;32m   1134\u001b[0m         \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sslobj\u001b[38;5;241m.\u001b[39mread(\u001b[38;5;28mlen\u001b[39m, buffer)\n\u001b[1;32m   1135\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1136\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sslobj\u001b[38;5;241m.\u001b[39mread(\u001b[38;5;28mlen\u001b[39m)\n\u001b[1;32m   1137\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m SSLError \u001b[38;5;28;01mas\u001b[39;00m x:\n\u001b[1;32m   1138\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m x\u001b[38;5;241m.\u001b[39margs[\u001b[38;5;241m0\u001b[39m] \u001b[38;5;241m==\u001b[39m SSL_ERROR_EOF \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msuppress_ragged_eofs:\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import pandas as pd\n", "from tqdm import tqdm\n", "import itertools\n", "\n", "\n", "# 假设 df 已经定义并包含所需的列\n", "\n", "# 存储生成的脚本\n", "script_texts = []\n", "\n", "# 获取总的分组数，用于进度条    \n", "total_groups = df.groupby(['industry', 'seller_type', 'live_gmv_percentile', 'script_type']).ngroups\n", "country_code = 'US'\n", "# 遍历每个分组，使用 tqdm 显示进度条\n", "for (industry, seller_type, gmv_percentile_level, script_type), group in tqdm(df.groupby(['industry', 'seller_type', 'live_gmv_percentile', 'script_type']), total=total_groups, desc=\"Processing groups\"):\n", "    # 创建当前分组的 strategies 子集\n", "    strategies = group[['Strategy', 'Explanation', 'Examples']].rename(columns={'Strategy': 'name', 'Explanation': 'explanation', 'Examples': 'examples'}).to_dict(orient='records')\n", "    \n", "    # 生成 1-3 个 strategy 的所有组合\n", "    strategy_combinations = []\n", "    for r in range(1, 4):\n", "        combinations = itertools.combinations(strategies, r)\n", "        strategy_combinations.extend([list(comb) for comb in combinations])\n", "\n", "    strategies_list = []\n", "    scripts_list = []\n", "    for strategy_combination in strategy_combinations:\n", "\n", "        if script_type == 'opening':\n", "            script, _, _, _ = agent.generate_opening_script(industry, seller_type, gmv_percentile_level, strategy_combination, country_code)\n", "        elif script_type == 'closing':\n", "            script, _, _, _ = agent.generate_closing_script(industry, seller_type, gmv_percentile_level, strategy_combination, country_code)\n", "\n", "        strategies_list.append([s['name'] for s in strategy_combination])\n", "        scripts_list.append(script['scripts'])\n", "\n", "    script_texts.append({\n", "        \"country_code\": country_code,\n", "        \"industry\": industry,\n", "        \"seller_type\": seller_type,\n", "        \"gmv_percentile_level\": gmv_percentile_level,\n", "        \"strategies\": strategies_list,\n", "        \"script_type\": script_type,\n", "        \"scripts\": scripts_list\n", "    })\n", "    \n", "# 创建新的 DataFrame\n", "df_scripts = pd.DataFrame(script_texts)\n", "\n", "# 输出结果\n", "display(df_scripts)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# SEA 开头结尾话术\n"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["df.to_csv('./output/df_group_sea.csv', index=False)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing groups for MY:   4%|▍         | 4/96 [03:07<1:11:54, 46.90s/it]\n", "Processing groups for PH:   4%|▍         | 4/96 [03:05<1:11:05, 46.37s/it]\n", "Processing groups for TH:   4%|▍         | 4/96 [03:59<1:31:59, 59.99s/it]\n", "Processing groups for SG:   4%|▍         | 4/96 [02:48<1:04:44, 42.23s/it]\n", "Processing groups for ID:   4%|▍         | 4/96 [02:20<53:55, 35.17s/it]\n", "Processing groups for VN:   4%|▍         | 4/96 [02:55<1:07:17, 43.89s/it]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country_code</th>\n", "      <th>industry</th>\n", "      <th>seller_type</th>\n", "      <th>gmv_percentile_level</th>\n", "      <th>strategies</th>\n", "      <th>script_type</th>\n", "      <th>scripts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>PH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>PH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>PH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>PH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>TH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>TH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>TH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>TH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>SG</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>SG</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>SG</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>SG</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>ID</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>ID</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>ID</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>ID</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>VN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>VN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>VN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>closing</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>VN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[]</td>\n", "      <td>opening</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Connection], [Call to Action], [Hum...</td>\n", "      <td>closing</td>\n", "      <td>[[<PERSON><PERSON> kasih semua kerana men<PERSON>tai live say...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Engagement], [Relatability], [Demon...</td>\n", "      <td>opening</td>\n", "      <td>[[Hai semua! Selamat datang ke live kita hari ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Clear Call to Action], [Personal Connection]...</td>\n", "      <td>closing</td>\n", "      <td>[[<PERSON><PERSON> kasih semua kerana menyertai sesi liv...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>MY</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Creating Urgency], [Personal Connection], [H...</td>\n", "      <td>opening</td>\n", "      <td>[[Hai semua! Selamat datang ke live stream kam...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>PH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Connection], [Call to Action], [Hum...</td>\n", "      <td>closing</td>\n", "      <td>[[Mga ka-beauty, maraming salamat sa pagsama s...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>PH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Engagement], [Relatability], [Demon...</td>\n", "      <td>opening</td>\n", "      <td>[[<PERSON><PERSON><PERSON><PERSON> araw, mga ka-beauty! <PERSON><PERSON>ta kayo?...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>PH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Clear Call to Action], [Personal Connection]...</td>\n", "      <td>closing</td>\n", "      <td>[[Mga ka-beauty, maraming salamat sa pagsama s...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>PH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Creating Urgency], [Personal Connection], [H...</td>\n", "      <td>opening</td>\n", "      <td>[[<PERSON><PERSON><PERSON><PERSON> araw, mga ka-beauty! Nandito na na...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>TH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Connection], [Call to Action], [Hum...</td>\n", "      <td>closing</td>\n", "      <td>[[ขอบคุณทุกคนที่มาร่วมสนุกกับไลฟ์วันนี้นะคะ! น...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>TH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Engagement], [Relatability], [Demon...</td>\n", "      <td>opening</td>\n", "      <td>[[สวัสดีค่ะเพื่อนๆ ทุกคน! วันนี้เรามีผลิตภัณฑ์...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>TH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Clear Call to Action], [Personal Connection]...</td>\n", "      <td>closing</td>\n", "      <td>[[ขอบคุณทุกคนที่เข้ามาชมไลฟ์วันนี้นะคะ ถ้าคุณส...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>TH</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Creating Urgency], [Personal Connection], [H...</td>\n", "      <td>opening</td>\n", "      <td>[[สวัสดีค่ะทุกคน! วันนี้เรามีโปรโมชั่นพิเศษสำห...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>SG</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Connection], [Call to Action], [Hum...</td>\n", "      <td>closing</td>\n", "      <td>[[Thank you all so much for joining me today! ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>SG</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Engagement], [Relatability], [Demon...</td>\n", "      <td>opening</td>\n", "      <td>[[Hey beautiful people! It's so great to see y...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>SG</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Clear Call to Action], [Personal Connection]...</td>\n", "      <td>closing</td>\n", "      <td>[[Thank you all for joining our beauty live st...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>SG</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Creating Urgency], [Personal Connection], [H...</td>\n", "      <td>opening</td>\n", "      <td>[[Hey beauty lovers! Welcome to our exclusive ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>ID</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Connection], [Call to Action], [Hum...</td>\n", "      <td>closing</td>\n", "      <td>[[<PERSON><PERSON> kasih banyak sudah bergabung di live ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>ID</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Engagement], [Relatability], [Demon...</td>\n", "      <td>opening</td>\n", "      <td>[[Halo teman-teman cantik! Selamat datang di l...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>ID</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Clear Call to Action], [Personal Connection]...</td>\n", "      <td>closing</td>\n", "      <td>[[<PERSON><PERSON> kasih sudah bergabung di live kita ha...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>ID</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Creating Urgency], [Personal Connection], [H...</td>\n", "      <td>opening</td>\n", "      <td>[[Halo semuanya! Selamat datang di live kita h...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>VN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Connection], [Call to Action], [Hum...</td>\n", "      <td>closing</td>\n", "      <td>[[<PERSON><PERSON><PERSON> ơn mọi người đã tham gia buổi livestream...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>VN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G1</td>\n", "      <td>[[Personal Engagement], [Relatability], [Demon...</td>\n", "      <td>opening</td>\n", "      <td>[[Xin chào các bạn yêu thích làm đẹp! Mình là ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>VN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Clear Call to Action], [Personal Connection]...</td>\n", "      <td>closing</td>\n", "      <td>[[<PERSON><PERSON><PERSON> ơn các bạn đã tham gia buổi livestream h...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>VN</td>\n", "      <td>beauty</td>\n", "      <td>affiliate</td>\n", "      <td>G3</td>\n", "      <td>[[Creating Urgency], [Personal Connection], [H...</td>\n", "      <td>opening</td>\n", "      <td>[[Xin chào mọi người! Hôm nay chúng ta có một ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   country_code industry seller_type gmv_percentile_level  \\\n", "0            MY   beauty   affiliate                   G1   \n", "1            MY   beauty   affiliate                   G1   \n", "2            MY   beauty   affiliate                   G3   \n", "3            MY   beauty   affiliate                   G3   \n", "4            PH   beauty   affiliate                   G1   \n", "5            PH   beauty   affiliate                   G1   \n", "6            PH   beauty   affiliate                   G3   \n", "7            PH   beauty   affiliate                   G3   \n", "8            TH   beauty   affiliate                   G1   \n", "9            TH   beauty   affiliate                   G1   \n", "10           TH   beauty   affiliate                   G3   \n", "11           TH   beauty   affiliate                   G3   \n", "12           SG   beauty   affiliate                   G1   \n", "13           SG   beauty   affiliate                   G1   \n", "14           SG   beauty   affiliate                   G3   \n", "15           SG   beauty   affiliate                   G3   \n", "16           ID   beauty   affiliate                   G1   \n", "17           ID   beauty   affiliate                   G1   \n", "18           ID   beauty   affiliate                   G3   \n", "19           ID   beauty   affiliate                   G3   \n", "20           VN   beauty   affiliate                   G1   \n", "21           VN   beauty   affiliate                   G1   \n", "22           VN   beauty   affiliate                   G3   \n", "23           VN   beauty   affiliate                   G3   \n", "24           MY   beauty   affiliate                   G1   \n", "25           MY   beauty   affiliate                   G1   \n", "26           MY   beauty   affiliate                   G3   \n", "27           MY   beauty   affiliate                   G3   \n", "28           PH   beauty   affiliate                   G1   \n", "29           PH   beauty   affiliate                   G1   \n", "30           PH   beauty   affiliate                   G3   \n", "31           PH   beauty   affiliate                   G3   \n", "32           TH   beauty   affiliate                   G1   \n", "33           TH   beauty   affiliate                   G1   \n", "34           TH   beauty   affiliate                   G3   \n", "35           TH   beauty   affiliate                   G3   \n", "36           SG   beauty   affiliate                   G1   \n", "37           SG   beauty   affiliate                   G1   \n", "38           SG   beauty   affiliate                   G3   \n", "39           SG   beauty   affiliate                   G3   \n", "40           ID   beauty   affiliate                   G1   \n", "41           ID   beauty   affiliate                   G1   \n", "42           ID   beauty   affiliate                   G3   \n", "43           ID   beauty   affiliate                   G3   \n", "44           VN   beauty   affiliate                   G1   \n", "45           VN   beauty   affiliate                   G1   \n", "46           VN   beauty   affiliate                   G3   \n", "47           VN   beauty   affiliate                   G3   \n", "\n", "                                           strategies script_type  \\\n", "0                                                  []     closing   \n", "1                                                  []     opening   \n", "2                                                  []     closing   \n", "3                                                  []     opening   \n", "4                                                  []     closing   \n", "5                                                  []     opening   \n", "6                                                  []     closing   \n", "7                                                  []     opening   \n", "8                                                  []     closing   \n", "9                                                  []     opening   \n", "10                                                 []     closing   \n", "11                                                 []     opening   \n", "12                                                 []     closing   \n", "13                                                 []     opening   \n", "14                                                 []     closing   \n", "15                                                 []     opening   \n", "16                                                 []     closing   \n", "17                                                 []     opening   \n", "18                                                 []     closing   \n", "19                                                 []     opening   \n", "20                                                 []     closing   \n", "21                                                 []     opening   \n", "22                                                 []     closing   \n", "23                                                 []     opening   \n", "24  [[Personal Connection], [Call to Action], [Hum...     closing   \n", "25  [[Personal Engagement], [Relatability], [Demon...     opening   \n", "26  [[Clear Call to Action], [Personal Connection]...     closing   \n", "27  [[Creating Urgency], [Personal Connection], [H...     opening   \n", "28  [[Personal Connection], [Call to Action], [Hum...     closing   \n", "29  [[Personal Engagement], [Relatability], [Demon...     opening   \n", "30  [[Clear Call to Action], [Personal Connection]...     closing   \n", "31  [[Creating Urgency], [Personal Connection], [H...     opening   \n", "32  [[Personal Connection], [Call to Action], [Hum...     closing   \n", "33  [[Personal Engagement], [Relatability], [Demon...     opening   \n", "34  [[Clear Call to Action], [Personal Connection]...     closing   \n", "35  [[Creating Urgency], [Personal Connection], [H...     opening   \n", "36  [[Personal Connection], [Call to Action], [Hum...     closing   \n", "37  [[Personal Engagement], [Relatability], [Demon...     opening   \n", "38  [[Clear Call to Action], [Personal Connection]...     closing   \n", "39  [[Creating Urgency], [Personal Connection], [H...     opening   \n", "40  [[Personal Connection], [Call to Action], [Hum...     closing   \n", "41  [[Personal Engagement], [Relatability], [Demon...     opening   \n", "42  [[Clear Call to Action], [Personal Connection]...     closing   \n", "43  [[Creating Urgency], [Personal Connection], [H...     opening   \n", "44  [[Personal Connection], [Call to Action], [Hum...     closing   \n", "45  [[Personal Engagement], [Relatability], [Demon...     opening   \n", "46  [[Clear Call to Action], [Personal Connection]...     closing   \n", "47  [[Creating Urgency], [Personal Connection], [H...     opening   \n", "\n", "                                              scripts  \n", "0                                                  []  \n", "1                                                  []  \n", "2                                                  []  \n", "3                                                  []  \n", "4                                                  []  \n", "5                                                  []  \n", "6                                                  []  \n", "7                                                  []  \n", "8                                                  []  \n", "9                                                  []  \n", "10                                                 []  \n", "11                                                 []  \n", "12                                                 []  \n", "13                                                 []  \n", "14                                                 []  \n", "15                                                 []  \n", "16                                                 []  \n", "17                                                 []  \n", "18                                                 []  \n", "19                                                 []  \n", "20                                                 []  \n", "21                                                 []  \n", "22                                                 []  \n", "23                                                 []  \n", "24  [[<PERSON><PERSON> kasih semua kerana men<PERSON> live say...  \n", "25  [[Hai semua! Selamat datang ke live kita hari ...  \n", "26  [[<PERSON><PERSON> kasih semua kerana menyertai sesi liv...  \n", "27  [[Hai semua! Selamat datang ke live stream kam...  \n", "28  [[Mga ka-beauty, maraming salamat sa pagsama s...  \n", "29  [[<PERSON><PERSON><PERSON><PERSON> araw, mga ka-beauty! Ka<PERSON>ta kayo?...  \n", "30  [[Mga ka-beauty, maraming salamat sa pagsama s...  \n", "31  [[<PERSON><PERSON><PERSON><PERSON> araw, mga ka-beauty! Nan<PERSON><PERSON> na na...  \n", "32  [[ขอบคุณทุกคนที่มาร่วมสนุกกับไลฟ์วันนี้นะคะ! น...  \n", "33  [[สวัสดีค่ะเพื่อนๆ ทุกคน! วันนี้เรามีผลิตภัณฑ์...  \n", "34  [[ขอบคุณทุกคนที่เข้ามาชมไลฟ์วันนี้นะคะ ถ้าคุณส...  \n", "35  [[สวัสดีค่ะทุกคน! วันนี้เรามีโปรโมชั่นพิเศษสำห...  \n", "36  [[Thank you all so much for joining me today! ...  \n", "37  [[Hey beautiful people! It's so great to see y...  \n", "38  [[Thank you all for joining our beauty live st...  \n", "39  [[Hey beauty lovers! Welcome to our exclusive ...  \n", "40  [[<PERSON><PERSON> kasih banyak sudah bergabung di live ...  \n", "41  [[Halo teman-teman cantik! Selamat datang di l...  \n", "42  [[<PERSON><PERSON> kasih sudah bergabung di live kita ha...  \n", "43  [[Halo semuanya! Selamat datang di live kita h...  \n", "44  [[<PERSON><PERSON><PERSON> ơn mọi người đã tham gia buổi livestream...  \n", "45  [[Xin chào các bạn yêu thích làm đẹp! Mình là ...  \n", "46  [[<PERSON><PERSON><PERSON> ơn các bạn đã tham gia buổi livestream h...  \n", "47  [[Xin chào mọi người! Hôm nay chúng ta có một ...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 获取总的分组数，用于进度条    \n", "total_groups = df.groupby(['industry', 'seller_type', 'live_gmv_percentile', 'script_type']).ngroups\n", "\n", "# 遍历每个国家\n", "for country_code in country_codes:\n", "    # 遍历每个分组，使用 tqdm 显示进度条\n", "    num_entry = 0\n", "    for (industry, seller_type, gmv_percentile_level, script_type), group in tqdm(df.groupby(['industry', 'seller_type', 'live_gmv_percentile', 'script_type']), total=total_groups, desc=f\"Processing groups for {country_code}\"):\n", "        \n", "        # 创建当前分组的 strategies 子集\n", "        strategies = group[['Strategy', 'Explanation', 'Examples']].rename(columns={'Strategy': 'name', 'Explanation': 'explanation', 'Examples': 'examples'}).to_dict(orient='records')\n", "        \n", "        # 生成 1-3 个 strategy 的所有组合\n", "        strategy_combinations = []\n", "        for r in range(1, 2):\n", "            combinations = itertools.combinations(strategies, r)\n", "            strategy_combinations.extend([list(comb) for comb in combinations])\n", "\n", "        strategies_list = []\n", "        scripts_list = []\n", "        \n", "\n", "        # 限制每个国家处理 5 个示例\n", "        for i, strategy_combination in enumerate(strategy_combinations):\n", "            # if i >= 5:  # 只处理前 5 个组合\n", "            #     break\n", "\n", "            if script_type == 'opening':\n", "                script, _, _, _ = agent.generate_opening_script(industry, seller_type, gmv_percentile_level, strategy_combination, country_code)\n", "            elif script_type == 'closing':\n", "                script, _, _, _ = agent.generate_closing_script(industry, seller_type, gmv_percentile_level, strategy_combination, country_code)\n", "\n", "            strategies_list.append([s['name'] for s in strategy_combination])\n", "            scripts_list.append(script['scripts'])\n", "        num_entry += 1\n", "        if num_entry >= 5:\n", "            break\n", "        script_texts.append({\n", "            \"country_code\": country_code,\n", "            \"industry\": industry,\n", "            \"seller_type\": seller_type,\n", "            \"gmv_percentile_level\": gmv_percentile_level,\n", "            \"strategies\": strategies_list,\n", "            \"script_type\": script_type,\n", "            \"scripts\": scripts_list\n", "        })\n", "    \n", "# 创建新的 DataFrame\n", "df_scripts = pd.DataFrame(script_texts)\n", "\n", "# 输出结果\n", "display(df_scripts)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["df_scripts.to_csv('./output/scripts_generation_SEA.csv', index=False)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}