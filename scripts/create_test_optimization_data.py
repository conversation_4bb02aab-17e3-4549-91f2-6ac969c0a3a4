import pandas as pd
import os

def create_test_optimization_data():
    """
    从原始数据中为每个国家筛选10条数据用于测试优化
    """
    # 读取原始数据
    input_file = './data/live_highlight_prompts_scripts_new.csv'
    output_file = './data/live_highlight_prompts_scripts_test_optimization.csv'
    
    print("正在读取原始数据...")
    df = pd.read_csv(input_file)
    print(f"读取到 {len(df)} 条原始记录")
    
    # 获取每个国家的数据分布
    country_counts = df['country'].value_counts()
    print(f"\n原始数据各国家分布:")
    for country, count in country_counts.items():
        print(f"  {country}: {count} 条记录")
    
    # 为每个国家随机选择10条数据
    print(f"\n开始为每个国家筛选10条数据...")
    test_df_list = []
    
    for country in sorted(country_counts.index):
        country_data = df[df['country'] == country]
        # 随机选择10条，如果不足10条则全选
        sample_size = min(10, len(country_data))
        sampled_data = country_data.sample(n=sample_size, random_state=42)
        test_df_list.append(sampled_data)
        print(f"  {country}: 筛选了 {len(sampled_data)} 条记录")
    
    # 合并所有国家的测试数据
    test_df = pd.concat(test_df_list, ignore_index=True)
    print(f"\n筛选完成！总共 {len(test_df)} 条测试记录")
    
    # 显示测试数据的国家分布
    test_country_counts = test_df['country'].value_counts()
    print(f"\n测试数据各国家分布:")
    for country, count in test_country_counts.items():
        print(f"  {country}: {count} 条记录")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 保存测试数据
    test_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"\n✓ 测试优化数据已保存到: {output_file}")
    
    # 显示一些样本数据信息
    print(f"\n样本数据预览:")
    print("=" * 80)
    for i, (idx, row) in enumerate(test_df.head(3).iterrows()):
        print(f"记录 {i+1}:")
        print(f"  国家: {row['country']}")
        print(f"  产品名称: {row['product_name'][:50]}...")
        print(f"  类别: {row['first_category_name']}")
        print(f"  ASR长度: {len(str(row['asr_info']))} 字符")
        print("-" * 40)
    
    return test_df

if __name__ == "__main__":
    print("=" * 60)
    print("创建测试优化数据集")
    print("=" * 60)
    
    test_data = create_test_optimization_data()
    
    print(f"\n✅ 完成！已为每个国家筛选10条数据，总共 {len(test_data)} 条记录用于测试优化。")
    print("\n接下来可以使用这些数据来:")
    print("1. 测试和优化 prompt")
    print("2. 验证生成脚本的质量")
    print("3. 调整参数设置")
    print("4. 进行小规模测试后再处理完整数据集")
