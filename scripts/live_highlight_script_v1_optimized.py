import pandas as pd
import json
import openai
from tqdm import tqdm
import os
from datetime import datetime


class OptimizedOpenAIAgent:
    def __init__(self, api_key, model="gpt-4o-2024-08-06"):
        self.client = openai.AzureOpenAI(
            azure_endpoint="https://search-va.byteintl.net/gpt/openapi/online/v2/crawl",
            api_version="2024-03-01-preview",
            api_key=api_key
        )
        self.model = model

    def system_message(self, message):
        return message

    def assistant_message(self, message):
        return message

    def user_message(self, message):
        return message

    @staticmethod
    def clean_json_output(output):
        output = output.strip()
        if output.startswith("```json"):
            output = output[7:]
        if output.endswith("```"):
            output = output[:-3]
        cleaned_output = output.strip()

        try:
            json_data = json.loads(cleaned_output)
        except json.JSONDecodeError as e:
            print(f"JSON decoding error: {e}")
            return cleaned_output

        def clean_json(data):
            if isinstance(data, dict):
                return {key: clean_json(value) for key, value in data.items()}
            elif isinstance(data, list):
                return [clean_json(item) for item in data]
            elif isinstance(data, str):
                return "" if data.lower() in ["unknown", "na", "null"] else data
            else:
                return data

        cleaned_json_data = clean_json(json_data)
        return cleaned_json_data

    def run_openai_task(self, system_message, assistant_message, user_message):
        messages = [
            {"role": "system", "content": self.system_message(system_message)},
            {"role": "assistant", "content": self.assistant_message(assistant_message)},
            {"role": "user", "content": self.user_message(user_message)}
        ]

        completion = self.client.chat.completions.create(
            extra_headers={"X-TT-LOGID": "abc"},
            model=self.model,
            messages=messages,
            temperature=0.3,  # 稍微增加创造性
            frequency_penalty=0.1,  # 减少重复
            presence_penalty=0.1
        )

        json_data = completion.model_dump_json()
        data_dict = json.loads(json_data)

        result = self.clean_json_output(data_dict['choices'][0]['message']['content'])
        usage_data = data_dict.get('usage', {})
        total_tokens = usage_data.get('total_tokens', 0)
        prompt_tokens = usage_data.get('prompt_tokens', 0)
        completion_tokens = usage_data.get('completion_tokens', 0)

        return result, total_tokens, prompt_tokens, completion_tokens

    def generate_highlight_script_v1_optimized(self, product_name, product_description, category, asr_info, country_code='ID'):
        
        # 语言和文化映射
        language_culture_mapping = {
            'ID': {
                'language': 'Indonesian',
                'culture_notes': 'Use friendly, enthusiastic tone. Include Islamic greetings when appropriate. Focus on family values and community.',
                'common_phrases': ['Assalamualaikum', 'Alhamdulillah', 'InsyaAllah', 'Subhanallah']
            },
            'VN': {
                'language': 'Vietnamese', 
                'culture_notes': 'Use respectful tone with proper honorifics. Emphasize quality and value for money.',
                'common_phrases': ['Xin chào', 'Cảm ơn', 'Tuyệt vời', 'Chất lượng cao']
            },
            'MY': {
                'language': 'Malay',
                'culture_notes': 'Mix of Malay, English, and Chinese influences. Use warm, friendly approach.',
                'common_phrases': ['Selamat', 'Terima kasih', 'Bagus', 'Murah']
            },
            'TH': {
                'language': 'Thai',
                'culture_notes': 'Use polite particles (ka/krub). Emphasize beauty and lifestyle benefits.',
                'common_phrases': ['Sawasdee ka/krub', 'Kob khun ka/krub', 'Suay mak', 'Dee mak']
            },
            'PH': {
                'language': 'Filipino',
                'culture_notes': 'Mix English and Filipino. Use energetic, family-oriented approach.',
                'common_phrases': ['Kumusta', 'Salamat', 'Ganda', 'Sulit']
            },
            'SG': {
                'language': 'English',
                'culture_notes': 'Professional yet friendly. Mix of cultures, emphasize convenience and quality.',
                'common_phrases': ['Hello', 'Thank you', 'Amazing', 'Great deal']
            }
        }
        
        culture_info = language_culture_mapping.get(country_code, language_culture_mapping['SG'])
        language = culture_info['language']
        
        system_message = """You are an expert TikTok live streaming script writer specializing in creating engaging, culturally-appropriate highlight scripts for Southeast Asian markets. Your scripts consistently drive high engagement and sales conversion rates."""

        assistant_message = f"""
        I'll create compelling TikTok live streaming highlight scripts based on your product information. Let me analyze the details:

        **PRODUCT ANALYSIS:**
        - Product: {product_name}
        - Category: {category}
        - Target Market: {country_code} ({language})
        - Cultural Context: {culture_info['culture_notes']}

        **SCRIPT CREATION STRATEGY:**

        I'll generate 4 distinct highlight scripts, each with a different approach:

        1. **URGENCY & SCARCITY SCRIPT**: Creates immediate action through limited-time offers
        2. **PRODUCT DEMO SCRIPT**: Showcases features and benefits with demonstration focus
        3. **SOCIAL PROOF SCRIPT**: Uses testimonials and community validation
        4. **VALUE PROPOSITION SCRIPT**: Emphasizes unique benefits and competitive advantages

        **CULTURAL ADAPTATION FOR {country_code}:**
        - Language: {language}
        - Cultural Notes: {culture_info['culture_notes']}
        - Local Phrases: {', '.join(culture_info['common_phrases'])}

        **SCRIPT REQUIREMENTS:**
        ✓ 30-60 seconds when spoken (150-300 words)
        ✓ Hook within first 3 seconds
        ✓ Clear call-to-action
        ✓ Culturally appropriate tone
        ✓ Reference existing ASR content when relevant
        ✓ Include specific product benefits
        ✓ Use local language naturally

        **OUTPUT FORMAT:**
        ```json
        {{
            "scripts": [
                {{
                    "title": "Descriptive Title",
                    "type": "Script Type (Urgency/Demo/Social Proof/Value)",
                    "content": "Complete script in {language}",
                    "key_elements": ["element1", "element2", "element3"],
                    "estimated_duration": "30-45 seconds"
                }}
            ]
        }}
        ```

        Now I'll create the scripts based on your specific product information.
        """

        user_message = f"""
        **PRODUCT INFORMATION:**
        Product Name: {product_name}
        Category: {category}
        Description: {product_description}

        **EXISTING ASR CONTENT:**
        {asr_info}

        **TARGET MARKET:**
        Country: {country_code}
        Language: {language}
        Cultural Context: {culture_info['culture_notes']}

        **TASK:**
        Create 4 engaging TikTok live streaming highlight scripts in {language} for the {country_code} market. Each script should:

        1. Be culturally appropriate and use natural {language} language
        2. Include a strong hook in the first 3 seconds
        3. Reference or improve upon the existing ASR content
        4. Have a clear call-to-action
        5. Be 30-60 seconds when spoken (150-300 words)
        6. Use one of these approaches: Urgency/Scarcity, Product Demo, Social Proof, or Value Proposition

        **IMPORTANT:**
        - Analyze the ASR content to understand the current selling approach
        - Improve upon weak points in the ASR content
        - Maintain authenticity while being persuasive
        - Use appropriate cultural phrases and tone for {country_code}

        Return the output in the exact JSON format specified above.
        """

        result, total_tokens, prompt_tokens, completion_tokens = self.run_openai_task(system_message, assistant_message, user_message)
        return result, total_tokens, prompt_tokens, completion_tokens


def run_prompt_optimization_test(version="v1.0", batch_size=50):
    """
    运行prompt优化测试

    Args:
        version: 版本号
        batch_size: 批处理大小，用于进度保存
    """
    print("=" * 80)
    print(f"直播高亮脚本生成 - Prompt优化测试 {version}")
    print("=" * 80)

    agent = OptimizedOpenAIAgent(api_key="X3zrNBdXZ1wQS1EnqMXP4bkFBBMELMPB")

    # 读取测试数据
    test_data_file = f'./output/prompt_optimization/{version}/test_data.csv'
    if not os.path.exists(test_data_file):
        print(f"❌ 测试数据文件不存在: {test_data_file}")
        print("请先运行 create_prompt_optimization_test_data.py 创建测试数据")
        return

    df = pd.read_csv(test_data_file)
    print(f"读取到 {len(df)} 个测试记录")

    # 显示数据分布
    country_counts = df['country'].value_counts()
    print(f"\n测试数据各国家分布:")
    for country, count in country_counts.items():
        print(f"  {country}: {count} 条记录")

    # 检查是否有进度文件（支持断点续传）
    output_dir = f'./output/prompt_optimization/{version}'
    progress_file = f'{output_dir}/batches/progress.json'
    results_file = f'{output_dir}/results.csv'

    start_index = 0
    results = []

    if os.path.exists(progress_file):
        with open(progress_file, 'r', encoding='utf-8') as f:
            progress_data = json.load(f)
            start_index = progress_data.get('last_completed_index', 0) + 1
            print(f"📁 发现进度文件，从第 {start_index + 1} 条记录继续")

        # 加载已有结果
        if os.path.exists(results_file):
            existing_df = pd.read_csv(results_file)
            results = existing_df.to_dict('records')
            print(f"📁 加载了 {len(results)} 条已完成的结果")

    # 处理剩余的记录
    for index in range(start_index, len(df)):
        row = df.iloc[index]

        try:
            highlight_vid = row['highlight_vid']
            product_id = row['product_id']
            product_name = row['product_name']
            product_desc = row['product_desc']
            category = row['first_category_name']
            asr_info = row['asr_info']
            country_code = row['country']

            print(f"\n处理记录 {index + 1}/{len(df)}: {product_name[:50]}... (国家: {country_code})")

            # 生成优化的直播高亮脚本
            scripts_result, total_tokens, prompt_tokens, completion_tokens = agent.generate_highlight_script_v1_optimized(
                product_name=product_name,
                product_description=product_desc,
                category=category,
                asr_info=asr_info,
                country_code=country_code
            )

            # 保存结果
            result_row = {
                'highlight_vid': highlight_vid,
                'product_id': product_id,
                'product_name': product_name,
                'category': category,
                'country_code': country_code,
                'scripts': scripts_result,
                'total_tokens': total_tokens,
                'prompt_tokens': prompt_tokens,
                'completion_tokens': completion_tokens,
                'version': version,
                'processed_at': datetime.now().isoformat()
            }
            results.append(result_row)

            print(f"✓ 成功生成脚本，使用 {total_tokens} tokens")

            # 每处理batch_size条记录保存一次进度
            if (index + 1) % batch_size == 0 or index == len(df) - 1:
                # 保存结果
                output_df = pd.DataFrame(results)
                output_df.to_csv(results_file, index=False, encoding='utf-8')

                # 保存进度
                progress_data = {
                    'last_completed_index': index,
                    'total_records': len(df),
                    'completed_records': len(results),
                    'last_saved_at': datetime.now().isoformat(),
                    'version': version
                }
                with open(progress_file, 'w', encoding='utf-8') as f:
                    json.dump(progress_data, f, indent=2, ensure_ascii=False)

                print(f"💾 进度已保存: {len(results)}/{len(df)} 条记录完成")

        except Exception as e:
            print(f"✗ 处理记录 {product_name} 时出错: {str(e)}")
            # 添加错误记录
            error_row = {
                'highlight_vid': row['highlight_vid'],
                'product_id': row['product_id'],
                'product_name': row['product_name'],
                'category': row['first_category_name'],
                'country_code': row['country'],
                'scripts': f"Error: {str(e)}",
                'total_tokens': 0,
                'prompt_tokens': 0,
                'completion_tokens': 0,
                'version': version,
                'processed_at': datetime.now().isoformat()
            }
            results.append(error_row)

    # 最终保存
    output_df = pd.DataFrame(results)
    output_df.to_csv(results_file, index=False, encoding='utf-8')
    print(f"\n✓ 最终结果已保存到: {results_file}")

    # 同时保存为 JSON 格式
    output_json_file = f'{output_dir}/results.json'
    with open(output_json_file, 'w', encoding='utf-8') as f:
        json.dump(output_df.to_dict('records'), f, indent=2, ensure_ascii=False)
    print(f"✓ JSON 格式结果已保存到: {output_json_file}")

    # 生成统计报告
    total_tokens_used = output_df['total_tokens'].sum()
    successful_generations = len(output_df[output_df['total_tokens'] > 0])
    country_stats = output_df['country_code'].value_counts()

    report = f"""
=== PROMPT优化测试报告 {version} ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总记录数: {len(df)}
成功生成: {successful_generations}
失败记录: {len(df) - successful_generations}
总计tokens: {total_tokens_used:,}
平均tokens: {total_tokens_used / len(df):.1f}

=== 国家分布 ===
{chr(10).join([f"{country}: {count} 个记录" for country, count in country_stats.items()])}

=== 下一步建议 ===
1. 检查生成的脚本质量和文化适应性
2. 分析各国家脚本的差异和特点
3. 评估相比原版本的改进效果
4. 准备v1.1版本的进一步优化
"""

    report_file = f'{output_dir}/test_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"✓ 测试报告已保存到: {report_file}")

    print(report)
    print(f"\n✅ Prompt优化测试 {version} 完成！")

    return output_df


if __name__ == "__main__":
    # 运行v1.0版本的prompt优化测试
    results = run_prompt_optimization_test(version="v1.0", batch_size=10)
    print("\n🎉 测试完成！可以开始分析结果质量了。")
