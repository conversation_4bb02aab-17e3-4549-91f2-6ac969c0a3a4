#!/usr/bin/env python3
"""
完成剩余国家的prompt优化测试
处理VN, TH, PH, SG各10条记录
"""

import pandas as pd
import json
import os
from run_v1_test_batch import OptimizedOpenAIAgent
from datetime import datetime
from tqdm import tqdm

def complete_remaining_countries():
    """
    完成剩余国家的测试
    """
    print("=" * 80)
    print("完成剩余国家的Prompt优化测试")
    print("=" * 80)
    
    # 读取测试数据
    test_data_file = './output/prompt_optimization/v1.0/test_data.csv'
    df = pd.read_csv(test_data_file)
    
    # 读取已有结果
    results_file = './output/prompt_optimization/v1.0/results.json'
    if os.path.exists(results_file):
        with open(results_file, 'r', encoding='utf-8') as f:
            existing_results = json.load(f)
        processed_countries = set(r['country_code'] for r in existing_results)
        print(f"已处理国家: {processed_countries}")
    else:
        existing_results = []
        processed_countries = set()
    
    # 筛选剩余需要处理的国家
    remaining_df = df[~df['country'].isin(processed_countries)]
    
    if len(remaining_df) == 0:
        print("✅ 所有国家都已处理完成！")
        return existing_results
    
    print(f"剩余需要处理: {len(remaining_df)} 条记录")
    
    # 显示剩余国家分布
    remaining_countries = remaining_df['country'].value_counts()
    print(f"\n剩余国家分布:")
    for country, count in remaining_countries.items():
        print(f"  {country}: {count} 条记录")
    
    # 初始化agent
    agent = OptimizedOpenAIAgent(api_key="X3zrNBdXZ1wQS1EnqMXP4bkFBBMELMPB")
    
    # 处理剩余记录
    new_results = []
    
    for index, row in tqdm(remaining_df.iterrows(), total=len(remaining_df), desc="处理剩余国家"):
        try:
            highlight_vid = row['highlight_vid']
            product_id = row['product_id']
            product_name = row['product_name']
            product_desc = row['product_desc']
            category = row['first_category_name']
            asr_info = row['asr_info']
            country_code = row['country']
            
            print(f"\n处理记录: {product_name[:50]}... (国家: {country_code})")
            
            # 生成优化的直播高亮脚本
            scripts_result, total_tokens, prompt_tokens, completion_tokens = agent.generate_highlight_script_v1_optimized(
                product_name=product_name,
                product_description=product_desc,
                category=category,
                asr_info=asr_info,
                country_code=country_code
            )
            
            # 保存结果
            result_row = {
                'highlight_vid': highlight_vid,
                'product_id': product_id,
                'product_name': product_name,
                'category': category,
                'country_code': country_code,
                'scripts': scripts_result,
                'total_tokens': total_tokens,
                'prompt_tokens': prompt_tokens,
                'completion_tokens': completion_tokens,
                'version': 'v1.0',
                'processed_at': datetime.now().isoformat()
            }
            new_results.append(result_row)
            
            print(f"✓ 成功生成脚本，使用 {total_tokens} tokens")
            
        except Exception as e:
            print(f"✗ 处理记录时出错: {str(e)}")
            # 添加错误记录
            error_row = {
                'highlight_vid': row['highlight_vid'],
                'product_id': row['product_id'],
                'product_name': row['product_name'],
                'category': row['first_category_name'],
                'country_code': row['country'],
                'scripts': f"Error: {str(e)}",
                'total_tokens': 0,
                'prompt_tokens': 0,
                'completion_tokens': 0,
                'version': 'v1.0',
                'processed_at': datetime.now().isoformat()
            }
            new_results.append(error_row)
    
    # 合并结果
    all_results = existing_results + new_results
    
    # 保存完整结果
    output_dir = './output/prompt_optimization/v1.0'
    
    # 保存CSV格式
    output_df = pd.DataFrame(all_results)
    results_csv_file = f'{output_dir}/results_complete.csv'
    output_df.to_csv(results_csv_file, index=False, encoding='utf-8')
    print(f"\n✓ 完整结果已保存到: {results_csv_file}")
    
    # 保存JSON格式
    results_json_file = f'{output_dir}/results_complete.json'
    with open(results_json_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    print(f"✓ JSON格式结果已保存到: {results_json_file}")
    
    # 生成完整统计报告
    total_tokens_used = sum(r['total_tokens'] for r in all_results)
    successful_generations = len([r for r in all_results if r['total_tokens'] > 0])
    country_stats = {}
    
    for result in all_results:
        country = result['country_code']
        if country not in country_stats:
            country_stats[country] = {'count': 0, 'successful': 0, 'total_tokens': 0}
        country_stats[country]['count'] += 1
        if result['total_tokens'] > 0:
            country_stats[country]['successful'] += 1
            country_stats[country]['total_tokens'] += result['total_tokens']
    
    report = f"""
=== 完整PROMPT优化测试报告 v1.0 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总记录数: {len(all_results)}
成功生成: {successful_generations}
失败记录: {len(all_results) - successful_generations}
成功率: {successful_generations/len(all_results)*100:.1f}%
总计tokens: {total_tokens_used:,}
平均tokens: {total_tokens_used/len(all_results):.1f}

=== 各国家统计 ===
{chr(10).join([f"{country}: {stats['count']} 条记录, 成功率 {stats['successful']/stats['count']*100:.1f}%, 平均tokens {stats['total_tokens']/max(stats['successful'],1):.1f}" for country, stats in country_stats.items()])}

=== 测试完成状态 ===
✅ 所有60条记录已处理完成
✅ 覆盖6个国家: ID, VN, MY, TH, PH, SG
✅ 每个国家10条测试记录
✅ 版本控制和结果分析已完成
"""
    
    complete_report_file = f'{output_dir}/complete_test_report.txt'
    with open(complete_report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    print(f"✓ 完整测试报告已保存到: {complete_report_file}")
    
    print(report)
    print(f"\n🎉 所有国家的prompt优化测试已完成！")
    
    return all_results

if __name__ == "__main__":
    complete_remaining_countries()
