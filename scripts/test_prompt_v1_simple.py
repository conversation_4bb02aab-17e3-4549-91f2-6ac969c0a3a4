import pandas as pd
import json
import openai
import os

# 简单测试脚本，只处理前3条记录
def test_optimized_prompt():
    print("=" * 60)
    print("测试优化的Prompt - 简化版本")
    print("=" * 60)
    
    # 读取测试数据
    test_data_file = './output/prompt_optimization/v1.0/test_data.csv'
    if not os.path.exists(test_data_file):
        print(f"❌ 测试数据文件不存在: {test_data_file}")
        return
    
    df = pd.read_csv(test_data_file)
    print(f"读取到 {len(df)} 个测试记录")
    
    # 只处理前3条记录进行测试
    test_df = df.head(3)
    print(f"测试前 {len(test_df)} 条记录")
    
    # 初始化OpenAI客户端
    client = openai.AzureOpenAI(
        azure_endpoint="https://search-va.byteintl.net/gpt/openapi/online/v2/crawl",
        api_version="2024-03-01-preview",
        api_key="X3zrNBdXZ1wQS1EnqMXP4bkFBBMELMPB"
    )
    
    results = []
    
    for index, row in test_df.iterrows():
        try:
            product_name = row['product_name']
            country_code = row['country']
            
            print(f"\n处理记录 {index + 1}: {product_name[:50]}... (国家: {country_code})")
            
            # 简化的prompt测试
            system_message = "You are an expert TikTok live streaming script writer."
            
            user_message = f"""
            Create 2 short TikTok live streaming scripts for:
            Product: {product_name}
            Country: {country_code}
            
            Return JSON format:
            {{
                "scripts": [
                    {{"title": "Script 1", "content": "Content 1"}},
                    {{"title": "Script 2", "content": "Content 2"}}
                ]
            }}
            """
            
            messages = [
                {"role": "system", "content": system_message},
                {"role": "user", "content": user_message}
            ]
            
            completion = client.chat.completions.create(
                extra_headers={"X-TT-LOGID": "abc"},
                model='gpt-4o-2024-08-06',
                messages=messages,
                temperature=0.3
            )
            
            result = completion.choices[0].message.content
            print(f"✓ 成功生成脚本")
            print(f"结果预览: {result[:100]}...")
            
            results.append({
                'product_name': product_name,
                'country_code': country_code,
                'scripts': result,
                'status': 'success'
            })
            
        except Exception as e:
            print(f"✗ 处理记录时出错: {str(e)}")
            results.append({
                'product_name': row['product_name'],
                'country_code': row['country'],
                'scripts': f"Error: {str(e)}",
                'status': 'error'
            })
    
    # 保存测试结果
    output_file = './output/prompt_optimization/v1.0/simple_test_results.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n✓ 简单测试结果已保存到: {output_file}")
    print(f"成功处理: {len([r for r in results if r['status'] == 'success'])}/{len(results)} 条记录")
    
    return results

if __name__ == "__main__":
    test_optimized_prompt()
