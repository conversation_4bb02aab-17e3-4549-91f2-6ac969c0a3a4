import pandas as pd
import json
import openai
from tqdm import tqdm
import os
from datetime import datetime


class OpenAIAgent:
    def __init__(self, api_key, model="gpt-4-0613"):
        self.client = openai.AzureOpenAI(
            azure_endpoint="https://search-va.byteintl.net/gpt/openapi/online/v2/crawl",
            api_version="2024-03-01-preview",
            api_key=api_key
        )
        self.model = model

    def system_message(self, message):
        return message

    def assistant_message(self, message):
        return message

    def user_message(self, message):
        return message

    @staticmethod
    def clean_json_output(output):
        output = output.strip()
        if output.startswith("```json"):
            output = output[7:]
        if output.endswith("```"):
            output = output[:-3]
        cleaned_output = output.strip()

        try:
            json_data = json.loads(cleaned_output)
        except json.JSONDecodeError as e:
            print(f"JSON decoding error: {e}")
            return cleaned_output

        def clean_json(data):
            if isinstance(data, dict):
                return {key: clean_json(value) for key, value in data.items()}
            elif isinstance(data, list):
                return [clean_json(item) for item in data]
            elif isinstance(data, str):
                return "" if data.lower() in ["unknown", "na", "null"] else data
            else:
                return data

        cleaned_json_data = clean_json(json_data)
        return cleaned_json_data

    def run_openai_task(self, system_message, assistant_message, user_message):
        messages = [
            {"role": "system", "content": self.system_message(system_message)},
            {"role": "assistant", "content": self.assistant_message(assistant_message)},
            {"role": "user", "content": self.user_message(user_message)}
        ]

        completion = self.client.chat.completions.create(
            extra_headers={"X-TT-LOGID": "abc"},  # 请务必带上此header，方便定位问题
            model=self.model,
            messages=messages,
            temperature=0,
            frequency_penalty=0,
            presence_penalty=0
        )

        json_data = completion.model_dump_json()
        data_dict = json.loads(json_data)

        result = self.clean_json_output(data_dict['choices'][0]['message']['content'])
        usage_data = data_dict.get('usage', {})
        total_tokens = usage_data.get('total_tokens', 0)
        prompt_tokens = usage_data.get('prompt_tokens', 0)
        completion_tokens = usage_data.get('completion_tokens', 0)

        return result, total_tokens, prompt_tokens, completion_tokens

    def generate_highlight_script(self, product_name, product_description, category, asr_info, country_code='ID'):
        
        # 根据 CSV 中的 country code 进行语言映射
        language_mapping = {
            'ID': 'Indonesian',
            'VN': 'Vietnamese', 
            'MY': 'Malay',
            'TH': 'Thai',
            'PH': 'Filipino',
            'SG': 'English'
        }
        
        language = language_mapping.get(country_code, 'English')
        
        system_message = "You are an assistant for a TikTok live streaming seller. Your task is to generate engaging highlight scripts for live streams based on product information and existing ASR (speech recognition) content."

        assistant_message = f"""
        Please generate highlight scripts based on the following information:

        - **Product Name**: {product_name}
        - **Category**: {category}
        - **Product Description**: {product_description}
        - **Existing ASR Content**: {asr_info}
        - **Target Language**: {language}
        - **Country**: {country_code}

        **Generate Highlight Scripts**:
        Please generate 3-5 highlight scripts that can be used for TikTok live streaming highlights. These scripts should:
        1. Be engaging and attention-grabbing
        2. Highlight key product features and benefits
        3. Include call-to-action elements
        4. Be suitable for the target market and language
        5. Reference or improve upon the existing ASR content when relevant

        The scripts should incorporate elements that work well for live streaming highlights, such as:
        - Product demonstrations
        - Special offers or promotions
        - Urgency and scarcity
        - Social proof
        - Clear calls to action

        Return the output in JSON format like this:
        {{
            "scripts": [
                {{
                    "title": "Script Title 1",
                    "content": "Script content 1"
                }},
                {{
                    "title": "Script Title 2", 
                    "content": "Script content 2"
                }},
                {{
                    "title": "Script Title 3",
                    "content": "Script content 3"
                }}
            ]
        }}
        """

        user_message = f"""
        Product Name: {product_name}
        Category: {category}
        Product Description: {product_description}
        Existing ASR Content: {asr_info}
        Target Language: {language}
        Country: {country_code}

        Please generate 3-5 engaging highlight scripts for TikTok live streaming in {language}. The scripts should be suitable for the {country_code} market and incorporate effective live streaming techniques.

        Return the output in JSON format like this:
        {{
            "scripts": [
                {{
                    "title": "Script Title 1",
                    "content": "Script content 1"
                }},
                {{
                    "title": "Script Title 2",
                    "content": "Script content 2"
                }},
                {{
                    "title": "Script Title 3", 
                    "content": "Script content 3"
                }}
            ]
        }}
        """

        result, total_tokens, prompt_tokens, completion_tokens = self.run_openai_task(system_message, assistant_message, user_message)
        return result, total_tokens, prompt_tokens, completion_tokens


def save_checkpoint(results, checkpoint_file, processed_count):
    """
    保存检查点文件
    """
    checkpoint_data = {
        'results': results,
        'processed_count': processed_count,
        'timestamp': datetime.now().isoformat()
    }

    with open(checkpoint_file, 'w', encoding='utf-8') as f:
        json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)

    print(f"✓ 检查点已保存: {checkpoint_file} (已处理 {processed_count} 条记录)")


def load_checkpoint(checkpoint_file):
    """
    加载检查点文件
    """
    if not os.path.exists(checkpoint_file):
        return None, 0

    try:
        with open(checkpoint_file, 'r', encoding='utf-8') as f:
            checkpoint_data = json.load(f)

        results = checkpoint_data.get('results', [])
        processed_count = checkpoint_data.get('processed_count', 0)
        timestamp = checkpoint_data.get('timestamp', 'Unknown')

        print(f"✓ 检查点已加载: {checkpoint_file}")
        print(f"  - 已处理记录: {processed_count}")
        print(f"  - 保存时间: {timestamp}")

        return results, processed_count

    except Exception as e:
        print(f"✗ 加载检查点失败: {e}")
        return None, 0


def save_intermediate_results(results, output_file, batch_num):
    """
    保存中间结果到 batches 文件夹
    """
    if not results:
        return

    # 确保 batches 文件夹存在
    batches_dir = './output/live_highlights/batches'
    os.makedirs(batches_dir, exist_ok=True)

    # 保存 CSV 格式到 batches 文件夹
    output_df = pd.DataFrame(results)
    filename_base = os.path.basename(output_file).replace('.csv', '')
    intermediate_csv = os.path.join(batches_dir, f'{filename_base}_batch_{batch_num}.csv')
    output_df.to_csv(intermediate_csv, index=False, encoding='utf-8')

    # 保存 JSON 格式到 batches 文件夹
    intermediate_json = os.path.join(batches_dir, f'{filename_base}_batch_{batch_num}.json')
    with open(intermediate_json, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"✓ 中间结果已保存: {intermediate_csv} 和 {intermediate_json}")


def get_processed_ids(results):
    """
    获取已处理的 highlight_vid 列表
    """
    return set(result['highlight_vid'] for result in results)


if __name__ == "__main__":
    agent = OpenAIAgent(api_key="X3zrNBdXZ1wQS1EnqMXP4bkFBBMELMPB", model='gpt-4o-2024-08-06')

    # 读取直播高亮数据
    df = pd.read_csv('./data/live_highlight_prompts_scripts_new.csv')
    print(f"读取到 {len(df)} 个直播高亮记录")

    # 配置参数
    BATCH_SIZE = 50  # 每50条记录保存一次
    checkpoint_file = './output/live_highlights/live_highlight_checkpoint.json'

    # 测试模式配置
    test_optimization_mode = False  # 设置为 True 进行测试优化（每国家10条）
    simple_test_mode = False  # 设置为 True 只处理前5条记录（测试用）

    if test_optimization_mode:
        # 获取每个国家的数据分布
        country_counts = df['country'].value_counts()
        print(f"\n原始数据各国家分布:")
        for country, count in country_counts.items():
            print(f"{country}: {count} 条记录")

        # 为每个国家随机选择10条数据
        test_df_list = []
        for country in country_counts.index:
            country_data = df[df['country'] == country]
            # 随机选择10条，如果不足10条则全选
            sample_size = min(10, len(country_data))
            sampled_data = country_data.sample(n=sample_size, random_state=42)
            test_df_list.append(sampled_data)

        # 合并所有国家的测试数据
        df = pd.concat(test_df_list, ignore_index=True)
        print(f"\n测试优化模式：每个国家筛选10条，总共 {len(df)} 条记录")

        # 显示测试数据的国家分布
        test_country_counts = df['country'].value_counts()
        print(f"测试数据各国家分布:")
        for country, count in test_country_counts.items():
            print(f"{country}: {count} 条记录")

    elif simple_test_mode:
        df = df.head(5)
        print(f"简单测试模式：只处理前 {len(df)} 条记录")

    # 尝试加载检查点
    print("\n=== 检查断点续传 ===")
    results, processed_count = load_checkpoint(checkpoint_file)
    if results is None:
        results = []
        processed_count = 0
        print("未找到检查点文件，从头开始处理")
    else:
        # 获取已处理的记录ID，避免重复处理
        processed_ids = get_processed_ids(results)
        print(f"已处理 {len(processed_ids)} 个记录，将跳过已处理的记录")

        # 过滤掉已处理的记录
        original_count = len(df)
        df = df[~df['highlight_vid'].isin(processed_ids)].reset_index(drop=True)
        print(f"原始记录: {original_count}, 剩余待处理记录: {len(df)} 个")
    
    # 处理记录
    batch_count = 0
    for index, row in tqdm(df.iterrows(), total=len(df), desc="生成直播高亮脚本"):
        try:
            highlight_vid = row['highlight_vid']
            product_id = row['product_id']
            product_name = row['product_name']
            product_desc = row['product_desc']
            category = row['first_category_name']
            asr_info = row['asr_info']
            country_code = row['country']

            current_total = processed_count + index + 1
            print(f"\n处理记录 {current_total}: {product_name[:50]}... (国家: {country_code})")

            # 生成直播高亮脚本
            scripts_result, total_tokens, prompt_tokens, completion_tokens = agent.generate_highlight_script(
                product_name=product_name,
                product_description=product_desc,
                category=category,
                asr_info=asr_info,
                country_code=country_code
            )

            # 保存结果
            result_row = {
                'highlight_vid': highlight_vid,
                'product_id': product_id,
                'product_name': product_name,
                'category': category,
                'country_code': country_code,
                'scripts': scripts_result,
                'total_tokens': total_tokens,
                'prompt_tokens': prompt_tokens,
                'completion_tokens': completion_tokens
            }
            results.append(result_row)

            print(f"✓ 成功生成脚本，使用 {total_tokens} tokens")

            # 每处理 BATCH_SIZE 条记录保存一次
            if (index + 1) % BATCH_SIZE == 0:
                batch_count += 1
                current_processed = processed_count + index + 1

                print(f"\n=== 批量保存 (第 {batch_count} 批) ===")

                # 保存检查点
                save_checkpoint(results, checkpoint_file, current_processed)

                # 保存中间结果
                if test_optimization_mode:
                    output_file = './output/live_highlights/live_highlight_scripts_test_optimization.csv'
                else:
                    output_file = './output/live_highlights/live_highlight_scripts.csv'

                save_intermediate_results(results, output_file, batch_count)

                print(f"已完成 {current_processed} 条记录的处理")

        except Exception as e:
            print(f"✗ 处理记录 {product_name} 时出错: {str(e)}")
            # 添加错误记录
            error_row = {
                'highlight_vid': row['highlight_vid'],
                'product_id': row['product_id'],
                'product_name': row['product_name'],
                'category': row['first_category_name'],
                'country_code': row['country'],
                'scripts': f"Error: {str(e)}",
                'total_tokens': 0,
                'prompt_tokens': 0,
                'completion_tokens': 0
            }
            results.append(error_row)

    # 处理完成后的最终保存
    final_processed = processed_count + len(df)
    print(f"\n=== 最终保存 ===")
    save_checkpoint(results, checkpoint_file, final_processed)
    
    # 保存最终结果到 output 目录
    output_df = pd.DataFrame(results)

    # 根据模式选择输出文件名
    if test_optimization_mode:
        output_file = './output/live_highlights/live_highlight_scripts_test_optimization.csv'
        output_json_file = './output/live_highlights/live_highlight_scripts_test_optimization.json'
    else:
        output_file = './output/live_highlights/live_highlight_scripts.csv'
        output_json_file = './output/live_highlights/live_highlight_scripts.json'

    output_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"✓ 最终结果已保存到: {output_file}")

    # 同时保存为 JSON 格式以便查看脚本内容
    with open(output_json_file, 'w', encoding='utf-8') as f:
        json.dump(output_df.to_dict('records'), f, indent=2, ensure_ascii=False)
    print(f"✓ JSON 格式结果已保存到: {output_json_file}")

    # 清理检查点文件（可选）
    if os.path.exists(checkpoint_file):
        try:
            os.remove(checkpoint_file)
            print(f"✓ 检查点文件已清理: {checkpoint_file}")
        except:
            print(f"⚠️ 无法删除检查点文件: {checkpoint_file}")

    # 打印统计信息
    total_tokens_used = output_df['total_tokens'].sum()
    successful_generations = len(output_df[output_df['total_tokens'] > 0])
    country_stats = output_df['country_code'].value_counts()

    print(f"\n=== 最终统计信息 ===")
    print(f"本次处理记录数: {len(df)}")
    print(f"总处理记录数: {len(results)}")
    print(f"成功生成脚本: {successful_generations}")
    print(f"总计使用 tokens: {total_tokens_used}")
    if len(results) > 0:
        print(f"平均每个记录使用 tokens: {total_tokens_used / len(results):.1f}")

    print(f"\n=== 国家分布 ===")
    for country, count in country_stats.items():
        print(f"{country}: {count} 个记录")

    print(f"\n✅ 所有任务完成！")
    print(f"💡 提示：如需继续处理更多数据，可直接重新运行脚本，将自动从断点继续")
