import pandas as pd
import os
from datetime import datetime

def create_prompt_optimization_test_data(version="v1.0", samples_per_country=10):
    """
    为prompt优化创建测试数据，每个国家选择指定数量的样本
    
    Args:
        version: 版本号，如 "v1.0"
        samples_per_country: 每个国家的样本数量，默认10条
    """
    print("=" * 80)
    print(f"创建Prompt优化测试数据 - {version}")
    print("=" * 80)
    
    # 读取原始数据
    input_file = './data/live_highlight_prompts_scripts_new.csv'
    output_dir = f'./output/prompt_optimization/{version}'
    output_file = f'{output_dir}/test_data.csv'
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(f'{output_dir}/batches', exist_ok=True)
    
    print(f"正在读取原始数据: {input_file}")
    df = pd.read_csv(input_file)
    print(f"读取到 {len(df)} 条原始记录")
    
    # 获取每个国家的数据分布
    country_counts = df['country'].value_counts()
    print(f"\n原始数据各国家分布:")
    for country, count in country_counts.items():
        print(f"  {country}: {count} 条记录")
    
    # 为每个国家选择指定数量的数据
    print(f"\n开始为每个国家筛选{samples_per_country}条数据...")
    test_df_list = []
    
    for country in sorted(country_counts.index):
        country_data = df[df['country'] == country]
        # 随机选择指定数量，如果不足则全选
        sample_size = min(samples_per_country, len(country_data))
        sampled_data = country_data.sample(n=sample_size, random_state=42)
        test_df_list.append(sampled_data)
        print(f"  {country}: 筛选了 {len(sampled_data)} 条记录")
    
    # 合并所有国家的测试数据
    test_df = pd.concat(test_df_list, ignore_index=True)
    print(f"\n筛选完成！总共 {len(test_df)} 条测试记录")
    
    # 显示测试数据的国家分布
    test_country_counts = test_df['country'].value_counts()
    print(f"\n测试数据各国家分布:")
    for country, count in test_country_counts.items():
        print(f"  {country}: {count} 条记录")
    
    # 保存测试数据
    test_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"\n✓ 测试数据已保存到: {output_file}")
    
    # 创建版本信息文件
    version_info = {
        'version': version,
        'created_at': datetime.now().isoformat(),
        'total_records': len(test_df),
        'samples_per_country': samples_per_country,
        'countries': test_country_counts.to_dict(),
        'source_file': input_file,
        'description': f'Prompt optimization test data {version} with {samples_per_country} samples per country'
    }
    
    import json
    version_info_file = f'{output_dir}/version_info.json'
    with open(version_info_file, 'w', encoding='utf-8') as f:
        json.dump(version_info, f, indent=2, ensure_ascii=False)
    print(f"✓ 版本信息已保存到: {version_info_file}")
    
    # 显示一些样本数据信息
    print(f"\n样本数据预览:")
    print("=" * 80)
    for i, (idx, row) in enumerate(test_df.head(3).iterrows()):
        print(f"记录 {i+1}:")
        print(f"  国家: {row['country']}")
        print(f"  产品名称: {row['product_name'][:50]}...")
        print(f"  类别: {row['first_category_name']}")
        print(f"  ASR长度: {len(str(row['asr_info']))} 字符")
        print("-" * 40)
    
    return test_df

if __name__ == "__main__":
    # 创建v1.0版本的测试数据
    test_data = create_prompt_optimization_test_data(version="v1.0", samples_per_country=10)
    
    print(f"\n✅ 完成！已为每个国家筛选10条数据，总共 {len(test_data)} 条记录用于prompt优化测试。")
    print("\n接下来可以:")
    print("1. 设计优化的prompt版本")
    print("2. 运行批处理测试脚本")
    print("3. 分析生成结果的质量")
    print("4. 迭代优化prompt设计")
