import pandas as pd
import json
import openai
from tqdm import tqdm


class OpenAIAgent:
    def __init__(self, api_key, model="gpt-4-0613"):
        self.client = openai.AzureOpenAI(
            azure_endpoint="https://search-va.byteintl.net/gpt/openapi/online/v2/crawl",
            api_version="2024-03-01-preview",
            api_key=api_key
        )
        self.model = model

    def system_message(self, message):
        return message

    def assistant_message(self, message):
        return message

    def user_message(self, message):
        return message

    @staticmethod
    def clean_json_output(output):
        output = output.strip()
        if output.startswith("```json"):
            output = output[7:]
        if output.endswith("```"):
            output = output[:-3]
        cleaned_output = output.strip()

        try:
            json_data = json.loads(cleaned_output)
        except json.JSONDecodeError as e:
            print(f"JSON decoding error: {e}")
            return cleaned_output

        def clean_json(data):
            if isinstance(data, dict):
                return {key: clean_json(value) for key, value in data.items()}
            elif isinstance(data, list):
                return [clean_json(item) for item in data]
            elif isinstance(data, str):
                return "" if data.lower() in ["unknown", "na", "null"] else data
            else:
                return data

        cleaned_json_data = clean_json(json_data)
        return cleaned_json_data

    def run_openai_task(self, system_message, user_message):
        messages = [
            {"role": "system", "content": self.system_message(system_message)},
            # {"role": "assistant", "content": self.assistant_message(assistant_message)},
            {"role": "user", "content": self.user_message(user_message)}
        ]

        completion = self.client.chat.completions.create(
            extra_headers={"X-TT-LOGID": "abc"},  # 请务必带上此header，方便定位问题
            model=self.model,
            messages=messages,
            temperature=0,
            frequency_penalty=0,
            presence_penalty=0
        )

        json_data = completion.model_dump_json()
        data_dict = json.loads(json_data)

        result = self.clean_json_output(data_dict['choices'][0]['message']['content'])
        usage_data = data_dict.get('usage', {})
        total_tokens = usage_data.get('total_tokens', 0)
        prompt_tokens = usage_data.get('prompt_tokens', 0)
        completion_tokens = usage_data.get('completion_tokens', 0)

        return result, total_tokens, prompt_tokens, completion_tokens

    def generate_opening_script(self, industry, attribute, gmv_percentile, strategies, country_code = 'US'):

        language_mapping = {
            'US': 'English',
            'MY': 'Malay',
            'PH': 'Filipino',
            'TH': 'Thai',
            'SG': 'English',
            'ID': 'Indonesian',
            'VN': 'Vietnamese'
        }
        system_message = "You are an assistant for a TikTok seller. Your task is to generate engaging opening scripts for live streams based on given strategies."

        strategy_explanations = "\n".join([f"1. **{strategy['name']}**: {strategy['explanation']}" for strategy in strategies])
        strategy_names = ", ".join([strategy['name'] for strategy in strategies])
        examples = "\n".join([f"**Examples for {strategy['name']}**: {strategy['examples']}" for strategy in strategies])

        assistant_message = f"""
        Please generate opening scripts based on the following information:

        - **Industry**: {industry}
        - **Attribute**: {attribute}
        - **GMV Percentile**: {gmv_percentile}
        - **Top Strategies**: {strategy_names}

        **Strategy Explanations**:
        {strategy_explanations}

        {examples}

        **Generate Opening Scripts**:
        Please generate 3-5 opening scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies.

        Return the output in JSON format like this:
        {{
            "scripts": [
                "Script 1",
                "Script 2",
                "Script 3"
            ]
        }}
        """

        user_message = f"""
        Industry: {industry}
        Attribute: {attribute}
        GMV Percentile: {gmv_percentile}
        Top Strategies: {strategy_names}

        Please generate 3-5 opening scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies. Please use {language_mapping[country_code]} to generate the scripts.

        Return the output in JSON format like this:
        {{
            "scripts": [
                "Script 1",
                "Script 2",
                "Script 3"
            ]
        }}
        """

        result, total_tokens, prompt_tokens, completion_tokens = self.run_openai_task(system_message, assistant_message, user_message)
        return result, total_tokens, prompt_tokens, completion_tokens

    def generate_closing_script(self, industry, attribute, gmv_percentile, strategies, country_code = 'US'):

        language_mapping = {
            'US': 'English',
            'MY': 'Malay',
            'PH': 'Filipino',
            'TH': 'Thai',
            'SG': 'English',
            'ID': 'Indonesian',
            'VN': 'Vietnamese'
        }
        
        system_message = "You are an assistant for a TikTok seller. Your task is to generate engaging closing scripts for live streams based on given strategies."

        strategy_explanations = "\n".join([f"1. **{strategy['name']}**: {strategy['explanation']}" for strategy in strategies])
        strategy_names = ", ".join([strategy['name'] for strategy in strategies])
        examples = "\n".join([f"**Examples for {strategy['name']}**: {strategy['examples']}" for strategy in strategies])

        assistant_message = f"""
        Please generate closing scripts based on the following information:

        - **Industry**: {industry}
        - **Attribute**: {attribute}
        - **GMV Percentile**: {gmv_percentile}
        - **Top Strategies**: {strategy_names}

        **Strategy Explanations**:
        {strategy_explanations}

        {examples}

        **Generate Closing Scripts**:
        Please generate 3-5 closing scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies.

        Return the output in JSON format like this:
        {{
            "scripts": [
                "Script 1",
                "Script 2",
                "Script 3"
            ]
        }}
        """

        user_message = f"""
        Industry: {industry}
        Attribute: {attribute}
        GMV Percentile: {gmv_percentile}
        Top Strategies: {strategy_names}

        Please generate 3-5 closing scripts referencing the above strategies, suitable for an affiliate live streaming in the {industry} industry. The scripts should incorporate elements from the specified strategies. Please use {language_mapping[country_code]} to generate the scripts.

        Return the output in JSON format like this:
        {{
            "scripts": [
                "Script 1",
                "Script 2",
                "Script 3"
            ]
        }}
        """

        result, total_tokens, prompt_tokens, completion_tokens = self.run_openai_task(system_message, assistant_message, user_message)
        return result, total_tokens, prompt_tokens, completion_tokens

    def generate_product_script(self, industry, product_name, product_description, language, max_count=5):
        system_message = "You are a helpful assistant specialized in creating engaging product scripts for TikTok sellers."
        
        user_message = f"""As a TikTok seller assistant, generate an engaging product script for live streaming based on the following information:
        - **Industry**: {industry}
        - **Product Name**: {product_name}
        - **Product Description**: \n{product_description}

        The script should use these specific labels:

        <Demonstrating Product Usage>
        <Highlighting Product Features>
        <Detailed Product Description>
        <Addressing Pain Points>
        <Personal Testimony>

        Important requirements:
        1. Generate the script in {language} language
        2. Do not include any emojis or emoticons
        3. Generate a total of {max_count} script sections
        4. You can use the same label multiple times with different content
        5. Distribute the {max_count} scripts across the labels based on what's most appropriate for this product
        6. Only use information provided in the product description - do not add false information
        7. Return ONLY a valid JSON object in this exact format:
        {{
            "scripts": [
                {{
                    "title": "label",
                    "text": "script"
                }},
                {{
                    "title": "label",
                    "text": "script"
                }},
                ...
            ]
        }}
        
        Do not include any explanations, notes, or additional text outside the JSON object."""
        result, total_tokens, prompt_tokens, completion_tokens = self.run_openai_task(system_message, user_message)

        return result, total_tokens, prompt_tokens, completion_tokens

if __name__ == "__main__":
    agent = OpenAIAgent(api_key="X3zrNBdXZ1wQS1EnqMXP4bkFBBMELMPB", model = 'gpt-4o-2024-08-06')

    # 读取产品演示数据
    df = pd.read_csv('./data/product_demo.csv')
    print(f"读取到 {len(df)} 个产品")

    # 准备存储结果的列表
    results = []

    # 语言映射
    language_mapping = {
        'Food & Beverages': 'English',
        'Beauty & Personal Care': 'English'
    }

    for index, row in tqdm(df.iterrows(), total=len(df), desc="生成产品脚本"):
        try:
            product_id = row['product_id']
            product_name = row['product_name']
            product_desc = row['product_desc']
            industry = row['first_category_name']

            # 根据行业确定语言
            language = language_mapping.get(industry, 'English')

            print(f"\n处理产品 {index + 1}/{len(df)}: {product_name[:50]}...")

            # 生成产品脚本
            scripts_result, total_tokens, prompt_tokens, completion_tokens = agent.generate_product_script(
                industry=industry,
                product_name=product_name,
                product_description=product_desc,
                language=language,
                max_count=5
            )

            # 保存结果
            result_row = {
                'product_id': product_id,
                'product_name': product_name,
                'industry': industry,
                'language': language,
                'scripts': scripts_result,
                'total_tokens': total_tokens,
                'prompt_tokens': prompt_tokens,
                'completion_tokens': completion_tokens
            }
            results.append(result_row)

            print(f"✓ 成功生成脚本，使用 {total_tokens} tokens")

        except Exception as e:
            print(f"✗ 处理产品 {product_name} 时出错: {str(e)}")
            # 添加错误记录
            error_row = {
                'product_id': row['product_id'],
                'product_name': row['product_name'],
                'industry': row['first_category_name'],
                'language': 'N/A',
                'scripts': f"Error: {str(e)}",
                'total_tokens': 0,
                'prompt_tokens': 0,
                'completion_tokens': 0
            }
            results.append(error_row)

    # 保存结果到 output 目录
    output_df = pd.DataFrame(results)
    output_file = './output/product_demo_scripts.csv'
    output_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"\n✓ 结果已保存到: {output_file}")

    # 同时保存为 JSON 格式以便查看脚本内容
    output_json_file = './output/product_demo_scripts.json'
    import json
    with open(output_json_file, 'w', encoding='utf-8') as f:
        json.dump(output_df.to_dict('records'), f, indent=2, ensure_ascii=False)
    print(f"✓ JSON 格式结果已保存到: {output_json_file}")

    # 打印统计信息
    total_tokens_used = output_df['total_tokens'].sum()
    successful_generations = len(output_df[output_df['total_tokens'] > 0])
    print(f"\n=== 统计信息 ===")
    print(f"总产品数: {len(df)}")
    print(f"成功生成脚本: {successful_generations}")
    print(f"总计使用 tokens: {total_tokens_used}")
    print(f"平均每个产品使用 tokens: {total_tokens_used / len(df):.1f}")
