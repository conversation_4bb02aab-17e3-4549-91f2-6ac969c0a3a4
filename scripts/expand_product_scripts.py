import pandas as pd
import json
import ast
import os

def expand_product_scripts(input_file='./output/product_demo_scripts.csv', 
                          output_file='./output/product_demo_scripts_expanded.csv'):
    """
    将产品脚本从紧凑格式展开为每个脚本一行的格式
    
    Args:
        input_file (str): 输入的 CSV 文件路径
        output_file (str): 输出的展开版 CSV 文件路径
    """
    
    print(f"正在读取文件: {input_file}")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False
    
    # 读取现有的结果文件
    df = pd.read_csv(input_file)
    print(f"读取到 {len(df)} 个产品记录")
    
    # 创建一个新的数据结构来存储展开的脚本
    expanded_data = []
    
    for index, row in df.iterrows():
        try:
            # 解析脚本字符串
            scripts_str = row['scripts']
            scripts_dict = ast.literal_eval(scripts_str)
            scripts_list = scripts_dict['scripts']
            
            print(f"处理产品 {index + 1}/{len(df)}: {row['product_name'][:50]}...")
            
            # 为每个脚本创建一行
            for script in scripts_list:
                expanded_row = {
                    'product_id': row['product_id'],
                    'product_name': row['product_name'],
                    'industry': row['industry'],
                    'language': row['language'],
                    'script_type': script['title'],
                    'script_content': script['text'],
                    'total_tokens': row['total_tokens'],
                    'prompt_tokens': row['prompt_tokens'],
                    'completion_tokens': row['completion_tokens']
                }
                expanded_data.append(expanded_row)
                
        except Exception as e:
            print(f'✗ 处理产品 {row["product_name"]} 时出错: {e}')
            continue
    
    # 创建新的 DataFrame
    expanded_df = pd.DataFrame(expanded_data)
    
    # 保存为 CSV
    expanded_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f'✓ 展开的脚本已保存到: {output_file}')
    print(f'总共生成了 {len(expanded_df)} 个脚本条目')
    print(f'涵盖 {expanded_df["product_id"].nunique()} 个产品')
    
    # 显示脚本类型统计
    script_type_counts = expanded_df['script_type'].value_counts()
    print('\n=== 脚本类型统计 ===')
    for script_type, count in script_type_counts.items():
        print(f"{script_type}: {count} 个")
    
    # 显示行业分布
    industry_counts = expanded_df['industry'].value_counts()
    print('\n=== 行业分布 ===')
    for industry, count in industry_counts.items():
        print(f"{industry}: {count} 个脚本")
    
    # 显示前几行作为预览
    print('\n=== 文件预览 (前3行) ===')
    preview_df = expanded_df.head(3)[['product_name', 'script_type', 'script_content']]
    for idx, row in preview_df.iterrows():
        print(f"\n产品: {row['product_name'][:60]}...")
        print(f"类型: {row['script_type']}")
        print(f"内容: {row['script_content'][:100]}...")
    
    return True

def create_summary_report(expanded_file='./output/product_demo_scripts_expanded.csv'):
    """
    创建脚本生成的汇总报告
    
    Args:
        expanded_file (str): 展开版 CSV 文件路径
    """
    
    if not os.path.exists(expanded_file):
        print(f"错误: 文件 {expanded_file} 不存在")
        return
    
    df = pd.read_csv(expanded_file)
    
    # 创建汇总报告
    report = []
    report.append("# 产品脚本生成汇总报告\n")
    report.append(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    report.append(f"总脚本数: {len(df)}")
    report.append(f"总产品数: {df['product_id'].nunique()}")
    report.append(f"总 tokens 使用: {df.groupby('product_id')['total_tokens'].first().sum()}")
    report.append(f"平均每产品 tokens: {df.groupby('product_id')['total_tokens'].first().mean():.1f}\n")
    
    # 按行业统计
    report.append("## 按行业统计")
    industry_stats = df.groupby('industry').agg({
        'product_id': 'nunique',
        'script_content': 'count'
    }).rename(columns={'product_id': '产品数', 'script_content': '脚本数'})
    
    for industry, stats in industry_stats.iterrows():
        report.append(f"- {industry}: {stats['产品数']} 个产品, {stats['脚本数']} 个脚本")
    
    # 按脚本类型统计
    report.append("\n## 按脚本类型统计")
    script_type_counts = df['script_type'].value_counts()
    for script_type, count in script_type_counts.items():
        report.append(f"- {script_type}: {count} 个")
    
    # 保存报告
    report_file = './output/product_scripts_summary_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print(f"✓ 汇总报告已保存到: {report_file}")

if __name__ == "__main__":
    print("=== 产品脚本展开工具 ===\n")
    
    # 展开脚本
    success = expand_product_scripts()
    
    if success:
        # 创建汇总报告
        create_summary_report()
        print("\n✅ 所有任务完成！")
    else:
        print("\n❌ 任务执行失败")
