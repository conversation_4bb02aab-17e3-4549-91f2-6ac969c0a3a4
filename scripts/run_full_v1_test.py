#!/usr/bin/env python3
"""
运行完整的v1.0 prompt优化测试 - 处理所有60条记录
"""

from run_v1_test_batch import run_v1_optimization_test

if __name__ == "__main__":
    print("开始运行完整的v1.0 prompt优化测试...")
    print("将处理所有60条记录（每个国家10条）")
    print("=" * 80)
    
    # 运行完整测试，处理所有记录
    results = run_v1_optimization_test(max_records=None)
    
    print("\n" + "=" * 80)
    print("🎉 完整测试完成！")
    print("现在可以:")
    print("1. 查看生成的脚本质量")
    print("2. 分析各国家的文化适应性")
    print("3. 评估相比原版本的改进")
    print("4. 准备下一轮优化")
