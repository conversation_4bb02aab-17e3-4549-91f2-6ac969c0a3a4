import pandas as pd
import numpy as np

def analyze_test_data():
    """
    分析测试优化数据的详细信息
    """
    # 读取测试数据
    df = pd.read_csv('./data/live_highlight_prompts_scripts_test_optimization.csv')
    
    print("=" * 80)
    print("测试优化数据分析报告")
    print("=" * 80)
    
    print(f"总记录数: {len(df)}")
    print(f"总列数: {len(df.columns)}")
    print(f"列名: {list(df.columns)}")
    
    # 国家分布
    print(f"\n=== 国家分布 ===")
    country_counts = df['country'].value_counts()
    for country, count in country_counts.items():
        print(f"{country}: {count} 条记录")
    
    # 类别分布
    print(f"\n=== 产品类别分布 ===")
    category_counts = df['first_category_name'].value_counts()
    for category, count in category_counts.head(10).items():
        print(f"{category}: {count} 条记录")
    
    # ASR信息长度分析
    print(f"\n=== ASR信息长度分析 ===")
    df['asr_length'] = df['asr_info'].astype(str).str.len()
    print(f"ASR平均长度: {df['asr_length'].mean():.1f} 字符")
    print(f"ASR最短长度: {df['asr_length'].min()} 字符")
    print(f"ASR最长长度: {df['asr_length'].max()} 字符")
    print(f"ASR中位数长度: {df['asr_length'].median():.1f} 字符")
    
    # 产品名称长度分析
    print(f"\n=== 产品名称长度分析 ===")
    df['product_name_length'] = df['product_name'].astype(str).str.len()
    print(f"产品名称平均长度: {df['product_name_length'].mean():.1f} 字符")
    print(f"产品名称最短长度: {df['product_name_length'].min()} 字符")
    print(f"产品名称最长长度: {df['product_name_length'].max()} 字符")
    
    # 价格分析
    print(f"\n=== 价格分析 ===")
    df['sale_price'] = pd.to_numeric(df['sale_price'], errors='coerce')
    print(f"平均价格: {df['sale_price'].mean():.2f}")
    print(f"最低价格: {df['sale_price'].min():.2f}")
    print(f"最高价格: {df['sale_price'].max():.2f}")
    print(f"价格中位数: {df['sale_price'].median():.2f}")
    
    # 按国家显示样本数据
    print(f"\n=== 各国家样本数据预览 ===")
    for country in sorted(df['country'].unique()):
        country_data = df[df['country'] == country]
        print(f"\n{country} 国家样本:")
        sample = country_data.iloc[0]
        print(f"  产品名称: {sample['product_name'][:60]}...")
        print(f"  类别: {sample['first_category_name']}")
        print(f"  价格: {sample['sale_price']}")
        print(f"  ASR长度: {len(str(sample['asr_info']))} 字符")
        print(f"  ASR开头: {str(sample['asr_info'])[:100]}...")
    
    # 保存分析结果
    analysis_results = {
        'total_records': len(df),
        'countries': country_counts.to_dict(),
        'categories': category_counts.head(10).to_dict(),
        'asr_stats': {
            'mean_length': df['asr_length'].mean(),
            'min_length': df['asr_length'].min(),
            'max_length': df['asr_length'].max(),
            'median_length': df['asr_length'].median()
        },
        'price_stats': {
            'mean_price': df['sale_price'].mean(),
            'min_price': df['sale_price'].min(),
            'max_price': df['sale_price'].max(),
            'median_price': df['sale_price'].median()
        }
    }
    
    import json
    with open('./output/test_data_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n✓ 分析结果已保存到: ./output/test_data_analysis.json")
    
    return df

if __name__ == "__main__":
    analyze_test_data()
